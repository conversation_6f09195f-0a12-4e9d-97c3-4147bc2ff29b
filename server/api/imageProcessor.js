import sharp from 'sharp';
import { createClient } from '@supabase/supabase-js';
import express from 'express';
import fileUpload from 'express-fileupload';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const app = express();
app.use(
    fileUpload({
        limits: { fileSize: 50 * 1024 * 1024 },
        useTempFiles: true,
        tempFileDir: '/tmp/',
        parseNested: true
    })
);
app.use(express.json()); // 用于解析metadata字段

//处理请求，把图片文件处理成缩略图，存储原图和缩略图并返回他们的地址信息
export default async function handler(req, res) {
    try {
        console.log('===== 图片处理API开始处理请求 =====');
        console.log('环境变量检查:', {
            SUPABASE_URL: process.env.VITE_SUPABASE_URL ? '已设置' : '未设置',
            SUPABASE_KEY: process.env.VITE_SUPABASE_KEY ? '已设置' : '未设置'
        });

        // 验证请求格式
        if (!req.files || !req.files.metadata) {
            console.log('请求格式错误: 缺少文件或元数据');
            return res.status(400).json({
                success: false,
                error: '请求格式错误，需要同时包含文件和元数据'
            });
        }

        // 验证文件有效性（新增详细检查）
        if (!req.files?.file) {
            console.log('请求错误: 未接收到文件');
            return res.status(400).json({ error: '未接收到文件' });
        }

        const file = req.files.file;
        const metadataJson = req.files.metadata.data.toString('utf8');

        // 解析元数据
        let metadata;
        try {
            metadata = JSON.parse(metadataJson);
        } catch (e) {
            console.log('元数据解析错误:', e);
            return res.status(400).json({ error: '无效的元数据格式' });
        }

        console.log('接收文件详情:', {
            name: file.name,
            mimetype: file.mimetype,
            size: file.size,
            dataType: typeof file.data
        });

        console.log('解析后的元数据:', metadata);

        // 生成安全的文件名 - 使用时间戳和随机字符串替代原始文件名
        const timestamp = Date.now();
        const randomStr = Math.random().toString(36).substring(2, 8);
        // 获取文件名（不包含后缀）和后缀
        const fileName = file.name.split('.').slice(0, -1).join('_');
        const fileExtension = file.name.split('.').pop();
        const safeFileName = `${fileName}-${timestamp}-${randomStr}.${fileExtension}`;

        // 使用安全文件名创建路径
        const originalPath = `original/${safeFileName}`;
        const thumbnailPath = `thumbnail/${safeFileName}`;

        // 验证文件内容
        if (!file.data || file.data.length === 0) {
            console.log('文件内容为空');
            return res.status(400).json({ error: '接收到的文件内容为空' });
        }

        // 转换Buffer的正确方式
        const originalBuffer = Buffer.isBuffer(file.data) ? file.data : Buffer.from(file.data);

        // 验证Buffer有效性
        if (originalBuffer.length === 0) {
            console.log('Buffer转换失败');
            return res.status(400).json({ error: '文件Buffer转换失败' });
        }

        // // 验证必要字段
        // const requiredFields = ['name', 'photo_url', 'thumbnail_url', 'gcj', 'custom_gcj'];
        // const missing = requiredFields.filter((field) => !metadata[field]);
        // if (missing.length > 0) {
        //     console.log('缺少必要字段:', missing);
        //     return res.status(400).json({
        //         success: false,
        //         error: `缺少必要字段: ${missing.join(', ')}`
        //     });
        // }

        // 生成缩略图时添加验证
        const processImage = async (buffer) => {
            try {
                const pipeline = sharp(buffer);
                const metadata = await pipeline.metadata();

                if (!metadata.width || !metadata.height) {
                    throw new Error('无法读取图片元数据');
                }

                return pipeline.resize({ width: Math.round(metadata.width * 0.05) }).toBuffer();
            } catch (error) {
                console.error('图片处理失败:', error);
                throw new Error('无效的图片文件');
            }
        };

        // 使用方式
        console.log('开始生成缩略图...');
        const thumbnail = await processImage(originalBuffer);
        console.log('缩略图生成成功, 大小:', thumbnail.length);

        // 初始化Supabase - 使用process.env而不是import.meta.env
        console.log('初始化Supabase客户端, URL:', process.env.VITE_SUPABASE_URL);
        const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.VITE_SUPABASE_KEY, { auth: { persistSession: false } });

        // 添加存储桶存在性检查
        console.log('检查存储桶是否存在...');
        const { data: bucketExists, error: bucketError } = await supabase.storage.getBucket('amap_images');

        if (bucketError) {
            console.error('获取存储桶信息失败:', bucketError);
            throw new Error(`获取存储桶信息失败: ${bucketError.message}`);
        }

        if (!bucketExists) {
            console.error('存储桶不存在');
            throw new Error('存储桶amap_images不存在');
        }

        console.log('存储桶检查通过, 开始上传文件...');

        // 并行上传文件
        const [originalRes, thumbRes] = await Promise.all([supabase.storage.from('amap_images').upload(originalPath, originalBuffer), supabase.storage.from('amap_images').upload(thumbnailPath, thumbnail)]);

        if (originalRes.error) {
            console.error('原始图片上传失败:', originalRes.error);
            throw originalRes.error;
        }
        if (thumbRes.error) {
            console.error('缩略图上传失败:', thumbRes.error);
            throw thumbRes.error;
        }

        console.log('文件上传成功, 路径:', {
            original: originalRes.data.path,
            thumbnail: thumbRes.data.path
        });

        const photo_url = `https://iabpcsmeijvjfgqhgocp.supabase.co/storage/v1/object/public/amap_images/${originalRes.data.path}`;
        const thumbnail_url = `https://iabpcsmeijvjfgqhgocp.supabase.co/storage/v1/object/public/amap_images/${thumbRes.data.path}`;
        // 返回图片URL给前端
        return res.status(200).json({
            success: true,
            data: {
                photo_url,
                thumbnail_url
            }
        });

        // // 构建符合表结构的插入数据
        // const insertData = {
        //     original_gps: {
        //         lng: metadata.original_lng,
        //         lat: metadata.original_lat
        //     },
        //     converted_gps: {
        //         lng: metadata.gcj_lng,
        //         lat: metadata.gcj_lat
        //     },
        //     name: metadata.file_name,
        //     gps_timestamp: new Date(metadata.timestamp).toISOString(),
        //     altitude: Number.isNaN(parseFloat(metadata.altitude)) ? null : parseFloat(metadata.altitude),
        //     city: metadata.city || '未知地区',
        //     full_address: metadata.full_address,
        //     original_url: 'https://iabpcsmeijvjfgqhgocp.supabase.co/storage/v1/object/public/amap_images/' + originalRes.data.path,
        //     thumbnail_url: 'https://iabpcsmeijvjfgqhgocp.supabase.co/storage/v1/object/public/amap_images/' + thumbRes.data.path
        // };

        // console.log('准备插入数据库的数据:', JSON.stringify(insertData));

        // // 插入数据库
        // const { data: insertedData, error: insertError } = await supabase.from('amap_marker_images').insert(insertData).select();

        // if (insertError) {
        //     console.error('数据库插入失败:', insertError);
        //     throw new Error(`数据库插入失败: ${insertError.message}`);
        // }

        // console.log('数据库插入成功, 返回数据:', insertedData);

        // res.status(200).json({
        //     success: true,
        //     data: insertedData && insertedData.length > 0 ? insertedData[0] : insertData
        // });
    } catch (error) {
        console.error('处理失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
}
