import express from 'express';
import bodyParser from 'body-parser';
import cors from 'cors';
import imageProcessor from './api/imageProcessor.js';
import panoProcessor from './api/panoProcessor.js';
import fileUpload from 'express-fileupload';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.resolve(path.dirname(__dirname), '.env') });

// 打印环境变量检查
console.log('环境变量检查:', {
    SUPABASE_URL: process.env.VITE_SUPABASE_URL ? '已设置' : '未设置',
    SUPABASE_KEY: process.env.VITE_SUPABASE_KEY ? '已设置' : '未设置'
});

const app = express();

// 使用中间件
app.use(bodyParser.json());
app.use(cors());
app.use(
    fileUpload({
        limits: {
            fileSize: 100 * 1024 * 1024,
            files: 10
        },
        useTempFiles: false, // 禁用临时文件
        parseNested: true,
        abortOnLimit: true,
        responseOnLimit: '文件大小超过限制',
        limitHandler: (req, res) => {
            res.status(413).json({ error: '文件过大' });
        }
    })
);

// 处理 /api/image-processor 请求
app.post('/api/image-processor', imageProcessor);

// 处理 /api/pano-processor 请求
app.post('/api/pano-processor', panoProcessor);

// 在路由中添加
app.get('/api/health', (req, res) => {
    res.status(200).json({
        status: 'ok',
        timestamp: new Date().toISOString()
    });
});

// 启动服务
const PORT = 3001;
app.listen(PORT, () => {
    console.log(`后端服务已启动，运行在 http://localhost:${PORT}`);
});

// 在文件末尾添加
process.on('uncaughtException', (err) => {
    console.error('未捕获的异常:', err);
    // 保持进程运行
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的 Promise 拒绝:', reason);
    // 保持进程运行
});
