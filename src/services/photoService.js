import { supabase } from './supabase.js';
/**
 * 获取所有照片标记
 */
export async function getAllPhotoMarkers() {
    try {
        const { data, error } = await supabase.from('amap_photo').select('*').order('create_time', { ascending: false });

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('获取照片标记失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 获取单个照片标记
 * @param {string} id 标记ID
 */
export async function getPhotoMarkerById(id) {
    try {
        const { data, error } = await supabase.from('amap_photo').select('*').eq('id', id).single();

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error(`获取照片标记(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 创建新的照片标记
 * @param {Object} markerData 标记数据
 */
export async function createPhotoMarker(markerData) {
    console.log('markerData', markerData);
    try {
        const { data, error } = await supabase.from('amap_photo').insert(markerData).select();

        if (error) throw error;
        return { success: true, data: data[0] };
    } catch (error) {
        console.error('创建照片标记失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 更新照片标记
 * @param {string} id 标记ID
 * @param {Object} markerData 更新的标记数据
 */
export async function updatePhotoMarker(id, markerData) {
    try {
        const { data, error } = await supabase.from('amap_photo').update(markerData).eq('id', id).select();

        if (error) throw error;
        return { success: true, data: data[0] };
    } catch (error) {
        console.error(`更新照片标记(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 删除照片标记
 * @param {string} id 标记ID
 */
export async function deletePhotoMarker(id) {
    try {
        const { error } = await supabase.from('amap_photo').delete().eq('id', id);

        if (error) throw error;
        return { success: true };
    } catch (error) {
        console.error(`删除照片标记(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 批量删除照片标记
 * @param {Array} ids 标记ID数组
 */
export async function deletePhotoMarkers(ids) {
    try {
        const { error } = await supabase.from('amap_photo').delete().in('id', ids);

        if (error) throw error;
        return { success: true };
    } catch (error) {
        console.error(`批量删除照片标记失败:`, error);
        return { success: false, error: error.message };
    }
}
