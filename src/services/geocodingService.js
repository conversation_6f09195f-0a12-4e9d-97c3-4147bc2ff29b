import * as fangService from './fangService.js';

/**
 * 地理编码服务
 * 用于批量获取房产数据的经纬度坐标
 */
class GeocodingService {
    constructor() {
        this.geocoder = null;
        this.isInitialized = false;
        this.batchSize = 10; // 每批处理的数量
        this.delay = 1000; // 请求间隔（毫秒）
    }

    /**
     * 初始化地理编码器
     * @param {Object} AMap - 高德地图对象
     */
    async initialize(AMap) {
        if (this.isInitialized) return;
        
        this.geocoder = new AMap.Geocoder({
            city: '027', // 武汉市区号
            radius: 1000, // 范围，单位：米
            extensions: 'all' // 返回更详细的信息
        });
        
        this.isInitialized = true;
        console.log('地理编码器初始化完成');
    }

    /**
     * 批量地理编码
     * @param {Array} addresses - 地址数组
     * @returns {Promise<Array>} 编码结果数组
     */
    async batchGeocode(addresses) {
        if (!this.isInitialized || !this.geocoder) {
            throw new Error('地理编码器未初始化');
        }

        return new Promise((resolve, reject) => {
            this.geocoder.getLocation(addresses, (status, result) => {
                if (status === 'complete' && result.geocodes) {
                    console.log(`批量地理编码成功，获取到 ${result.geocodes.length} 个结果`);
                    resolve(result.geocodes);
                } else {
                    console.error('批量地理编码失败:', status, result);
                    reject(new Error(`地理编码失败: ${status}`));
                }
            });
        });
    }

    /**
     * 构建搜索地址
     * @param {Object} fangData - 房产数据
     * @returns {string} 完整地址
     */
    buildSearchAddress(fangData) {
        // 构建完整地址：武汉市 + 区域 + 位置 + 名称
        const parts = ['武汉市'];
        
        if (fangData.district) {
            parts.push(fangData.district);
        }
        
        if (fangData.location) {
            parts.push(fangData.location);
        }
        
        if (fangData.name) {
            parts.push(fangData.name);
        }
        
        return parts.join('');
    }

    /**
     * 处理单批数据的地理编码
     * @param {Array} fangDataBatch - 房产数据批次
     * @returns {Promise<Array>} 更新结果
     */
    async processBatch(fangDataBatch) {
        try {
            // 构建地址数组
            const addresses = fangDataBatch.map(item => this.buildSearchAddress(item));
            console.log('正在处理地址:', addresses);

            // 批量地理编码
            const geocodes = await this.batchGeocode(addresses);
            
            // 处理结果并更新数据库
            const updateResults = [];
            
            for (let i = 0; i < fangDataBatch.length; i++) {
                const fangData = fangDataBatch[i];
                const geocode = geocodes[i];
                
                if (geocode && geocode.location) {
                    const lng = geocode.location.lng;
                    const lat = geocode.location.lat;
                    
                    console.log(`${fangData.name}: ${lng}, ${lat}`);
                    
                    // 更新数据库
                    const updateResult = await fangService.updateFangData(fangData.id, {
                        gcj_lng: lng,
                        gcj_lat: lat
                    });
                    
                    updateResults.push({
                        id: fangData.id,
                        name: fangData.name,
                        success: updateResult.success,
                        lng: lng,
                        lat: lat
                    });
                } else {
                    console.warn(`未找到 ${fangData.name} 的坐标信息`);
                    updateResults.push({
                        id: fangData.id,
                        name: fangData.name,
                        success: false,
                        error: '未找到坐标信息'
                    });
                }
            }
            
            return updateResults;
        } catch (error) {
            console.error('处理批次失败:', error);
            throw error;
        }
    }

    /**
     * 批量处理所有缺失坐标的房产数据
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Promise<Object>} 处理结果统计
     */
    async processAllMissingCoordinates(progressCallback = null) {
        try {
            // 获取所有缺失坐标的数据
            const result = await fangService.getAllFangData({
                missingCoordinates: true
            });
            
            if (!result.success) {
                throw new Error(result.error);
            }
            
            const allData = result.data;
            console.log(`找到 ${allData.length} 条缺失坐标的数据`);
            
            if (allData.length === 0) {
                return {
                    total: 0,
                    processed: 0,
                    success: 0,
                    failed: 0,
                    results: []
                };
            }
            
            // 分批处理
            const batches = [];
            for (let i = 0; i < allData.length; i += this.batchSize) {
                batches.push(allData.slice(i, i + this.batchSize));
            }
            
            console.log(`将分 ${batches.length} 批处理，每批 ${this.batchSize} 条`);
            
            const allResults = [];
            let successCount = 0;
            let failedCount = 0;
            
            for (let i = 0; i < batches.length; i++) {
                const batch = batches[i];
                console.log(`处理第 ${i + 1}/${batches.length} 批...`);
                
                try {
                    const batchResults = await this.processBatch(batch);
                    allResults.push(...batchResults);
                    
                    // 统计结果
                    batchResults.forEach(result => {
                        if (result.success) {
                            successCount++;
                        } else {
                            failedCount++;
                        }
                    });
                    
                    // 调用进度回调
                    if (progressCallback) {
                        progressCallback({
                            current: i + 1,
                            total: batches.length,
                            processed: (i + 1) * this.batchSize,
                            totalItems: allData.length,
                            success: successCount,
                            failed: failedCount
                        });
                    }
                    
                    // 延迟以避免请求过于频繁
                    if (i < batches.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, this.delay));
                    }
                    
                } catch (error) {
                    console.error(`第 ${i + 1} 批处理失败:`, error);
                    failedCount += batch.length;
                }
            }
            
            return {
                total: allData.length,
                processed: allData.length,
                success: successCount,
                failed: failedCount,
                results: allResults
            };
            
        } catch (error) {
            console.error('批量地理编码失败:', error);
            throw error;
        }
    }

    /**
     * 处理指定数量的测试数据
     * @param {number} count - 处理数量
     * @returns {Promise<Object>} 处理结果
     */
    async processTestData(count = 5) {
        try {
            // 获取指定数量的缺失坐标数据
            const result = await fangService.getAllFangData({
                missingCoordinates: true,
                limit: count
            });
            
            if (!result.success) {
                throw new Error(result.error);
            }
            
            const testData = result.data;
            console.log(`处理 ${testData.length} 条测试数据`);
            
            if (testData.length === 0) {
                return {
                    total: 0,
                    success: 0,
                    failed: 0,
                    results: []
                };
            }
            
            const results = await this.processBatch(testData);
            
            const successCount = results.filter(r => r.success).length;
            const failedCount = results.filter(r => !r.success).length;
            
            return {
                total: testData.length,
                success: successCount,
                failed: failedCount,
                results: results
            };
            
        } catch (error) {
            console.error('处理测试数据失败:', error);
            throw error;
        }
    }
}

// 创建单例
const geocodingService = new GeocodingService();
export default geocodingService;
