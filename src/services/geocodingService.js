/**
 * 地理编码服务
 */

import { supabase } from '@/services/supabase';
import logService from '@/services/logService';

const AMAP_REST_KEY = '7a5917e97482a3cb8b2c1631b7cd9971';

// 区域映射
const districtMap = {
    江夏: '江夏区',
    洪山: '洪山区',
    武昌: '武昌区',
    沌口: '汉南区',
    经济开发区: '汉南区',
    江汉: '江汉区',
    江岸: '江岸区',
    硚口: '硚口区',
    汉阳: '汉阳区',
    青山: '青山区',
    武汉: '武汉市',
    汉南: '汉南区',
    蔡甸: '蔡甸区',
    新洲: '新洲区',
    黄陂: '黄陂区',
    东西湖: '东西湖区'
};

// 武汉各区的大致坐标范围
const districtBounds = {
    江夏区: { minLng: 114.0, maxLng: 114.8, minLat: 30.1, maxLat: 30.6 },
    洪山区: { minLng: 114.2, maxLng: 114.6, minLat: 30.4, maxLat: 30.7 },
    武昌区: { minLng: 114.2, maxLng: 114.4, minLat: 30.5, maxLat: 30.7 },
    汉南区: { minLng: 114.0, maxLng: 114.3, minLat: 30.1, maxLat: 30.4 },
    江汉区: { minLng: 114.2, maxLng: 114.3, minLat: 30.5, maxLat: 30.7 },
    江岸区: { minLng: 114.2, maxLng: 114.4, minLat: 30.6, maxLat: 30.8 },
    硚口区: { minLng: 114.1, maxLng: 114.3, minLat: 30.5, maxLat: 30.7 },
    汉阳区: { minLng: 114.1, maxLng: 114.3, minLat: 30.4, maxLat: 30.6 },
    青山区: { minLng: 114.3, maxLng: 114.5, minLat: 30.6, maxLat: 30.7 },
    蔡甸区: { minLng: 113.8, maxLng: 114.3, minLat: 30.3, maxLat: 30.6 },
    新洲区: { minLng: 114.6, maxLng: 115.1, minLat: 30.6, maxLat: 31.2 },
    黄陂区: { minLng: 114.0, maxLng: 114.6, minLat: 30.7, maxLat: 31.4 },
    东西湖区: { minLng: 113.9, maxLng: 114.3, minLat: 30.6, maxLat: 30.9 }
};

class GeocodingService {
    constructor() {
        this.isProcessing = false;
    }

    // 验证坐标是否在指定区域内
    isCoordinateInDistrict(lng, lat, district) {
        const standardDistrict = districtMap[district] || district;
        const bounds = districtBounds[standardDistrict];

        if (!bounds) {
            // 如果没有具体区域边界，至少验证是否在武汉市范围内
            return lng >= 113.6 && lng <= 115.1 && lat >= 29.9 && lat <= 31.4;
        }

        return lng >= bounds.minLng && lng <= bounds.maxLng && lat >= bounds.minLat && lat <= bounds.maxLat;
    }

    // 地理编码单个地址
    async geocodeAddress(name, district, location) {
        try {
            const standardDistrict = districtMap[district] || district;

            // 尝试多种地址组合
            const addressVariations = [`武汉市${standardDistrict}${location}${name}`, `武汉市${standardDistrict}${name}`, `武汉市${location}${name}`, `武汉${name}`];

            for (const address of addressVariations) {
                const url = `https://restapi.amap.com/v3/geocode/geo?key=${AMAP_REST_KEY}&address=${encodeURIComponent(address)}&city=武汉`;
                const response = await fetch(url);
                const data = await response.json();

                if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
                    const location = data.geocodes[0].location.split(',');
                    const lng = parseFloat(location[0]);
                    const lat = parseFloat(location[1]);

                    // 验证坐标是否在武汉范围内
                    if (lng >= 113.6 && lng <= 115.1 && lat >= 29.9 && lat <= 31.4) {
                        // 验证是否在指定区域内
                        const inDistrict = this.isCoordinateInDistrict(lng, lat, district);

                        return {
                            success: true,
                            lng: lng,
                            lat: lat,
                            address: address,
                            formatted_address: data.geocodes[0].formatted_address,
                            inDistrict: inDistrict
                        };
                    }
                }

                // 延迟避免请求过频
                await new Promise((resolve) => setTimeout(resolve, 200));
            }

            return { success: false, reason: 'no_valid_results' };
        } catch (error) {
            return { success: false, reason: 'error', error: error.message };
        }
    }

    // 获取没有坐标的房产数据
    async getFangDataWithoutGcj(limit = 20) {
        try {
            const { data, error } = await supabase.from('fang_data').select('id, name, district, location').is('gcj', null).limit(limit);

            if (error) throw error;
            return data || [];
        } catch (error) {
            throw new Error(`获取数据失败: ${error.message}`);
        }
    }

    // 更新数据库中的gcj字段
    async updateFangDataGcj(id, gcjData) {
        try {
            const { error } = await supabase.from('fang_data').update({ gcj: gcjData }).eq('id', id);

            if (error) throw error;
            return true;
        } catch (error) {
            console.error(`更新数据失败 (ID: ${id}):`, error);
            return false;
        }
    }

    // 批量更新房屋位置信息
    async batchUpdateLocations(onProgress) {
        if (this.isProcessing) {
            throw new Error('正在处理中，请稍后再试');
        }

        this.isProcessing = true;

        try {
            let totalProcessed = 0;
            let successCount = 0;
            let failCount = 0;
            const processedItems = [];

            // 分批处理
            let hasMoreData = true;
            let batchNumber = 1;

            while (hasMoreData) {
                const fangData = await this.getFangDataWithoutGcj(20);

                if (fangData.length === 0) {
                    hasMoreData = false;
                    break;
                }

                console.log(`处理第 ${batchNumber} 批数据，共 ${fangData.length} 条`);

                for (const item of fangData) {
                    totalProcessed++;

                    const itemResult = {
                        id: item.id,
                        name: item.name,
                        district: item.district,
                        location: item.location,
                        success: false,
                        inDistrict: false,
                        coordinates: null,
                        error: null
                    };

                    try {
                        // 地理编码
                        const geocodeResult = await this.geocodeAddress(item.name, item.district, item.location);

                        if (geocodeResult.success) {
                            const gcjData = {
                                lng: geocodeResult.lng,
                                lat: geocodeResult.lat
                            };

                            // 更新数据库
                            const updateSuccess = await this.updateFangDataGcj(item.id, gcjData);

                            if (updateSuccess) {
                                successCount++;
                                itemResult.success = true;
                                itemResult.inDistrict = geocodeResult.inDistrict;
                                itemResult.coordinates = gcjData;

                                console.log(`✓ 成功: ${item.name} -> ${geocodeResult.lng}, ${geocodeResult.lat} (区域验证: ${geocodeResult.inDistrict ? '✓' : '✗'})`);
                            } else {
                                failCount++;
                                itemResult.error = '数据库更新失败';
                                console.log(`✗ 数据库更新失败: ${item.name}`);
                            }
                        } else {
                            failCount++;
                            itemResult.error = geocodeResult.reason || '地理编码失败';
                            console.log(`✗ 地理编码失败: ${item.name} - ${geocodeResult.reason}`);
                        }
                    } catch (error) {
                        failCount++;
                        itemResult.error = error.message;
                        console.error(`处理失败: ${item.name}`, error);
                    }

                    processedItems.push(itemResult);

                    // 调用进度回调
                    if (onProgress) {
                        onProgress({
                            current: batchNumber,
                            processed: totalProcessed,
                            success: successCount,
                            failed: failCount
                        });
                    }

                    // 延迟避免请求过频
                    await new Promise((resolve) => setTimeout(resolve, 300));
                }

                batchNumber++;

                // 批次间延迟
                await new Promise((resolve) => setTimeout(resolve, 1000));
            }

            const results = {
                totalProcessed,
                successCount,
                failCount,
                successRate: totalProcessed > 0 ? `${((successCount / totalProcessed) * 100).toFixed(1)}%` : '0%',
                items: processedItems
            };

            // 记录日志
            logService.logGeocoding('批量更新房屋位置信息', results);

            return results;
        } finally {
            this.isProcessing = false;
        }
    }

    // 获取处理状态
    getProcessingStatus() {
        return this.isProcessing;
    }
}

// 创建单例实例
const geocodingService = new GeocodingService();

export default geocodingService;
