import { supabase } from './supabase.js';
/**
 * 获取所有引脚标记
 */
export async function getAllPageDatas() {
    try {
        const { data, error } = await supabase.from('amap_page').select('*').order('create_time', { ascending: false });

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('获取引脚标记失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 获取单个引脚标记
 * @param {string} id 标记ID
 */
export async function getPageDataById(id) {
    try {
        const { data, error } = await supabase.from('amap_page').select('*').eq('id', id).single();

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error(`获取引脚标记(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 创建新的引脚标记
 * @param {Object} markerData 标记数据
 */
export async function createPageData(markerData) {
    try {
        const { data, error } = await supabase.from('amap_page').insert([markerData]).select();

        if (error) throw error;
        return { success: true, data: data[0] };
    } catch (error) {
        console.error('创建引脚标记失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 更新引脚标记
 * @param {string} id 标记ID
 * @param {Object} markerData 更新的标记数据
 */
export async function updatePageData(id, markerData) {
    console.log('updatePageMarker_id:', id); // Add this lin
    console.log('updatePageMarker_markerData:', markerData); // Add this line
    // 创建一个新对象，排除id字段
    const { id: _, ...updateData } = markerData;
    void _;
    console.log('updatePageMarker_markerData(已移除id):', updateData);
    try {
        if (!id) {
            console.error('ID is required for updating the record.');
            return;
        } else {
            console.log('ID is existed.', id);
        }
        const { data, error } = await supabase.from('amap_page').update(updateData).eq('id', id).select();

        if (error) throw error;
        return { success: true, data: data[0] };
    } catch (error) {
        console.error(`更新引脚标记(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 删除引脚标记
 * @param {string} id 标记ID
 */
export async function deletePageData(id) {
    try {
        const { error } = await supabase.from('amap_page').delete().eq('id', id);

        if (error) throw error;
        return { success: true };
    } catch (error) {
        console.error(`删除引脚标记(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 批量删除引脚标记
 * @param {Array} ids 标记ID数组
 */
export async function deletePageDatas(ids) {
    try {
        const { error } = await supabase.from('amap_page').delete().in('id', ids);

        if (error) throw error;
        return { success: true };
    } catch (error) {
        console.error(`批量删除引脚标记失败:`, error);
        return { success: false, error: error.message };
    }
}
