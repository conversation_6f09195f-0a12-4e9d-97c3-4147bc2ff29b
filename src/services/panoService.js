import { supabase } from './supabase.js';
/**
 * 获取所有全景标记
 */
export async function getAllPanoMarkers() {
    try {
        const { data, error } = await supabase.from('amap_pano').select('*').order('create_time', { ascending: false });

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('获取全景标记失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 获取单个全景标记
 * @param {string} id 标记ID
 */
export async function getPanoMarkerById(id) {
    try {
        const { data, error } = await supabase.from('amap_pano').select('*').eq('id', id).single();

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error(`获取全景标记(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 创建新的全景标记
 * @param {Object} markerData 标记数据
 */
export async function createPanoMarker(markerData) {
    try {
        const { data, error } = await supabase.from('amap_pano').insert(markerData).select();
        console.log('创建全景标记数据:', data);
        if (error) throw error;
        return { success: true, data: data[0] };
    } catch (error) {
        console.error('创建全景标记失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 更新全景标记
 * @param {string} id 标记ID
 * @param {Object} markerData 更新的标记数据
 */
export async function updatePanoMarker(id, markerData) {
    try {
        const { data, error } = await supabase.from('amap_pano').update(markerData).eq('id', id).select();

        if (error) throw error;
        console.log('更新全景标记成功:', data);
        return { success: true, data: data[0] };
    } catch (error) {
        console.error(`更新全景标记(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 删除全景标记
 * @param {string} id 标记ID
 */
export async function deletePanoMarker(id) {
    try {
        const { error } = await supabase.from('amap_pano').delete().eq('id', id);

        if (error) throw error;
        return { success: true };
    } catch (error) {
        console.error(`删除全景标记(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 批量删除全景标记
 * @param {Array} ids 标记ID数组
 */
export async function deletePanoMarkers(ids) {
    try {
        const { error } = await supabase.from('amap_pano').delete().in('id', ids);

        if (error) throw error;
        return { success: true };
    } catch (error) {
        console.error(`批量删除全景标记失败:`, error);
        return { success: false, error: error.message };
    }
}
