import AMapLoader from '@amap/amap-jsapi-loader';

// 单例模式管理 AMap 实例
class MapService {
    constructor() {
        this.AMap = null;
        this.map = null;
        this.markers = new Map(); // 使用 Map 对象存储所有标记，便于管理
        this.isLoading = false;
        this.loadPromise = null;
    }

    // 初始化并加载高德地图
    async initMap() {
        // 如果已经加载过或正在加载，直接返回 Promise
        if (this.AMap) {
            return Promise.resolve(this.AMap);
        }

        if (this.loadPromise) {
            return this.loadPromise;
        }

        // 设置安全配置
        window._AMapSecurityConfig = { securityJsCode: '05b99346f0c80233a92e6c1d6b56a9f3' };

        // 创建加载 Promise
        this.isLoading = true;
        this.loadPromise = AMapLoader.load({
            key: 'f30bebf0ca8200ba9080214e826ff768',
            version: '2.0',
            plugins: ['AMap.Geocoder', 'AMap.Scale', 'AMap.ToolBar', 'AMap.InfoWindow', 'AMap.PlaceSearch'],
            useAMapUI: false,
            mapStyle: 'amap://styles/normal'
        })
            .then((AMap) => {
                this.AMap = AMap;
                window.AMap = AMap; // 全局可访问，但不推荐直接使用
                this.isLoading = false;
                return AMap;
            })
            .catch((error) => {
                this.isLoading = false;
                this.loadPromise = null;
                console.error('高德地图加载失败:', error);
                throw error;
            });

        return this.loadPromise;
    }

    // 创建地图实例
    async createMap(containerId, options = {}) {
        if (!this.AMap) {
            await this.initMap();
        }

        if (this.map) {
            return this.map;
        }

        const defaultOptions = {
            viewMode: '3D',
            zoom: 11,
            center: [114.305548, 30.59294]
        };

        this.map = new this.AMap.Map(containerId, { ...defaultOptions, ...options });

        // 添加基础控件
        this.map.addControl(new this.AMap.ToolBar());
        this.map.addControl(new this.AMap.Scale());

        return this.map;
    }

    // 获取地图实例
    getMap() {
        return this.map;
    }

    // 获取 AMap 实例
    getAMap() {
        return this.AMap;
    }

    // 添加标记
    addMarker(id, options) {
        if (!this.map || !this.AMap) {
            console.error('地图未初始化');
            return null;
        }

        // 如果已存在同ID标记，先移除
        if (this.markers.has(id)) {
            this.removeMarker(id);
        }

        const marker = new this.AMap.Marker(options);
        marker.setMap(this.map);
        this.markers.set(id, marker);
        return marker;
    }

    // 移除标记
    removeMarker(id) {
        if (this.markers.has(id)) {
            const marker = this.markers.get(id);
            marker.setMap(null);
            this.markers.delete(id);
            return true;
        }
        return false;
    }

    // 清除所有标记
    clearMarkers() {
        this.markers.forEach((marker) => {
            marker.setMap(null);
        });
        this.markers.clear();
    }

    // 销毁地图实例
    destroy() {
        if (this.map) {
            this.clearMarkers();
            this.map.destroy();
            this.map = null;
            // 重置其他状态
            this.loadPromise = null;
        }
    }
}

// 创建单例
const mapService = new MapService();
export default mapService;
