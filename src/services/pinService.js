import { supabase } from './supabase.js';
/**
 * 获取所有引脚标记
 */
export async function getAllPinMarkers() {
    try {
        const { data, error } = await supabase.from('amap_pin').select('*').order('create_time', { ascending: false });

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('获取引脚标记失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 获取单个引脚标记
 * @param {string} id 标记ID
 */
export async function getPinMarkerById(id) {
    try {
        const { data, error } = await supabase.from('amap_pin').select('*').eq('id', id).single();

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error(`获取引脚标记(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 创建新的引脚标记
 * @param {Object} markerData 标记数据
 */
export async function createPinMarker(markerData) {
    try {
        const { data, error } = await supabase.from('amap_pin').insert([markerData]).select();

        if (error) throw error;
        return { success: true, data: data[0] };
    } catch (error) {
        console.error('创建引脚标记失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 更新引脚标记
 * @param {string} id 标记ID
 * @param {Object} markerData 更新的标记数据
 */
export async function updatePinMarker(id, markerData) {
    console.log('updatePinMarker_id:', id); // Add this lin
    console.log('updatePinMarker_markerData:', markerData); // Add this line
    // 创建一个新对象，排除id字段
    const { id: _, ...updateData } = markerData;
    void _;
    console.log('updatePinMarker_markerData(已移除id):', updateData);
    try {
        if (!id) {
            console.error('ID is required for updating the record.');
            return;
        } else {
            console.log('ID is existed.', id);
        }
        const { data, error } = await supabase.from('amap_pin').update(updateData).eq('id', id).select();

        if (error) throw error;
        return { success: true, data: data[0] };
    } catch (error) {
        console.error(`更新引脚标记(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 删除引脚标记
 * @param {string} id 标记ID
 */
export async function deletePinMarker(id) {
    try {
        const { error } = await supabase.from('amap_pin').delete().eq('id', id);

        if (error) throw error;
        return { success: true };
    } catch (error) {
        console.error(`删除引脚标记(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 批量删除引脚标记
 * @param {Array} ids 标记ID数组
 */
export async function deletePinMarkers(ids) {
    try {
        const { error } = await supabase.from('amap_pin').delete().in('id', ids);

        if (error) throw error;
        return { success: true };
    } catch (error) {
        console.error(`批量删除引脚标记失败:`, error);
        return { success: false, error: error.message };
    }
}
