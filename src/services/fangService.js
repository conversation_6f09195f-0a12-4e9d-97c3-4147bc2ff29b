import { supabase } from './supabase.js';

/**
 * 房产数据服务
 * 提供房产数据的 CRUD 操作和数据导入功能
 */

/**
 * 获取所有房产数据
 * @param {Object} options - 查询选项
 * @param {string} options.district - 区域筛选
 * @param {string} options.location - 位置筛选
 * @param {number} options.minPrice - 最低价格
 * @param {number} options.maxPrice - 最高价格
 * @param {number} options.limit - 限制数量
 * @param {number} options.offset - 偏移量
 * @returns {Promise<{success: boolean, data?: Array, error?: string, count?: number}>}
 */
export async function getAllFangData(options = {}) {
    try {
        let query = supabase.from('fang_data').select('*', { count: 'exact' }).order('created_at', { ascending: false });

        // 筛选缺失坐标的数据
        if (options.missingCoordinates) {
            query = query.or('gcj_lng.is.null,gcj_lat.is.null');
        }

        // 应用筛选条件
        if (options.district) {
            query = query.eq('district', options.district);
        }

        if (options.location) {
            query = query.eq('location', options.location);
        }

        if (options.minPrice !== undefined) {
            query = query.gte('price', options.minPrice);
        }

        if (options.maxPrice !== undefined) {
            query = query.lte('price', options.maxPrice);
        }

        // 应用分页
        if (options.limit) {
            query = query.limit(options.limit);
        }

        if (options.offset) {
            query = query.range(options.offset, options.offset + (options.limit || 50) - 1);
        }

        const { data, error, count } = await query;

        if (error) throw error;

        return {
            success: true,
            data: data || [],
            count: count || 0
        };
    } catch (error) {
        console.error('获取房产数据失败:', error);
        return {
            success: false,
            error: error.message,
            data: [],
            count: 0
        };
    }
}

/**
 * 根据ID获取单个房产数据
 * @param {number} id - 房产数据ID
 * @returns {Promise<{success: boolean, data?: Object, error?: string}>}
 */
export async function getFangDataById(id) {
    try {
        const { data, error } = await supabase.from('fang_data').select('*').eq('id', id).single();

        if (error) throw error;

        return { success: true, data };
    } catch (error) {
        console.error(`获取房产数据(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 根据安居客ID获取房产数据
 * @param {string} ajkId - 安居客ID
 * @returns {Promise<{success: boolean, data?: Object, error?: string}>}
 */
export async function getFangDataByAjkId(ajkId) {
    try {
        const { data, error } = await supabase.from('fang_data').select('*').eq('ajk_id', ajkId).single();

        if (error) throw error;

        return { success: true, data };
    } catch (error) {
        console.error(`获取房产数据(安居客ID: ${ajkId})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 创建新的房产数据
 * @param {Object} fangData - 房产数据对象
 * @returns {Promise<{success: boolean, data?: Object, error?: string}>}
 */
export async function createFangData(fangData) {
    try {
        const { data, error } = await supabase.from('fang_data').insert([fangData]).select().single();

        if (error) throw error;

        return { success: true, data };
    } catch (error) {
        console.error('创建房产数据失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 批量创建房产数据
 * @param {Array} fangDataArray - 房产数据数组
 * @returns {Promise<{success: boolean, data?: Array, error?: string, insertedCount?: number}>}
 */
export async function createFangDataBatch(fangDataArray) {
    try {
        const { data, error } = await supabase.from('fang_data').insert(fangDataArray).select();

        if (error) throw error;

        return {
            success: true,
            data: data || [],
            insertedCount: data ? data.length : 0
        };
    } catch (error) {
        console.error('批量创建房产数据失败:', error);
        return {
            success: false,
            error: error.message,
            insertedCount: 0
        };
    }
}

/**
 * 更新房产数据
 * @param {number} id - 房产数据ID
 * @param {Object} updateData - 更新的数据
 * @returns {Promise<{success: boolean, data?: Object, error?: string}>}
 */
export async function updateFangData(id, updateData) {
    try {
        const { data, error } = await supabase.from('fang_data').update(updateData).eq('id', id).select().single();

        if (error) throw error;

        return { success: true, data };
    } catch (error) {
        console.error(`更新房产数据(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 删除房产数据
 * @param {number} id - 房产数据ID
 * @returns {Promise<{success: boolean, error?: string}>}
 */
export async function deleteFangData(id) {
    try {
        const { error } = await supabase.from('fang_data').delete().eq('id', id);

        if (error) throw error;

        return { success: true };
    } catch (error) {
        console.error(`删除房产数据(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 获取所有区域列表
 * @returns {Promise<{success: boolean, data?: Array, error?: string}>}
 */
export async function getDistrictList() {
    try {
        const { data, error } = await supabase.from('fang_data').select('district').not('district', 'is', null).order('district');

        if (error) throw error;

        // 去重
        const uniqueDistricts = [...new Set(data.map((item) => item.district))];

        return { success: true, data: uniqueDistricts };
    } catch (error) {
        console.error('获取区域列表失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 获取指定区域的位置列表
 * @param {string} district - 区域名称
 * @returns {Promise<{success: boolean, data?: Array, error?: string}>}
 */
export async function getLocationsByDistrict(district) {
    try {
        const { data, error } = await supabase.from('fang_data').select('location').eq('district', district).not('location', 'is', null).order('location');

        if (error) throw error;

        // 去重
        const uniqueLocations = [...new Set(data.map((item) => item.location))];

        return { success: true, data: uniqueLocations };
    } catch (error) {
        console.error(`获取区域(${district})位置列表失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 搜索房产数据
 * @param {string} keyword - 搜索关键词
 * @param {Object} options - 搜索选项
 * @returns {Promise<{success: boolean, data?: Array, error?: string}>}
 */
export async function searchFangData(keyword, options = {}) {
    try {
        let query = supabase.from('fang_data').select('*').or(`name.ilike.%${keyword}%,district.ilike.%${keyword}%,location.ilike.%${keyword}%`).order('created_at', { ascending: false });

        if (options.limit) {
            query = query.limit(options.limit);
        }

        const { data, error } = await query;

        if (error) throw error;

        return { success: true, data: data || [] };
    } catch (error) {
        console.error('搜索房产数据失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 获取价格统计信息
 * @returns {Promise<{success: boolean, data?: Object, error?: string}>}
 */
export async function getPriceStatistics() {
    try {
        const { data, error } = await supabase.rpc('get_price_statistics');

        if (error) throw error;

        return { success: true, data: data || {} };
    } catch (error) {
        console.error('获取价格统计失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 解析 CSV 数据行
 * @param {string} csvLine - CSV 行数据
 * @returns {Object} 解析后的房产数据对象
 */
function parseCsvLine(csvLine) {
    // 更智能的 CSV 解析，处理包含逗号的字段
    const fields = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < csvLine.length; i++) {
        const char = csvLine[i];

        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            fields.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }

    // 添加最后一个字段
    fields.push(current.trim());

    return {
        sort: fields[0] || null,
        name: fields[1] || null,
        ajk_id: fields[2] || null,
        district: fields[3] || null,
        location: fields[4] || null,
        price: fields[5] ? parseFloat(fields[5]) : null,
        tags: fields[6] || null,
        jianmian: fields[7] || null,
        establish: fields[8] || null,
        on_sold: fields[9] || null,
        ajk_pano: fields[10] || null,
        ajk_url: fields[11] || null,
        gcj_lng: null, // 需要后续通过地理编码获取
        gcj_lat: null // 需要后续通过地理编码获取
    };
}

/**
 * 从 CSV 文件导入房产数据
 * @param {string} csvFilePath - CSV 文件路径（相对于 public 目录）
 * @returns {Promise<{success: boolean, data?: Object, error?: string}>}
 */
export async function importFangDataFromCsv(csvFilePath = '/ajk-fang.csv') {
    try {
        // 获取 CSV 文件内容
        const response = await fetch(csvFilePath);
        if (!response.ok) {
            throw new Error(`无法读取 CSV 文件: ${response.statusText}`);
        }

        const csvText = await response.text();
        const lines = csvText.split('\n').filter((line) => line.trim());

        if (lines.length <= 1) {
            throw new Error('CSV 文件为空或只有标题行');
        }

        // 跳过标题行，解析数据行
        const fangDataArray = [];
        const errors = [];

        for (let i = 1; i < lines.length; i++) {
            try {
                const fangData = parseCsvLine(lines[i]);

                // 验证必要字段
                if (!fangData.name) {
                    errors.push(`第 ${i + 1} 行: 缺少小区名称`);
                    continue;
                }

                fangDataArray.push(fangData);
            } catch (error) {
                errors.push(`第 ${i + 1} 行解析失败: ${error.message}`);
            }
        }

        if (fangDataArray.length === 0) {
            throw new Error('没有有效的数据可以导入');
        }

        // 批量插入数据
        const batchSize = 100; // 每批处理100条记录
        let totalInserted = 0;
        const insertErrors = [];

        for (let i = 0; i < fangDataArray.length; i += batchSize) {
            const batch = fangDataArray.slice(i, i + batchSize);

            try {
                const result = await createFangDataBatch(batch);
                if (result.success) {
                    totalInserted += result.insertedCount;
                } else {
                    insertErrors.push(`批次 ${Math.floor(i / batchSize) + 1} 插入失败: ${result.error}`);
                }
            } catch (error) {
                insertErrors.push(`批次 ${Math.floor(i / batchSize) + 1} 插入异常: ${error.message}`);
            }
        }

        return {
            success: true,
            data: {
                totalProcessed: fangDataArray.length,
                totalInserted,
                parseErrors: errors,
                insertErrors,
                successRate: totalInserted / fangDataArray.length
            }
        };
    } catch (error) {
        console.error('导入 CSV 数据失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 清空房产数据表
 * @returns {Promise<{success: boolean, error?: string}>}
 */
export async function clearAllFangData() {
    try {
        const { error } = await supabase.from('fang_data').delete().neq('id', 0); // 删除所有记录

        if (error) throw error;

        return { success: true };
    } catch (error) {
        console.error('清空房产数据失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 获取数据库统计信息
 * @returns {Promise<{success: boolean, data?: Object, error?: string}>}
 */
export async function getFangDataStatistics() {
    try {
        // 获取总数
        const { count: totalCount, error: countError } = await supabase.from('fang_data').select('*', { count: 'exact', head: true });

        if (countError) throw countError;

        // 获取区域分布
        const { data: districtData, error: districtError } = await supabase.from('fang_data').select('district').not('district', 'is', null);

        if (districtError) throw districtError;

        // 统计区域分布
        const districtStats = {};
        districtData.forEach((item) => {
            districtStats[item.district] = (districtStats[item.district] || 0) + 1;
        });

        // 获取价格范围
        const { data: priceData, error: priceError } = await supabase.from('fang_data').select('price').not('price', 'is', null).order('price');

        if (priceError) throw priceError;

        const prices = priceData.map((item) => item.price);
        const priceStats = {
            min: prices.length > 0 ? Math.min(...prices) : 0,
            max: prices.length > 0 ? Math.max(...prices) : 0,
            avg: prices.length > 0 ? prices.reduce((a, b) => a + b, 0) / prices.length : 0
        };

        return {
            success: true,
            data: {
                totalCount,
                districtStats,
                priceStats,
                lastUpdated: new Date().toISOString()
            }
        };
    } catch (error) {
        console.error('获取统计信息失败:', error);
        return { success: false, error: error.message };
    }
}
