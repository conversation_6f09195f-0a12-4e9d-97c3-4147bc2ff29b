import boundaryService from './boundaryService.js';
import { createMatrixMarker } from '../schema/matrixMarker.js';

/**
 * 网格生成服务类
 * 用于在指定区域内生成1km间距的网格点
 */
class GridService {
    constructor() {
        this.gridCache = new Map();
        this.defaultGridSize = 1000; // 默认1km网格
    }

    /**
     * 计算1km对应的经纬度差值
     * 在不同纬度下，1km对应的经纬度差值是不同的
     * @param {number} lat - 纬度
     * @returns {Object} {latDelta, lngDelta} 经纬度差值
     */
    calculateGridDelta(lat) {
        // 地球半径（米）
        const R = 6371000;

        // 1km对应的纬度差值（纬度差值在全球范围内基本恒定）
        const latDelta = (this.defaultGridSize / R) * (180 / Math.PI);

        // 1km对应的经度差值（随纬度变化）
        const lngDelta = latDelta / Math.cos((lat * Math.PI) / 180);

        return { latDelta, lngDelta };
    }

    /**
     * 生成武汉三环内的网格点
     * @param {Object} options - 生成选项
     * @param {number} [options.gridSize=1000] - 网格大小（米）
     * @param {boolean} [options.onlyInBoundary=true] - 是否只生成边界内的点
     * @returns {Promise<Array>} MatrixMarker对象数组
     */
    async generateWuhanThirdRingGrid(options = {}) {
        const { gridSize = 1000, onlyInBoundary = true } = options;

        // 获取边界矩形
        const bounds = await boundaryService.getWuhanThirdRingBounds();

        console.log('边界矩形:', bounds);

        // 使用边界中心点计算网格间距
        const centerLat = bounds.center.lat;
        const { latDelta, lngDelta } = this.calculateGridDelta(centerLat);

        console.log(`网格间距: 纬度 ${latDelta.toFixed(6)}°, 经度 ${lngDelta.toFixed(6)}°`);

        const gridPoints = [];
        let gridX = 0;
        let gridY = 0;

        // 从西南角开始生成网格点
        let currentLat = bounds.south;

        while (currentLat <= bounds.north) {
            let currentLng = bounds.west;
            gridX = 0;

            while (currentLng <= bounds.east) {
                const point = { lng: currentLng, lat: currentLat };

                // 如果需要检查边界，则只添加在三环内的点
                let shouldAdd = true;
                if (onlyInBoundary) {
                    shouldAdd = await boundaryService.isPointInWuhanThirdRing(point);
                }

                if (shouldAdd) {
                    // 创建 MatrixMarker 对象
                    const matrixMarker = createMatrixMarker({
                        x: gridX,
                        y: gridY,
                        custom_gcj: point,
                        gps: this.gcjToWgs84(point), // 转换为GPS坐标
                        gcj: point, // GCJ-02坐标
                        grid_size: gridSize,
                        boundary_region: 'wuhan_3rd_ring'
                    });

                    gridPoints.push(matrixMarker);
                }

                currentLng += lngDelta;
                gridX++;
            }

            currentLat += latDelta;
            gridY++;
        }

        console.log(`生成网格点总数: ${gridPoints.length}`);
        return gridPoints;
    }

    /**
     * 生成指定区域的网格点
     * @param {Object} bounds - 边界矩形 {north, south, east, west}
     * @param {Object} options - 生成选项
     * @param {number} [options.gridSize=1000] - 网格大小（米）
     * @param {Array} [options.boundary] - 边界多边形点数组，用于过滤
     * @returns {Array} MatrixMarker对象数组
     */
    generateGridInBounds(bounds, options = {}) {
        const { gridSize = 1000, boundary } = options;

        // 使用边界中心点计算网格间距
        const centerLat = (bounds.north + bounds.south) / 2;
        const { latDelta, lngDelta } = this.calculateGridDelta(centerLat);

        const gridPoints = [];
        let gridX = 0;
        let gridY = 0;

        let currentLat = bounds.south;

        while (currentLat <= bounds.north) {
            let currentLng = bounds.west;
            gridX = 0;

            while (currentLng <= bounds.east) {
                const point = { lng: currentLng, lat: currentLat };

                // 如果提供了边界多边形，检查点是否在内部
                let shouldAdd = true;
                if (boundary) {
                    shouldAdd = boundaryService.isPointInPolygon(point, boundary);
                }

                if (shouldAdd) {
                    const matrixMarker = createMatrixMarker({
                        x: gridX,
                        y: gridY,
                        custom_gcj: point,
                        gps: this.gcjToWgs84(point),
                        gcj: point,
                        grid_size: gridSize
                    });

                    gridPoints.push(matrixMarker);
                }

                currentLng += lngDelta;
                gridX++;
            }

            currentLat += latDelta;
            gridY++;
        }

        return gridPoints;
    }

    /**
     * 验证网格点间距
     * @param {Array} gridPoints - 网格点数组
     * @param {number} expectedDistance - 期望距离（米）
     * @returns {Object} 验证结果
     */
    validateGridSpacing(gridPoints, expectedDistance = 1000) {
        if (gridPoints.length < 2) {
            return { valid: false, message: '网格点数量不足' };
        }

        const distances = [];

        // 检查相邻点的距离
        for (let i = 0; i < gridPoints.length - 1; i++) {
            const point1 = gridPoints[i].gcj;
            const point2 = gridPoints[i + 1].gcj;
            const distance = boundaryService.calculateDistance(point1, point2);
            distances.push(distance);
        }

        const avgDistance = distances.reduce((sum, d) => sum + d, 0) / distances.length;
        const tolerance = expectedDistance * 0.1; // 10% 容差

        const valid = Math.abs(avgDistance - expectedDistance) <= tolerance;

        return {
            valid,
            averageDistance: avgDistance,
            expectedDistance,
            tolerance,
            message: valid ? '网格间距验证通过' : `网格间距不符合预期，平均距离: ${avgDistance.toFixed(2)}m`
        };
    }

    /**
     * GCJ-02 转 WGS84 坐标
     * 简化的转换算法，适用于中国境内
     * @param {Object} gcjPoint - GCJ-02坐标点 {lng, lat}
     * @returns {Object} WGS84坐标点 {lng, lat}
     */
    gcjToWgs84(gcjPoint) {
        const { lng, lat } = gcjPoint;

        // 简化的转换参数（适用于武汉地区）
        const dLat = this.transformLat(lng - 105.0, lat - 35.0);
        const dLng = this.transformLng(lng - 105.0, lat - 35.0);

        const radLat = (lat / 180.0) * Math.PI;
        let magic = Math.sin(radLat);
        const eccentricity = 0.006693421622965943;
        magic = 1 - eccentricity * magic * magic;
        const sqrtMagic = Math.sqrt(magic);

        const earthRadius = 6378245.0;
        const deltaLat = (dLat * 180.0) / (((earthRadius * (1 - eccentricity)) / (magic * sqrtMagic)) * Math.PI);
        const deltaLng = (dLng * 180.0) / ((earthRadius / sqrtMagic) * Math.cos(radLat) * Math.PI);

        return {
            lng: lng - deltaLng,
            lat: lat - deltaLat
        };
    }

    /**
     * 纬度转换辅助函数
     */
    transformLat(lng, lat) {
        let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
        ret += ((20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0) / 3.0;
        ret += ((20.0 * Math.sin(lat * Math.PI) + 40.0 * Math.sin((lat / 3.0) * Math.PI)) * 2.0) / 3.0;
        ret += ((160.0 * Math.sin((lat / 12.0) * Math.PI) + 320 * Math.sin((lat * Math.PI) / 30.0)) * 2.0) / 3.0;
        return ret;
    }

    /**
     * 经度转换辅助函数
     */
    transformLng(lng, lat) {
        let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
        ret += ((20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0) / 3.0;
        ret += ((20.0 * Math.sin(lng * Math.PI) + 40.0 * Math.sin((lng / 3.0) * Math.PI)) * 2.0) / 3.0;
        ret += ((150.0 * Math.sin((lng / 12.0) * Math.PI) + 300.0 * Math.sin((lng / 30.0) * Math.PI)) * 2.0) / 3.0;
        return ret;
    }

    /**
     * 生成网格点（默认为武汉三环区域）
     * 这是 generateWuhanThirdRingGrid 的别名方法
     * @param {Object} options - 生成选项
     * @returns {Promise<Array>} MatrixMarker对象数组
     */
    async generateGridPoints(options = {}) {
        return await this.generateWuhanThirdRingGrid(options);
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.gridCache.clear();
    }
}

// 创建单例
const gridService = new GridService();
export default gridService;
