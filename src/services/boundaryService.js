/**
 * 边界服务类
 * 用于获取武汉三环边界数据和计算矩形覆盖区域
 */
class BoundaryService {
    constructor() {
        this.boundaryCache = new Map();
        this.wuhanThirdRingBoundary = null;
    }

    /**
     * 获取武汉三环边界数据
     * 由于高德地图API可能没有直接的三环边界接口，我们使用预定义的边界点
     * @returns {Promise<Array>} 边界坐标点数组 [{lng, lat}, ...]
     */
    async getWuhanThirdRingBoundary() {
        // 如果已缓存，直接返回
        if (this.wuhanThirdRingBoundary) {
            return this.wuhanThirdRingBoundary;
        }

        // 武汉三环大致边界坐标点（GCJ-02坐标系）
        // 这些坐标点是根据武汉三环路实际路线近似提取的
        const boundaryPoints = [
            // 北段
            { lng: 114.345, lat: 30.65 },
            { lng: 114.38, lat: 30.645 },
            { lng: 114.41, lat: 30.635 },
            { lng: 114.435, lat: 30.62 },
            // 东段
            { lng: 114.45, lat: 30.6 },
            { lng: 114.46, lat: 30.575 },
            { lng: 114.465, lat: 30.55 },
            { lng: 114.46, lat: 30.525 },
            // 东南段
            { lng: 114.45, lat: 30.5 },
            { lng: 114.435, lat: 30.48 },
            { lng: 114.415, lat: 30.465 },
            { lng: 114.39, lat: 30.455 },
            // 南段
            { lng: 114.365, lat: 30.45 },
            { lng: 114.34, lat: 30.455 },
            { lng: 114.315, lat: 30.465 },
            { lng: 114.29, lat: 30.48 },
            // 西南段
            { lng: 114.27, lat: 30.5 },
            { lng: 114.255, lat: 30.525 },
            { lng: 114.25, lat: 30.55 },
            { lng: 114.255, lat: 30.575 },
            // 西段
            { lng: 114.265, lat: 30.6 },
            { lng: 114.28, lat: 30.62 },
            { lng: 114.3, lat: 30.635 },
            { lng: 114.32, lat: 30.645 }
        ];

        this.wuhanThirdRingBoundary = boundaryPoints;
        return boundaryPoints;
    }

    /**
     * 计算边界的最小外接矩形
     * @param {Array} boundaryPoints - 边界点数组
     * @returns {Object} 矩形边界 {north, south, east, west}
     */
    calculateBoundingBox(boundaryPoints) {
        if (!boundaryPoints || boundaryPoints.length === 0) {
            throw new Error('边界点数组不能为空');
        }

        let minLng = boundaryPoints[0].lng;
        let maxLng = boundaryPoints[0].lng;
        let minLat = boundaryPoints[0].lat;
        let maxLat = boundaryPoints[0].lat;

        boundaryPoints.forEach((point) => {
            minLng = Math.min(minLng, point.lng);
            maxLng = Math.max(maxLng, point.lng);
            minLat = Math.min(minLat, point.lat);
            maxLat = Math.max(maxLat, point.lat);
        });

        return {
            north: maxLat,
            south: minLat,
            east: maxLng,
            west: minLng,
            center: {
                lng: (minLng + maxLng) / 2,
                lat: (minLat + maxLat) / 2
            },
            width: maxLng - minLng,
            height: maxLat - minLat
        };
    }

    /**
     * 获取武汉三环的边界矩形
     * @returns {Promise<Object>} 边界矩形对象
     */
    async getWuhanThirdRingBounds() {
        const boundaryPoints = await this.getWuhanThirdRingBoundary();
        return this.calculateBoundingBox(boundaryPoints);
    }

    /**
     * 判断点是否在多边形内（射线法）
     * @param {Object} point - 测试点 {lng, lat}
     * @param {Array} polygon - 多边形顶点数组
     * @returns {boolean} 是否在多边形内
     */
    isPointInPolygon(point, polygon) {
        const x = point.lng;
        const y = point.lat;
        let inside = false;

        for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
            const xi = polygon[i].lng;
            const yi = polygon[i].lat;
            const xj = polygon[j].lng;
            const yj = polygon[j].lat;

            if (yi > y !== yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi) {
                inside = !inside;
            }
        }

        return inside;
    }

    /**
     * 判断点是否在武汉三环内
     * @param {Object} point - 测试点 {lng, lat}
     * @returns {Promise<boolean>} 是否在三环内
     */
    async isPointInWuhanThirdRing(point) {
        const boundary = await this.getWuhanThirdRingBoundary();
        return this.isPointInPolygon(point, boundary);
    }

    /**
     * 计算两点间距离（单位：米）
     * 使用 Haversine 公式
     * @param {Object} point1 - 点1 {lng, lat}
     * @param {Object} point2 - 点2 {lng, lat}
     * @returns {number} 距离（米）
     */
    calculateDistance(point1, point2) {
        const R = 6371000; // 地球半径，单位：米
        const dLat = this.toRadians(point2.lat - point1.lat);
        const dLng = this.toRadians(point2.lng - point1.lng);

        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(this.toRadians(point1.lat)) * Math.cos(this.toRadians(point2.lat)) * Math.sin(dLng / 2) * Math.sin(dLng / 2);

        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }

    /**
     * 角度转弧度
     * @param {number} degrees - 角度
     * @returns {number} 弧度
     */
    toRadians(degrees) {
        return degrees * (Math.PI / 180);
    }

    /**
     * 弧度转角度
     * @param {number} radians - 弧度
     * @returns {number} 角度
     */
    toDegrees(radians) {
        return radians * (180 / Math.PI);
    }

    /**
     * 根据起点、方向和距离计算目标点
     * @param {Object} point - 起点 {lng, lat}
     * @param {number} bearing - 方向角（度）
     * @param {number} distance - 距离（米）
     * @returns {Object} 目标点 {lng, lat}
     */
    calculateDestinationPoint(point, bearing, distance) {
        const R = 6371000; // 地球半径，单位：米
        const δ = distance / R; // 角距离
        const θ = this.toRadians(bearing);

        const φ1 = this.toRadians(point.lat);
        const λ1 = this.toRadians(point.lng);

        const φ2 = Math.asin(Math.sin(φ1) * Math.cos(δ) + Math.cos(φ1) * Math.sin(δ) * Math.cos(θ));
        const λ2 = λ1 + Math.atan2(Math.sin(θ) * Math.sin(δ) * Math.cos(φ1), Math.cos(δ) - Math.sin(φ1) * Math.sin(φ2));

        return {
            lng: this.toDegrees(λ2),
            lat: this.toDegrees(φ2)
        };
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.boundaryCache.clear();
        this.wuhanThirdRingBoundary = null;
    }
}

// 创建单例
const boundaryService = new BoundaryService();
export default boundaryService;
