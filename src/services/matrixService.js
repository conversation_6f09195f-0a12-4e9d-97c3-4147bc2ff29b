import { supabase } from './supabase.js';

/**
 * 矩阵标记数据库服务
 * 用于与Supabase数据库中的amap_matrix表进行交互
 */

/**
 * 获取所有矩阵标记
 * @returns {Promise<Object>} 包含success状态和data的对象
 */
export async function getAllMatrixMarkers() {
    try {
        const { data, error } = await supabase.from('amap_matrix').select('*').order('create_time', { ascending: false });

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('获取矩阵标记失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 根据ID获取单个矩阵标记
 * @param {string} id - 矩阵标记ID (格式: matrix_x_y)
 * @returns {Promise<Object>} 包含success状态和data的对象
 */
export async function getMatrixMarkerById(id) {
    try {
        const { data, error } = await supabase.from('amap_matrix').select('*').eq('id', id).single();

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error(`获取矩阵标记(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 根据网格索引获取矩阵标记
 * @param {number} gridX - X轴网格索引
 * @param {number} gridY - Y轴网格索引
 * @returns {Promise<Object>} 包含success状态和data的对象
 */
export async function getMatrixMarkerByGrid(gridX, gridY) {
    try {
        const { data, error } = await supabase.from('amap_matrix').select('*').eq('grid_index->x', gridX).eq('grid_index->y', gridY).single();

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error(`获取矩阵标记(网格: ${gridX},${gridY})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 获取指定区域内的矩阵标记
 * @param {Object} bounds - 边界对象 {north, south, east, west}
 * @returns {Promise<Object>} 包含success状态和data的对象
 */
export async function getMatrixMarkersInBounds(bounds) {
    try {
        const { data, error } = await supabase.from('amap_matrix').select('*').gte('gcj->lng', bounds.west).lte('gcj->lng', bounds.east).gte('gcj->lat', bounds.south).lte('gcj->lat', bounds.north);

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('获取区域内矩阵标记失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 根据状态获取矩阵标记
 * @param {string} status - 状态 ('available', 'captured', 'processing', 'unavailable')
 * @returns {Promise<Object>} 包含success状态和data的对象
 */
export async function getMatrixMarkersByStatus(status) {
    try {
        const { data, error } = await supabase.from('amap_matrix').select('*').eq('status', status).order('create_time', { ascending: false });

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error(`获取状态为${status}的矩阵标记失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 创建单个矩阵标记
 * @param {Object} markerData - 矩阵标记数据
 * @returns {Promise<Object>} 包含success状态和data的对象
 */
export async function createMatrixMarker(markerData) {
    try {
        const { data, error } = await supabase.from('amap_matrix').insert([markerData]).select();

        if (error) throw error;
        return { success: true, data: data[0] };
    } catch (error) {
        console.error('创建矩阵标记失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 批量创建矩阵标记
 * @param {Array} markersData - 矩阵标记数据数组
 * @returns {Promise<Object>} 包含success状态和data的对象
 */
export async function createMatrixMarkers(markersData) {
    try {
        // Supabase批量插入有限制，分批处理
        const batchSize = 100;
        const results = [];

        for (let i = 0; i < markersData.length; i += batchSize) {
            const batch = markersData.slice(i, i + batchSize);

            const { data, error } = await supabase.from('amap_matrix').insert(batch).select();

            if (error) throw error;
            results.push(...data);
        }

        return { success: true, data: results };
    } catch (error) {
        console.error('批量创建矩阵标记失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 更新矩阵标记
 * @param {string} id - 矩阵标记ID
 * @param {Object} updateData - 更新的数据
 * @returns {Promise<Object>} 包含success状态和data的对象
 */
export async function updateMatrixMarker(id, updateData) {
    try {
        // 排除id字段，避免更新主键
        const { id: _, ...dataToUpdate } = updateData;
        void _;

        const { data, error } = await supabase.from('amap_matrix').update(dataToUpdate).eq('id', id).select();

        if (error) throw error;
        return { success: true, data: data[0] };
    } catch (error) {
        console.error(`更新矩阵标记(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 更新矩阵标记状态
 * @param {string} id - 矩阵标记ID
 * @param {string} status - 新状态
 * @returns {Promise<Object>} 包含success状态和data的对象
 */
export async function updateMatrixMarkerStatus(id, status) {
    try {
        const { data, error } = await supabase
            .from('amap_matrix')
            .update({
                status,
                update_time: new Date().toISOString()
            })
            .eq('id', id)
            .select();

        if (error) throw error;
        return { success: true, data: data[0] };
    } catch (error) {
        console.error(`更新矩阵标记状态(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 批量更新矩阵标记状态
 * @param {Array} ids - 矩阵标记ID数组
 * @param {string} status - 新状态
 * @returns {Promise<Object>} 包含success状态和data的对象
 */
export async function updateMatrixMarkersStatus(ids, status) {
    try {
        const { data, error } = await supabase
            .from('amap_matrix')
            .update({
                status,
                update_time: new Date().toISOString()
            })
            .in('id', ids)
            .select();

        if (error) throw error;
        return { success: true, data };
    } catch (error) {
        console.error('批量更新矩阵标记状态失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 删除矩阵标记
 * @param {string} id - 矩阵标记ID
 * @returns {Promise<Object>} 包含success状态的对象
 */
export async function deleteMatrixMarker(id) {
    try {
        const { error } = await supabase.from('amap_matrix').delete().eq('id', id);

        if (error) throw error;
        return { success: true };
    } catch (error) {
        console.error(`删除矩阵标记(ID: ${id})失败:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * 批量删除矩阵标记
 * @param {Array} ids - 矩阵标记ID数组
 * @returns {Promise<Object>} 包含success状态的对象
 */
export async function deleteMatrixMarkers(ids) {
    try {
        const { error } = await supabase.from('amap_matrix').delete().in('id', ids);

        if (error) throw error;
        return { success: true };
    } catch (error) {
        console.error('批量删除矩阵标记失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 清空所有矩阵标记
 * @returns {Promise<Object>} 包含success状态的对象
 */
export async function clearAllMatrixMarkers() {
    try {
        const { error } = await supabase.from('amap_matrix').delete().neq('id', ''); // 删除所有记录

        if (error) throw error;
        return { success: true };
    } catch (error) {
        console.error('清空矩阵标记失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 检查矩阵数据是否已存在
 * @returns {Promise<Object>} 包含success状态和count的对象
 */
export async function checkMatrixDataExists() {
    try {
        const { count, error } = await supabase.from('amap_matrix').select('*', { count: 'exact', head: true });

        if (error) throw error;
        return { success: true, count, exists: count > 0 };
    } catch (error) {
        console.error('检查矩阵数据失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 获取矩阵统计信息
 * @returns {Promise<Object>} 包含success状态和统计数据的对象
 */
export async function getMatrixStatistics() {
    try {
        // 获取总数
        const { count: total, error: totalError } = await supabase.from('amap_matrix').select('*', { count: 'exact', head: true });

        if (totalError) throw totalError;

        // 获取各状态的数量
        const statusCounts = {};
        const statuses = ['planned', 'captured']; // 使用数据库约束允许的状态值

        for (const status of statuses) {
            const { count, error } = await supabase.from('amap_matrix').select('*', { count: 'exact', head: true }).eq('status', status);

            if (error) throw error;
            statusCounts[status] = count;
        }

        return {
            success: true,
            data: {
                total,
                statusCounts,
                lastUpdated: new Date().toISOString()
            }
        };
    } catch (error) {
        console.error('获取矩阵统计信息失败:', error);
        return { success: false, error: error.message };
    }
}

/**
 * 将gridService生成的数据转换为数据库格式
 * @param {Array} gridPoints - gridService生成的网格点数组
 * @returns {Array} 数据库格式的矩阵标记数组
 */
export function convertGridPointsToDbFormat(gridPoints) {
    return gridPoints.map((point) => ({
        id: point.id,
        custom_gcj: point.custom_gcj,
        gps: point.gps,
        gcj: point.gcj,
        grid_index: point.grid_index,
        type: point.type || 'matrix',
        status: point.status || 'planned', // 修正状态值，使用数据库约束允许的值
        grid_size: point.grid_size || 1000,
        boundary_region: point.boundary_region || 'wuhan_3rd_ring',
        metadata: point.metadata || null,
        captured_time: point.captured_time || null,
        pano_url: point.pano_url || null,
        notes: point.notes || null
    }));
}

/**
 * 将数据库格式转换为前端使用的格式
 * @param {Array} dbMarkers - 数据库格式的矩阵标记数组
 * @returns {Array} 前端格式的矩阵标记数组
 */
export function convertDbMarkersToFrontendFormat(dbMarkers) {
    return dbMarkers.map((marker) => ({
        id: marker.id,
        gridIndex: marker.grid_index, // 直接使用 JSONB 字段
        gps: marker.gps, // 直接使用 JSONB 字段
        gcj: marker.gcj, // 直接使用 JSONB 字段
        custom_gcj: marker.custom_gcj, // 直接使用 JSONB 字段
        position: marker.gcj, // 使用 gcj 坐标作为 position
        status: marker.status,
        type: marker.type,
        grid_size: marker.grid_size,
        boundary_region: marker.boundary_region,
        metadata: marker.metadata,
        captured_time: marker.captured_time,
        pano_url: marker.pano_url,
        notes: marker.notes,
        createTime: marker.create_time,
        updateTime: marker.update_time
    }));
}
