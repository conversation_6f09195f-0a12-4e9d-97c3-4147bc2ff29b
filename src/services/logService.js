/**
 * 日志服务
 */

class LogService {
    constructor() {
        this.logs = [];
        this.loadLogs();
    }

    // 从localStorage加载日志
    loadLogs() {
        try {
            const savedLogs = localStorage.getItem('fangmap_logs');
            if (savedLogs) {
                this.logs = JSON.parse(savedLogs);
            }
        } catch (error) {
            console.error('加载日志失败:', error);
            this.logs = [];
        }
    }

    // 保存日志到localStorage
    saveLogs() {
        try {
            localStorage.setItem('fangmap_logs', JSON.stringify(this.logs));
        } catch (error) {
            console.error('保存日志失败:', error);
        }
    }

    // 添加日志
    addLog(type, title, details) {
        const log = {
            id: Date.now() + Math.random(),
            type: type, // 'info', 'success', 'warning', 'error'
            title: title,
            details: details,
            timestamp: new Date().toISOString(),
            formattedTime: new Date().toLocaleString('zh-CN')
        };

        this.logs.unshift(log); // 最新的在前面
        
        // 限制日志数量，最多保存100条
        if (this.logs.length > 100) {
            this.logs = this.logs.slice(0, 100);
        }

        this.saveLogs();
        return log;
    }

    // 获取所有日志
    getAllLogs() {
        return this.logs;
    }

    // 获取特定类型的日志
    getLogsByType(type) {
        return this.logs.filter(log => log.type === type);
    }

    // 清除所有日志
    clearLogs() {
        this.logs = [];
        this.saveLogs();
    }

    // 删除特定日志
    deleteLog(id) {
        this.logs = this.logs.filter(log => log.id !== id);
        this.saveLogs();
    }

    // 记录地理编码操作
    logGeocoding(action, results) {
        const details = {
            action: action,
            totalProcessed: results.totalProcessed || 0,
            successCount: results.successCount || 0,
            failCount: results.failCount || 0,
            successRate: results.successRate || '0%',
            items: results.items || []
        };

        return this.addLog('info', `地理编码操作: ${action}`, details);
    }

    // 记录错误
    logError(title, error) {
        const details = {
            error: error.message || error,
            stack: error.stack || null
        };

        return this.addLog('error', title, details);
    }

    // 记录成功操作
    logSuccess(title, details) {
        return this.addLog('success', title, details);
    }

    // 记录警告
    logWarning(title, details) {
        return this.addLog('warning', title, details);
    }
}

// 创建单例实例
const logService = new LogService();

export default logService;
