.layout-main-container {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 3.5rem);
    justify-content: space-between;
    padding: 3.5rem 0.5rem 0rem 0rem;
    margin-bottom: 0rem;
    transition: margin-left var(--layout-section-transition-duration);
    background-color: rgba(255, 255, 255, 0);
    overflow: hidden;
}

.layout-main {
    flex: 1 1 auto;
    //padding-bottom: 0rem;
}
