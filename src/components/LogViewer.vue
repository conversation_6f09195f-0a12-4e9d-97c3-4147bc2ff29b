<template>
    <div class="log-viewer">
        <div class="log-header">
            <h3>操作日志</h3>
            <div class="log-actions">
                <Button 
                    label="清除所有日志" 
                    icon="pi pi-trash" 
                    severity="danger" 
                    size="small"
                    @click="clearAllLogs"
                />
            </div>
        </div>

        <div v-if="logs.length === 0" class="no-logs">
            <i class="pi pi-info-circle"></i>
            <p>暂无操作日志</p>
        </div>

        <div v-else class="log-list">
            <div 
                v-for="log in logs" 
                :key="log.id" 
                class="log-item"
                :class="getLogClass(log.type)"
                @click="showLogDetail(log)"
            >
                <div class="log-item-header">
                    <div class="log-title">
                        <i :class="getLogIcon(log.type)"></i>
                        <span>{{ log.title }}</span>
                    </div>
                    <div class="log-time">{{ log.formattedTime }}</div>
                </div>
                <div class="log-summary">
                    <span v-if="log.details.totalProcessed">
                        处理: {{ log.details.totalProcessed }}条
                    </span>
                    <span v-if="log.details.successCount">
                        成功: {{ log.details.successCount }}条
                    </span>
                    <span v-if="log.details.failCount">
                        失败: {{ log.details.failCount }}条
                    </span>
                    <span v-if="log.details.successRate">
                        成功率: {{ log.details.successRate }}
                    </span>
                </div>
            </div>
        </div>

        <!-- 日志详情对话框 -->
        <Dialog 
            v-model:visible="detailVisible" 
            modal 
            header="日志详情" 
            :style="{ width: '70vw', maxHeight: '80vh' }"
            :maximizable="true"
        >
            <div v-if="selectedLog" class="log-detail">
                <div class="detail-header">
                    <h4>{{ selectedLog.title }}</h4>
                    <span class="detail-time">{{ selectedLog.formattedTime }}</span>
                </div>

                <div class="detail-content">
                    <div v-if="selectedLog.details.action" class="detail-section">
                        <strong>操作类型:</strong> {{ selectedLog.details.action }}
                    </div>

                    <div v-if="selectedLog.details.totalProcessed" class="detail-section">
                        <strong>处理统计:</strong>
                        <ul>
                            <li>总处理数: {{ selectedLog.details.totalProcessed }}</li>
                            <li>成功数: {{ selectedLog.details.successCount }}</li>
                            <li>失败数: {{ selectedLog.details.failCount }}</li>
                            <li>成功率: {{ selectedLog.details.successRate }}</li>
                        </ul>
                    </div>

                    <div v-if="selectedLog.details.items && selectedLog.details.items.length > 0" class="detail-section">
                        <strong>处理详情:</strong>
                        <div class="items-list">
                            <div 
                                v-for="(item, index) in selectedLog.details.items" 
                                :key="index"
                                class="item-detail"
                                :class="{ 'success': item.success, 'failed': !item.success }"
                            >
                                <div class="item-header">
                                    <span class="item-name">{{ item.name }}</span>
                                    <span class="item-status">
                                        <i :class="item.success ? 'pi pi-check' : 'pi pi-times'"></i>
                                        {{ item.success ? '成功' : '失败' }}
                                    </span>
                                </div>
                                <div class="item-info">
                                    <span v-if="item.district">区域: {{ item.district }}</span>
                                    <span v-if="item.location">位置: {{ item.location }}</span>
                                    <span v-if="item.coordinates">
                                        坐标: {{ item.coordinates.lng }}, {{ item.coordinates.lat }}
                                    </span>
                                    <span v-if="item.inDistrict !== undefined">
                                        区域验证: {{ item.inDistrict ? '✓ 在区域内' : '✗ 不在区域内' }}
                                    </span>
                                </div>
                                <div v-if="item.error" class="item-error">
                                    错误: {{ item.error }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="selectedLog.details.error" class="detail-section error-section">
                        <strong>错误信息:</strong>
                        <pre>{{ selectedLog.details.error }}</pre>
                        <div v-if="selectedLog.details.stack" class="stack-trace">
                            <strong>堆栈跟踪:</strong>
                            <pre>{{ selectedLog.details.stack }}</pre>
                        </div>
                    </div>
                </div>
            </div>
        </Dialog>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useConfirm } from 'primevue/useconfirm';
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';
import logService from '@/services/logService';

// 响应式数据
const logs = ref([]);
const detailVisible = ref(false);
const selectedLog = ref(null);
const confirm = useConfirm();

// 加载日志
const loadLogs = () => {
    logs.value = logService.getAllLogs();
};

// 获取日志样式类
const getLogClass = (type) => {
    return `log-${type}`;
};

// 获取日志图标
const getLogIcon = (type) => {
    const icons = {
        info: 'pi pi-info-circle',
        success: 'pi pi-check-circle',
        warning: 'pi pi-exclamation-triangle',
        error: 'pi pi-times-circle'
    };
    return icons[type] || 'pi pi-info-circle';
};

// 显示日志详情
const showLogDetail = (log) => {
    selectedLog.value = log;
    detailVisible.value = true;
};

// 清除所有日志
const clearAllLogs = () => {
    confirm.require({
        message: '确定要清除所有日志吗？此操作不可撤销。',
        header: '确认清除',
        icon: 'pi pi-exclamation-triangle',
        rejectClass: 'p-button-secondary p-button-outlined',
        rejectLabel: '取消',
        acceptLabel: '确定',
        accept: () => {
            logService.clearLogs();
            loadLogs();
        }
    });
};

// 组件挂载时加载日志
onMounted(() => {
    loadLogs();
});

// 暴露刷新方法给父组件
defineExpose({
    refresh: loadLogs
});
</script>

<style scoped>
.log-viewer {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.log-header h3 {
    margin: 0;
    color: #333;
}

.no-logs {
    text-align: center;
    padding: 40px;
    color: #666;
}

.no-logs i {
    font-size: 3rem;
    margin-bottom: 10px;
    color: #ccc;
}

.log-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.log-item {
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid;
    cursor: pointer;
    transition: all 0.2s;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.log-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.log-info { border-left-color: #2196F3; }
.log-success { border-left-color: #4CAF50; }
.log-warning { border-left-color: #FF9800; }
.log-error { border-left-color: #F44336; }

.log-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.log-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    color: #333;
}

.log-time {
    font-size: 12px;
    color: #666;
}

.log-summary {
    display: flex;
    gap: 15px;
    font-size: 13px;
    color: #666;
}

.log-detail {
    padding: 10px 0;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.detail-header h4 {
    margin: 0;
    color: #333;
}

.detail-time {
    font-size: 14px;
    color: #666;
}

.detail-section {
    margin-bottom: 20px;
}

.detail-section strong {
    display: block;
    margin-bottom: 8px;
    color: #333;
}

.detail-section ul {
    margin: 8px 0;
    padding-left: 20px;
}

.items-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.item-detail {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.item-detail:last-child {
    border-bottom: none;
}

.item-detail.success {
    background: #f6ffed;
}

.item-detail.failed {
    background: #fff2f0;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.item-name {
    font-weight: bold;
    color: #333;
}

.item-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
}

.item-status .pi-check {
    color: #52c41a;
}

.item-status .pi-times {
    color: #ff4d4f;
}

.item-info {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    font-size: 12px;
    color: #666;
}

.item-error {
    margin-top: 8px;
    padding: 8px;
    background: #ffebee;
    border-radius: 4px;
    font-size: 12px;
    color: #c62828;
}

.error-section pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    overflow-x: auto;
}

.stack-trace {
    margin-top: 10px;
}
</style>
