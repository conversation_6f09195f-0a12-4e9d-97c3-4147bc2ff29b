<template>
    <Transition name="slide">
        <div v-if="visible" class="doc-editor">
            <div v-if="visible" class="editor-menubar">
                <!-- <button type="button" class="layout-topbar-action" @click="toggleDarkMode">
                    <i :class="['pi', { 'pi-moon': isDarkTheme, 'pi-sun': !isDarkTheme }]"></i>
                </button> -->
                <div class="editor-menubar-center">
                    <div class="editor-menubar-group">
                        <Button severity="success" rounded text @click="editor.chain().focus().toggleHighlight().run()">
                            <template #icon>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-highlighter w-3.5 h-3.5"
                                >
                                    <path d="m9 11-6 6v3h9l3-3"></path>
                                    <path d="m22 12-4.6 4.6a2 2 0 0 1-2.8 0l-5.2-5.2a2 2 0 0 1 0-2.8L14 4"></path>
                                </svg>
                            </template>
                        </Button>
                        <Button severity="secondary" rounded text @click="toggle">
                            <template #icon>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-link w-3.5 h-3.5"
                                >
                                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                                    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                                </svg>
                            </template>
                        </Button>
                    </div>
                    <div class="editor-menubar-divider"></div>
                    <div class="editor-menubar-group">
                        <Button severity="secondary" rounded text @click="toggle">
                            <template #icon> </template>
                        </Button>
                        <Button severity="secondary" rounded text @click="toggle">
                            <template #icon> </template>
                        </Button>
                        <Button severity="secondary" text @click="toggle">
                            <template #icon>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-image-up w-3.5 h-3.5"
                                >
                                    <path d="M10.3 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10l-3.1-3.1a2 2 0 0 0-2.814.014L6 21"></path>
                                    <path d="m14 19.5 3-3 3 3"></path>
                                    <path d="M17 22v-5.5"></path>
                                    <circle cx="9" cy="9" r="2"></circle>
                                </svg>
                            </template>
                        </Button>
                        <Popover ref="op">
                            <div>
                                <span class="font-medium block mb-2">照片链接</span>
                                <InputGroup>
                                    <InputText v-model="imageUrl" class="w-[25rem]"></InputText>
                                    <InputGroupAddon>
                                        <Button icon="pi pi-check" rounded text severity="success" @click="addImage"></Button>
                                    </InputGroupAddon>
                                </InputGroup>
                            </div>
                        </Popover>
                    </div>
                </div>
                <div class="editor-menubar-right">
                    <Button icon="pi pi-times" severity="secondary" size="small" rounded text @click="closeEditor"></Button>
                </div>
            </div>
            <editor-content :editor="editor" class="tiptap prose prose-sm max-w-none" />
        </div>
    </Transition>
</template>

<script setup>
import { ref, watch, onBeforeUnmount, onUnmounted, onMounted, reactive } from 'vue';
import { usePageStore } from '@/stores/pageStore.js';
import { Editor, EditorContent } from '@tiptap/vue-3';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import { CountNode, ImageNode } from './extension.js';
import Placeholder from '@tiptap/extension-placeholder';
import Highlight from '@tiptap/extension-highlight';
import { supabase } from '@/services/supabase.js';

// 从父组件 Amap.vue 中接收的 props
const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    visible: {
        type: Boolean,
        default: false
    }
});

const iButtonConfig = reactive({
    i_image: false,
    i_video: false,
    i_bold: false,
    i_highlight: false,
    i_underline: false,
    i_strike: false,
    i_code: false,
    i_link: false,
    i_list: false,
    i_superscript: false,
    i_subscript: false,
    i_quote: false,
    i_codeblock: false,
    i_table: false,
    i_horizontalrule: false,
    i_undo: false,
    i_redo: false,
    i_italic: false,
    i_fullscreen: false
});

const emit = defineEmits(['update:modelValue', 'close']);
const htmlContent = ref('');
const editor = ref(null);
const pageStore = usePageStore();
const firstImageUrl = ref('');

// 初始化 editor
// 创建新的编辑器实例
editor.value = new Editor({
    extensions: [
        StarterKit,
        CountNode,
        ImageNode,
        Image.configure({
            inline: true,
            allowBase64: true
        }),
        Placeholder.configure({
            placeholder: ({ node }) => {
                // console.log('节点类型:', node.type.name, node);
                if (node.attrs.level === 1) return 'H1';
                if (node.attrs.level === 2) return 'H2';
                if (node.type.name === 'paragraph' && editor.value.isEmpty) return '空文档！';
                if (node.type.name === 'paragraph') return '请输入内容';
                if (node.type.name === 'listItem') return '列表';
                if (node.type.name === 'orderedList') return '数字列表';
                if (node.type.name === 'bulletList') return '子弹列表';
                return '空文档！';
            }
        }),
        Highlight
    ],
    content: `<p>默认文章内容</p><p></p><img src="https://mmbiz.qpic.cn/sz_mmbiz_png/ibVnBEBMlgmyvF5FVI2lR1OicHtMm8jDPRpz3kFXDibAuHZbZxyWM59muQsrbdu470ibzhqtAONViaRu4x3iaVDE0jBw/640" alt="Image"></img><node-image marker="[object Object]" src="https://iabpcsmeijvjfgqhgocp.supabase.co/storage/v1/object/public/amap_images/original/1741075025022-ck6trt.jpeg" alt="IMG_0066.jpeg" marker_id="7619e191-67f3-474a-8953-285aba8b51b4" marker_type="photo"></node-image><p>asdsd</p><node-image marker="[object Object]" src="https://iabpcsmeijvjfgqhgocp.supabase.co/storage/v1/object/public/amap_images/original/1740904783395-z421s2.jpg" alt="润府_4_143平_116.jpg" marker_id="88c9a380-c280-44f7-825e-3dc6b47e61cf" marker_type="photo"></node-image><p></p><node-image marker="[object Object]" src="https://iabpcsmeijvjfgqhgocp.supabase.co/storage/v1/object/public/amap_images/original/IMG_0434-1743989525304-9d25zr.jpeg" alt="IMG_0434.jpeg" marker_id="b1ab0664-53b5-4797-8a03-f4a948f66f28" marker_type="photo"></node-image>`,
    onUpdate: ({ editor }) => {
        console.log('✍🏻: PageEditor: onUpdate ');
        htmlContent.value = editor.getHTML();
        // 发送更新后的内容到父组件 Amap.vue
        emit('update:modelValue', htmlContent.value);
        // emit('content-change', htmlContent.value);
        // console.log('🌏editor(PageEditor) ', editor);
        // console.log('$doc(PageEditor) nodes number', editor.$doc.children.length);
        // console.log('all nodes', editor.$nodes());
        // console.log('🌏$node-> heading 1 ', editor.$nodes('heading', { level: 1 }));
        // console.log('🌏$node-> heading', editor.$nodes('heading'));
        // console.log('🌏$node-> paragraph', editor.$nodes('paragraph'));
    }
});

const markerList = ref([]);
// 创建一个解析函数，用来解析 editor.value.options.element.innerHTML 中的 node-image 标签，并根据 marker_id 和 marker_type 的数组，从对应的 marker store 中获取 marker，最后把所有的 marker 存到 MarkerList 中
const parseNodeImages = () => {
    // console.log(markerList);
    return markerList.value;
};
void parseNodeImages();
watch(
    () => editor.value.$doc.children.length,
    (newValue) => {
        console.log('😄 : nodes number changed: ', newValue, editor.value.$doc);
        if (newValue > 2 && editor.value.$doc.children[1].textContent === '🌏') {
            console.log('!!!!!!!doc saved!!!!!!!');
        }
        // console.log('😄4: htmlContent: changed , send to Amap');
        // console.log('📒5: htmlContent -> ', newValue);
        // console.log('📒6: editor.commandManager.editor -> ', editor.value.commandManager.editor);
        // console.log('📒7: editor.options.content -> ', editor.value.options.content); 不变
    }
);

onMounted(() => {
    pageStore.setEditor(editor.value);

    // 监听粘贴事件
    const editorDom = editor.value.view.dom;
    editorDom.addEventListener('paste', handlePasteImage);
});

onBeforeUnmount(() => {
    if (editor.value) {
        editor.value.destroy();
    }
    // 解绑事件
    const editorDom = editor.value?.view?.dom;
    if (editorDom) {
        editorDom.removeEventListener('paste', handlePasteImage);
    }
    pageStore.setEditor(null);
    console.log('PageEditor: onUnmounted!!pageStore editor is null!!!!!!!!!!!!!!!!!!!!');
});

onUnmounted(() => {
    pageStore.setEditor(null);
    console.log('PageEditor: onUnmounted!!pageStore editor is null!!!!!!!!!!!!!!!!!!!!');
});

const closeEditor = () => {
    emit('close');
};
const imageUrl = ref('');
const addImage = () => {
    editor.value.chain().focus().setImage({ src: imageUrl.value }).run();
    console.log('addImage', imageUrl.value);
    imageUrl.value = '';
};
const op = ref(null);
const toggle = (event) => {
    op.value.toggle(event);
};

// watch(pageStore.editor.value, (newValue) => {
//     // console.log('🌏2: Amap: pageContent received from PageEditor ');
//     console.log('🌏PageEditor-> ', newValue);
//     console.log('🌏$doc-> ', newValue.$doc);
//     console.log('🌏$node-> heading 1 ', newValue.$nodes('heading 1'));
//     console.log('🌏$node-> heading', newValue.$nodes('heading'));
//     console.log('🌏$node-> paragraph', newValue.$nodes('paragraph'));
// });

const handlePasteImage = async (event) => {
    const items = event.clipboardData && event.clipboardData.items;
    if (!items) return;

    for (const item of items) {
        if (item.type.indexOf('image') === 0) {
            event.preventDefault();
            const file = item.getAsFile();
            if (file) {
                // 转为 webp
                const webpBlob = await imageFileToWebp(file);
                // 上传到 supabase
                const fileName = `pasted-${Date.now()}.webp`;
                const { data, error } = await supabase.storage.from('pageimage').upload(`webp/${fileName}`, webpBlob, {
                    contentType: 'image/webp',
                    upsert: true
                });
                if (error) {
                    console.error('上传失败', error);
                    return;
                }
                // 获取公开URL
                const { data: publicUrlData } = supabase.storage.from('pageimage').getPublicUrl(`webp/${fileName}`);
                const url = publicUrlData.publicUrl;
                // 插入图片
                editor.value.chain().focus().setImage({ src: url }).run();
            }
        }
    }
};

function imageFileToWebp(file) {
    return new Promise((resolve, reject) => {
        const img = new window.Image();
        const reader = new FileReader();
        reader.onload = function (e) {
            img.src = e.target.result;
        };
        img.onload = function () {
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0);
            canvas.toBlob(
                (blob) => {
                    if (blob) resolve(blob);
                    else reject(new Error('WebP 转换失败'));
                },
                'image/webp',
                0.3 // 可调整压缩率
            );
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}
</script>

<style lang="scss">
:root {
    --purple-light: #f3e8ff;
    --black: #222;
    --white: #fff;
    --gray-2: #eee;
    --gray-3: #ccc;
    --gray-4: #999;
    --purple: #9333ea;
}
.doc-editor {
    position: fixed;
    top: 105px;
    left: 14px;
    width: 25%;
    height: 86.8vh;
    padding: 0 0.4rem;
    bottom: 10px;
    background-color: #fff;
    box-shadow: -4px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}
.slide-enter-active,
.slide-leave-active {
    transition: transform 0.3s ease;
}
.slide-enter-from,
.slide-leave-to {
    transform: translateX(-100%);
}
.tiptap {
    flex: 1 1 auto;
    min-height: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.tiptap .ProseMirror {
    flex: 1 1 auto;
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
    padding-right: 10px;
    :first-child {
        margin-top: 0.2em;
    }

    /* List styles */
    ul,
    ol {
        padding: 0 1rem;
        margin: 0.25rem 0.5rem 0.25rem 0.4rem;

        li p {
            margin-top: 0.25em;
            margin-bottom: 0.25em;
        }
    }

    /* Heading styles */
    .tiptap.prose .ProseMirror h1,
    .tiptap.prose .ProseMirror h2,
    .tiptap.prose .ProseMirror h3,
    .tiptap.prose .ProseMirror h4,
    .tiptap.prose .ProseMirror h5,
    .tiptap.prose .ProseMirror h6 {
        line-height: 1.1;
        text-wrap: pretty;
    }

    .tiptap.prose .ProseMirror h1 {
        margin-top: 0.6em !important;
        margin-bottom: 0.4em !important;
        font-size: 1.4rem;
    }
    .tiptap.prose .ProseMirror h2 {
        margin-top: 0.3em !important;
        margin-bottom: 0.3em !important;
        font-size: 1.2rem;
    }
    .tiptap.prose .ProseMirror h3 {
        margin-top: 0.2em !important;
        margin-bottom: 0.2em !important;
        font-size: 1.1rem;
    }
    .tiptap.prose .ProseMirror h4 {
        margin-top: 0.2em !important;
        margin-bottom: 0.2em !important;
        font-size: 1rem;
    }
    .tiptap.prose .ProseMirror h5 {
        margin-top: 0.1em !important;
        margin-bottom: 0.1em !important;
        font-size: 1rem;
    }
    .tiptap.prose .ProseMirror h6 {
        margin-top: 0.1em !important;
        margin-bottom: 0.1em !important;
        font-size: 1rem;
    }

    /* Code and preformatted text styles */
    code {
        background-color: var(--purple-light);
        border-radius: 0.4rem;
        color: var(--black);
        font-size: 0.85rem;
        padding: 0.25em 0.3em;
        white-space: normal;
        word-break: break-word;
    }

    pre {
        background: var(--black);
        border-radius: 0.5rem;
        color: var(--white);
        font-family: 'JetBrainsMono', monospace;
        margin: 0.75rem 0;
        padding: 0.75rem 1rem;
        width: 100%;
        overflow: visible;

        code {
            background: none;
            color: inherit;
            font-size: 0.8rem;
            padding: 0;
            white-space: pre-wrap;
            word-break: break-word;
            display: block;
            width: 100%;
        }
    }

    blockquote {
        border-left: 3px solid var(--gray-3);
        margin: 1.5rem 0;
        padding-left: 1rem;
    }

    hr {
        border: none;
        border-top: 1px solid #86efac;
        margin: 1rem 0;
    }

    /* Placeholder (at the top) */
    p.is-editor-empty:first-child::before {
        color: #86efac;
        content: attr(data-placeholder);
        float: left;
        height: 0;
        pointer-events: none;
    }

    /* Placeholder (on every new line) */
    .is-empty::before {
        color: var(--gray-4);
        content: attr(data-placeholder);
        float: left;
        height: 0;
        pointer-events: none;
    }
}
.tiptap .ProseMirror h1 {
    font-size: 1.4rem;
}
.tiptap .ProseMirror:focus {
    outline: none !important;
    box-shadow: none !important;
}
.tiptap.prose .ProseMirror p {
    margin-top: 0.2em !important;
    margin-bottom: 0.2em !important;
}
/* 移除下面这些重复的 h1-h6 margin-top 设置 */
/*
.tiptap.prose .ProseMirror h1 {
    margin-top: 0.6em !important;
}
.tiptap.prose .ProseMirror h2 {
    margin-top: 0.5em !important;
}
.tiptap.prose .ProseMirror h3 {
    margin-top: 0.4em !important;
}
.tiptap.prose .ProseMirror h4 {
    margin-top: 0.3em !important;
}
.tiptap.prose .ProseMirror h5 {
    margin-top: 0.2em !important;
}
.tiptap.prose .ProseMirror h6 {
    margin-top: 0.1em !important;
}
*/
.editor-menubar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0rem 0.1rem 0rem 0.1rem;
    background: #fff;
    border-bottom: 1px solid #eee;
    position: relative;
    margin-bottom: 0.5rem;

    .editor-menubar-center {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        gap: 0rem;
    }

    .editor-menubar-group {
        display: flex;
        align-items: center;
        gap: 0rem;
    }

    .editor-menubar-divider {
        width: 1px;
        height: 24px;
        background: #e0e0e0;
        margin: 0 0.2rem;
    }

    .editor-menubar-right {
        margin-left: 1.5rem;
        display: flex;
        align-items: center;
    }
}
</style>
