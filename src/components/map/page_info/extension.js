import { mergeAttributes, Node } from '@tiptap/core';
import { VueNodeViewRenderer } from '@tiptap/vue-3';

import NodeCount from './NodeCount.vue';
import NodeImage from './NodeImage.vue';

export const CountNode = Node.create({
    name: 'nodeCount',

    group: 'block',

    atom: true,

    addAttributes() {
        return {
            count: {
                default: 0
            }
        };
    },

    parseHTML() {
        return [
            {
                tag: 'node-count'
            }
        ];
    },

    renderHTML({ HTMLAttributes }) {
        return ['node-count', mergeAttributes(HTMLAttributes)];
    },

    addNodeView() {
        return VueNodeViewRenderer(NodeCount);
    }
});

export const ImageNode = Node.create({
    name: 'nodeImage',

    group: 'block',

    atom: true,

    addAttributes() {
        return {
            marker: {
                default: null
            },
            src: {
                default: null
            },
            alt: {
                default: '照片预览'
            },
            marker_id: {
                default: null
            },
            marker_type: {
                default: null
            }
        };
    },

    parseHTML() {
        return [
            {
                tag: 'node-image'
            }
        ];
    },

    renderHTML({ HTMLAttributes }) {
        return ['node-image', mergeAttributes(HTMLAttributes)];
    },

    addNodeView() {
        return VueNodeViewRenderer(NodeImage);
    }
});
