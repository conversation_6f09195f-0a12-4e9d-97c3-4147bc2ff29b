<template>
    <node-view-wrapper>
        <div class="content">
            <div>
                <label> {{ markerData?.name ? '✅ NodeImage ' + markerData?.name : '❌ 非 NodeImage ' + props.node.attrs.alt }} </label>
                <div class="photo-preview">
                    <img :src="photoSrc" :alt="node.attrs.alt" class="preview-image" />
                    <div class="preview-actions">
                        <Button icon="pi pi-compass" class="p-button-rounded p-button-info p-button-sm locate-button" @click="handleLocate" />
                    </div>
                </div>
            </div>
        </div>
    </node-view-wrapper>
</template>

<script setup>
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3';
import { computed, onMounted, watch, ref } from 'vue';
import { useMapStore } from '@/stores/mapStore';
import { usePhotoStore } from '@/stores/photoStore';

const props = defineProps(nodeViewProps);

// 从节点属性中获取 marker 数据
const markerData = ref(props.node.attrs.marker || null);
const photoStore = usePhotoStore();

// 更新图片源
const photoSrc = computed(() => {
    // 优先使用 marker 中的 photo_url
    // if (markerData.value?.photo_url) {
    //     return markerData.value.photo_url;
    // }

    if (props.node.attrs.marker && props.node.attrs.marker.photo_url) {
        return props.node.attrs.marker.photo_url;
    }
    // 如果有直接设置的 src，则使用它
    if (props.node.attrs.src) {
        return props.node.attrs.src;
    }
    // 默认图片
    return 'https://iabpcsmeijvjfgqhgocp.supabase.co/storage/v1/object/public/amap_images/original/IMG_0303-1743988681034-tad6sx.jpeg';
});

// 确保 src 属性与 marker 的 photo_url 保持同步
onMounted(() => {
    console.log('🌇 onMounted: node.attrs.src : ', props.node.attrs.src);
    console.log('🌇 onMounted: node.attrs.alt : ', props.node.attrs.alt);
    if (markerData.value?.photo_url && !props.node.attrs.src) {
        props.updateAttributes({
            src: markerData.value.photo_url
        });
        console.log('🌇 更新 photo_url 属性: ', markerData.value.photo_url);
        console.log('🌇 更新 node.attrs.src 属性: ', props.node.attrs.src);
        console.log('🌇 更新 props.node 属性: ', props.node);
    }
});

// 当 marker 数据变化时更新 src 属性
watch(
    () => markerData.value?.photo_url,
    (newUrl) => {
        if (newUrl && newUrl !== props.node.attrs.src) {
            props.updateAttributes({
                src: newUrl
            });
        }
    }
);

// 处理定位功能
const handleLocate = async () => {
    const mapStore = useMapStore();
    const map = mapStore.getMapInstance();

    // 如果是 object 则标明这个 nodeImage 是已有从数据库中读取的标签
    if (markerData.value === '[object Object]' || !markerData.value) {
        console.log('从 store 中获取 marker 数据');
        const markerId = props.node.attrs.marker_id;
        const markerType = props.node.attrs.marker_type;

        if (markerType === 'photo' && markerId) {
            try {
                // 从 PhotoStore 获取对应的 marker 数据
                await photoStore.fetchPhotoById(markerId);
                const photoMarker = photoStore.selectedPhoto;
                if (photoMarker) {
                    markerData.value = photoMarker;
                    console.log('获取到的 photo marker:', photoMarker);
                }
            } catch (error) {
                console.error('获取 photo marker 失败:', error);
                return;
            }
        }
    }

    if (map && markerData.value?.gcj) {
        // 设置地图中心点
        map.setCenter([markerData.value.gcj.lng, markerData.value.gcj.lat]);
        map.setZoom(16);

        // 可选：添加高亮效果
        const AMap = window.AMap;
        if (AMap) {
            // 创建自定义 marker 内容
            const markerContent = document.createElement('div');
            markerContent.className = 'custom-marker';
            markerContent.innerHTML = `
                <div class="marker-image-container">
                    <img src="${markerData.value.thumbnail_url || markerData.value.photo_url}" class="marker-image" />
                </div>
                <div class="marker-point"></div>
            `;

            const highlightMarker = new AMap.Marker({
                position: [markerData.value.gcj.lng, markerData.value.gcj.lat],
                content: markerContent,
                anchor: 'bottom-center',
                offset: new AMap.Pixel(0, 0),
                zIndex: 110
            });

            // 添加样式
            const style = document.createElement('style');
            style.textContent = `
                .custom-marker {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                }
                .marker-image-container {
                    background: white;
                    padding: 4px;
                    border-radius: 8px;
                    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
                }
                .marker-image {
                    width: 48px;
                    height: 48px;
                    border-radius: 4px;
                    object-fit: cover;
                }
                .marker-point {
                    width: 8px;
                    height: 8px;
                    background: white;
                    border-radius: 50%;
                    margin-top: -2px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
            `;
            document.head.appendChild(style);

            highlightMarker.setMap(map);

            // 10秒后移除高亮和样式
            setTimeout(() => {
                highlightMarker.setMap(null);
                document.head.removeChild(style);
            }, 10000);
        }
    }
};
</script>

<style lang="scss">
.tiptap {
    /* Vue component */

    .content {
        margin: 0.1rem;
        padding: 0.1rem 0 0.1rem 0;
    }

    .photo-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0rem;
        position: relative;
        margin-top: 0rem;

        .preview-image {
            max-width: 100%;
            height: auto;
            margin-bottom: 0.1rem;
            border-radius: 0.5rem;
        }

        .preview-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }

        .locate-button {
            width: 2rem;
            height: 2rem;
            font-size: 0.85rem;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
    }
}
</style>
