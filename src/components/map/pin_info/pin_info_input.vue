<template>
    <div class="pin-info-input">
        <form @submit.prevent="handleSubmit">
            <div class="form-group">
                <label for="name">PIN标记名称 <span class="required">*</span></label>
                <InputText id="name" v-model="formData.name" :class="{ 'p-invalid': v$.name.$invalid && v$.name.$dirty }" />
                <small v-if="v$.name.$invalid && v$.name.$dirty" class="p-error">{{ v$.name.$errors[0].$message }}</small>
            </div>

            <div class="form-group">
                <label for="tag">标签</label>
                <Chips v-model="formData.tag" separator="," />
                <small class="helper-text">输入标签后按回车或逗号添加</small>
            </div>

            <div class="form-group">
                <label for="link">链接</label>
                <InputText id="link" v-model="formData.link" placeholder="http://" />
            </div>

            <div class="form-group">
                <label>位置信息</label>
                <div class="location-info">
                    <div class="coordinates">
                        <span>经度: {{ marker.value.custom_gcj.lng.toFixed(6) }}</span>
                        <span>纬度: {{ marker.value.custom_gcj.lat.toFixed(6) }}</span>
                    </div>
                    <div v-if="formData.full_address" class="address">
                        <i class="pi pi-map-marker"></i>
                        <ul>
                            <li>
                                <span>当前地址：{{ formData.full_address }}</span>
                            </li>
                            <li>
                                <span>城市：{{ formData.city }}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label for="name">海拔高度</label>
                <InputText id="altitude" v-model.number="formData.altitude" />
                <Slider v-model.number="formData.altitude" min="0" max="500" />
            </div>

            <div class="form-group">
                <label for="icon">图标</label>
                <div class="icon-selector">
                    <div v-for="(svg, key) in svgOptions" :key="key" class="icon-option" :class="{ selected: formData.icon === key }" @click="formData.icon = key">
                        <img :src="svg" class="svg-icon" />
                        <span>{{ key }}</span>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <Button type="submit" label="保存" icon="pi pi-check" class="p-button-success" :loading="loading" />
                <Button type="button" label="取消" icon="pi pi-times" class="p-button-secondary" @click="handleCancel" :disabled="loading" />
            </div>
        </form>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { useVuelidate } from '@vuelidate/core';
import { required } from '@vuelidate/validators';
import InputText from 'primevue/inputtext';
import Chips from 'primevue/chips';
import Button from 'primevue/button';
import { ICON_SVG } from '@/config/svg.js';
import { gcj02towgs84 } from '@/utils/coordinate';
import { getGeoInfo } from '@/utils/mapTool.js';

const props = defineProps({
    marker: {
        type: Object,
        required: true
    }
});
const geoInfo = ref(null);

const emit = defineEmits(['submit', 'cancel']);

const loading = ref(false);

// SVG 图标选项
const svgOptions = ICON_SVG;

// 表单数据
const formData = reactive({
    name: props.marker.value.name || '',
    tag: [],
    icon: 'PIN', // 默认使用 ICON_SVG.PIN
    link: '',
    full_address: props.marker.value.full_address || '',
    city: props.marker.value.city,
    altitude: 0,
    custom_gcj: {
        lng: props.marker.value.custom_gcj.lng,
        lat: props.marker.value.custom_gcj.lat
    }
});
// 添加 watch 监听 props.marker.value 的变化
watch(
    () => props.marker.value,
    (newValue) => {
        if (newValue) {
            //formData.name = newValue.name || '';
            formData.full_address = newValue.full_address || '';
            formData.city = newValue.city || '';
            // 可以根据需要更新其他字段
            console.log('Pin_formData: ', formData);
        }
    },
    { deep: true }
);

// 表单验证规则
const rules = {
    name: { required }
};

const v$ = useVuelidate(rules, formData);

// 初始化表单数据
onMounted(async () => {
    // 添加 async
    if (props.marker) {
        // 如果有地址信息，则设置地址
        if (props.marker.address) {
            console.log('props.marker.address:', props.marker.address);
            console.log('props.marker.value.address:', props.marker.value.address);
            formData.full_address = props.marker.address;
            console.log('pin info input formData:', formData.full_address);
        } else {
            // 等待地址获取完成
            try {
                geoInfo.value = await getGeoInfo(props.marker.lng, props.marker.lat);
            } catch (error) {
                console.error('获取地址失败:', error);
            }
        }
    }
});

// 提交表单
const handleSubmit = async () => {
    const isValid = await v$.value.$validate();
    if (!isValid) return;

    loading.value = true;

    try {
        // 如果地址为空，尝试获取地址
        if (!formData.full_address) {
            try {
                await getGeoInfo(props.marker.value.custom_gcj.lng, props.marker.value.custom_gcj.lat);
            } catch (e) {
                console.warn('自动获取地址失败:', e);
                // 地址获取失败不阻止表单提交
            }
        }

        // 构建完整的GPS数据
        let gcjtogps = { lng: props.marker.value.custom_gcj.lng, lat: props.marker.value.custom_gcj.lat };
        // 使用坐标转换工具进行转换（如果有）
        if (gcj02towgs84) {
            try {
                const converted = gcj02towgs84(gcjtogps.lng, gcjtogps.lat);
                gcjtogps = { lng: converted[0], lat: converted[1] };
            } catch (e) {
                console.warn('坐标转换失败:', e);
            }
        }

        // 构建提交数据
        const submitData = {
            name: formData.name,
            tag: tagString, // 使用逗号分隔的字符串
            collection_id: props.marker.value.collection_id ? props.marker.value.collection_id : '', // 确保 collection_id 存在
            link: formData.link,
            city: formData.city || '',
            full_address: formData.full_address || '',
            gps: gcjtogps,
            gcj: formData.gcj ? formData.gcj : formData.custom_gcj,
            custom_gcj: formData.custom_gcj,
            altitude: formData.altitude || 0,
            icon: formData.icon
        };

        console.log('提交新Pin标记数据:', submitData);
        emit('submit', submitData);
    } catch (error) {
        console.error('提交表单失败:', error);
        alert('提交表单失败：' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};
// 将标签数组转换为逗号分隔的字符串
const tagString = Array.isArray(formData.tag) ? formData.tag.join(',') : typeof formData.tag === 'string' ? formData.tag : '';
// 取消
const handleCancel = () => {
    emit('cancel');
};
</script>

<style lang="scss" scoped>
.pin-info-input {
    padding: 10px;

    .form-group {
        margin-bottom: 15px;

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;

            .required {
                color: red;
            }
        }

        .helper-text {
            display: block;
            color: #666;
            font-size: 0.8rem;
            margin-top: 3px;
        }
    }

    .location-info {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;

        .coordinates {
            display: flex;
            gap: 15px;
            margin-bottom: 5px;

            span {
                font-family: monospace;
            }
        }

        .address {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #666;

            i {
                color: #ff4d4f;
            }
        }
    }

    .icon-selector {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .icon-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
                background-color: #f0f0f0;
            }

            &.selected {
                border-color: #2196f3;
                background-color: #e3f2fd;
            }

            .svg-icon {
                width: 24px;
                height: 24px;
                margin-bottom: 5px;
            }

            span {
                font-size: 0.7rem;
                text-align: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                width: 100%;
                padding: 0 2px;
            }
        }
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
    }

    :deep(.p-inputtext),
    :deep(.p-chips) {
        width: 100%;
    }
}
</style>
@/utils/coordinate
