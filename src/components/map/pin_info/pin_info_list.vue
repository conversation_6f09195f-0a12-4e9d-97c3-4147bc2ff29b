<template>
    <div class="pin-info-list">
        <div class="list-header">
            <h3>
                引脚标记列表 <span v-if="items.length > 0" class="counter">({{ items.length }})</span>
            </h3>
            <div class="list-actions">
                <Button icon="pi pi-plus" class="p-button-success p-button-sm" title="添加新标记" @click="$emit('add-item')" />
                <Button icon="pi pi-refresh" class="p-button-info p-button-sm" title="刷新列表" @click="$emit('refresh')" />
                <Button icon="pi pi-trash" class="p-button-danger p-button-sm" :disabled="selectedItems.length === 0" title="删除选中项" @click="handleDeleteSelected" />
            </div>
        </div>

        <div class="filter-bar">
            <span class="p-input-icon-left">
                <i class="pi pi-search" />
                <InputText v-model="filters.global" placeholder="搜索..." />
            </span>
            <Dropdown v-model="filters.label" :options="labelOptions" optionLabel="name" optionValue="value" placeholder="标签筛选" class="label-filter" />
        </div>

        <DataTable
            :value="filteredItems"
            v-model:selection="selectedItems"
            dataKey="id"
            :paginator="true"
            :rows="10"
            :rowsPerPageOptions="[5, 10, 20]"
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
            responsiveLayout="scroll"
            :loading="loading"
            sortField="created_at"
            :sortOrder="-1"
            stripedRows
            rowHover
        >
            <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>
            <Column field="name" header="名称" sortable>
                <template #body="slotProps">
                    <span class="marker-name" :title="slotProps.data.full_address || ''"> {{ slotProps.data.name?.slice(0, 10) }}{{ slotProps.data.name?.length > 10 ? '...' : '' }} </span>
                </template>
            </Column>
            <Column field="created_at" header="创建时间" sortable style="width: 150px">
                <template #body="slotProps">
                    {{ new Date(slotProps.data.created_at).toLocaleDateString() }}
                </template>
            </Column>
            <Column field="city" header="城市" />
            <Column field="label" header="标签">
                <template #body="slotProps">
                    <div class="tag-container">
                        <Tag v-for="(tag, index) in getTagsArray(slotProps.data.label)" :key="index" :value="tag" :severity="getTagSeverity(tag)" />
                        <span v-if="getTagsArray(slotProps.data.label).length === 0" class="no-tags">无标签</span>
                    </div>
                </template>
            </Column>
            <Column header="操作" style="width: 100px">
                <template #body="slotProps">
                    <Button icon="pi pi-map-marker" class="p-button-rounded p-button-warning p-button-sm" title="定位到地图" @click="$emit('locate-item', slotProps.data)" />
                    <Button icon="pi pi-eye" class="p-button-rounded p-button-info p-button-sm" title="查看详情" @click="handleView(slotProps.data)" />
                    <Button icon="pi pi-pencil" class="p-button-rounded p-button-success p-button-sm" title="编辑" @click="handleEdit(slotProps.data)" />
                    <Button icon="pi pi-trash" class="p-button-rounded p-button-danger p-button-sm" title="删除" @click="handleDelete(slotProps.data.id)" />
                </template>
            </Column>
        </DataTable>

        <div v-if="filteredItems.length === 0" class="empty-list">
            <p>{{ items.length === 0 ? '暂无引脚标记' : '没有符合条件的标记' }}</p>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';
import Tag from 'primevue/tag';
import InputText from 'primevue/inputtext';
import Dropdown from 'primevue/dropdown';

const props = defineProps({
    items: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['select-item', 'edit-item', 'delete-items', 'refresh', 'add-item', 'locate-item']);

const selectedItems = ref([]);
const filters = ref({
    global: '',
    label: null
});

// 获取所有标签选项
const allTags = computed(() => {
    const tagSet = new Set();
    props.items.forEach((item) => {
        const tags = getTagsArray(item.label);
        tags.forEach((tag) => tagSet.add(tag));
    });
    return Array.from(tagSet).sort();
});

// 标签选项
const labelOptions = computed(() => {
    return [{ name: '全部', value: null }, ...allTags.value.map((tag) => ({ name: tag, value: tag }))];
});

// 将标签字段转换为数组
const getTagsArray = (label) => {
    if (!label) return [];
    if (Array.isArray(label)) return label;
    if (typeof label === 'string') {
        // 直接按逗号分割字符串
        return label
            .split(',')
            .map((tag) => tag.trim())
            .filter((tag) => tag);
    }
    return [];
};

// 过滤后的数据
const filteredItems = computed(() => {
    return props.items.filter((item) => {
        // 全局搜索
        const matchesGlobal =
            !filters.value.global ||
            item.name?.toLowerCase().includes(filters.value.global.toLowerCase()) ||
            item.full_address?.toLowerCase().includes(filters.value.global.toLowerCase()) ||
            item.city?.toLowerCase().includes(filters.value.global.toLowerCase()) ||
            getTagsArray(item.label).some((tag) => tag.toLowerCase().includes(filters.value.global.toLowerCase()));

        // 标签筛选
        const matchesLabel = !filters.value.label || getTagsArray(item.label).some((tag) => tag === filters.value.label);

        return matchesGlobal && matchesLabel;
    });
});

// 获取标签样式
const getTagSeverity = (tag) => {
    const tagMap = {
        景点: 'success',
        餐饮: 'warning',
        住宿: 'info',
        购物: 'danger',
        交通: 'primary'
    };

    return tagMap[tag] || 'secondary';
};

// 查看标记详情
const handleView = (item) => {
    emit('select-item', item);
};

// 编辑标记
const handleEdit = (item) => {
    emit('edit-item', item);
};

// 删除选中的标记
const handleDeleteSelected = () => {
    if (selectedItems.value.length > 0) {
        if (confirm(`确定要删除选定的 ${selectedItems.value.length} 个标记吗？此操作不可恢复。`)) {
            const ids = selectedItems.value.map((item) => item.id);
            emit('delete-items', ids);
            selectedItems.value = [];
        }
    }
};

// 删除单个标记
const handleDelete = (id) => {
    if (confirm(`确定要删除这个标记吗？此操作不可恢复。`)) {
        emit('delete-items', [id]);
    }
};
</script>

<style lang="scss" scoped>
.pin-info-list {
    padding: 10px;

    .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        h3 {
            margin: 0;
            font-size: 1.1rem;

            .counter {
                font-size: 0.9rem;
                color: #666;
                font-weight: normal;
            }
        }

        .list-actions {
            display: flex;
            gap: 5px;
        }
    }

    .filter-bar {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        gap: 10px;

        .p-input-icon-left {
            flex: 1;
        }

        .label-filter {
            width: 150px;
        }

        :deep(.p-inputtext) {
            width: 100%;
        }
    }

    .marker-name {
        font-weight: 500;
    }

    .tag-container {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        max-width: 200px;

        .no-tags {
            color: #999;
            font-style: italic;
            font-size: 0.9rem;
        }
    }

    .empty-list {
        text-align: center;
        padding: 20px;
        color: #666;
        font-style: italic;
    }
}
</style>
