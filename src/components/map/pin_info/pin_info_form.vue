<template>
    <div class="pin-info-form">
        <form @submit.prevent="handleSubmit">
            <div class="form-group">
                <label for="name">标记名称 <span class="required">*</span></label>
                <InputText id="name" v-model="formData.name" :class="{ 'p-invalid': v$.name.$invalid && v$.name.$dirty }" />
                <small v-if="v$.name.$invalid && v$.name.$dirty" class="p-error">{{ v$.name.$errors[0].$message }}</small>
            </div>

            <div class="form-group">
                <label for="tag">标签</label>
                <Chips v-model="formData.tag" separator="," />
                <small class="helper-text">输入标签后按回车或逗号添加</small>
            </div>

            <div class="form-group">
                <label for="link">链接</label>
                <InputText id="link" v-model="formData.link" placeholder="http://" />
            </div>

            <div class="form-group">
                <label>位置信息</label>
                <div class="location-info">
                    <div class="coordinates">
                        <!-- <span>经度: {{ formData.gcj.lng ? formData.gcj.lng : 'null' }}</span>
                        <span>纬度: {{ formData.gcj.lat ? formData.gcj.lat : 'null' }}</span> -->
                    </div>
                    <div v-if="formData.full_address" class="address">
                        <i class="pi pi-map-marker"></i>
                        <span>{{ formData.full_address }}</span>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="icon">图标</label>
                <div class="icon-selector">
                    <div v-for="(svg, key) in svgOptions" :key="key" class="icon-option" :class="{ selected: formData.icon === key }" @click="formData.icon = key">
                        <img :src="svg" class="svg-icon" />
                        <span>{{ key }}</span>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>自定义位置</label>
                <div class="custom-position-controls">
                    <Button type="button" label="设置自定义位置" icon="pi pi-map-marker" @click="enableCustomPosition" />
                    <div v-if="formData.custom_gps" class="custom-position-info">
                        <span>自定义位置: {{ formData.custom_gcj.lng }}, {{ formData.custom_gcj.lat }}</span>
                        <Button type="button" icon="pi pi-times" class="p-button-text p-button-danger" @click="formData.custom_gps = null" />
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>创建信息</label>
                <div class="meta-info">
                    <div class="meta-item">
                        <span class="meta-label">创建时间:</span>
                        <span class="meta-value">{{ formatDate(formData.create_time) }}</span>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <Button type="submit" label="更新" icon="pi pi-check" class="p-button-success" :loading="loading" />
                <Button type="button" label="删除" icon="pi pi-trash" class="p-button-danger" @click="confirmDelete" :disabled="loading" />
                <Button type="button" label="返回" icon="pi pi-arrow-left" class="p-button-secondary" @click="handleBackToList" :disabled="loading" />
            </div>
        </form>

        <!-- 删除确认对话框 -->
        <Dialog v-model:visible="showDeleteDialog" header="确认删除" :modal="true" :closable="true">
            <div class="delete-dialog-content">
                <p>确定要删除此引脚标记吗？此操作不可撤销。</p>
            </div>
            <template #footer>
                <Button label="确认删除" icon="pi pi-trash" class="p-button-danger" @click="handleDelete" :loading="deleting" />
                <Button label="取消" icon="pi pi-times" class="p-button-secondary" @click="showDeleteDialog = false" :disabled="deleting" />
            </template>
        </Dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useVuelidate } from '@vuelidate/core';
import { required } from '@vuelidate/validators';
import InputText from 'primevue/inputtext';
import Chips from 'primevue/chips';
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';
import { ICON_SVG } from '@/config/svg.js';

const props = defineProps({
    marker: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['update', 'delete', 'cancel', 'back-to-list', 'enable-custom-position']);

const loading = ref(false);
const deleting = ref(false);
const showDeleteDialog = ref(false);

// SVG 图标选项
const svgOptions = ICON_SVG;

// 构造表单数据结构
const formData = reactive({
    id: 'null',
    name: null,
    icon: 'PIN', // 默认使用 ICON_SVG.PIN
    tag: null,
    link: null,
    create_time: null,
    gps: null,
    gcj: null,
    custom_gcj: null,
    altitude: 0,
    city: null,
    full_address: null,
    collection_id: null
});

// 表单验证规则
const rules = {
    name: { required }
};

const v$ = useVuelidate(rules, formData);

// 初始化表单数据
onMounted(() => {
    console.log('收到的marker标记数据:', props.marker);
    console.log('初始化的formData:', formData);
    if (props.marker) {
        formData.id = props.marker.value.id;
        formData.name = props.marker.value.name || '';
        formData.icon = props.marker.value.icon || 'PIN'; // 默认使用 ICON_SVG.PIN
        formData.tag = props.marker.value.tag || '';
        formData.link = props.marker.value.link || '';
        formData.create_time = props.marker.value.create_time || '';
        formData.gps = props.marker.value.gps;
        formData.gcj = props.marker.value.gcj;
        formData.custom_gcj = props.marker.value.custom_gcj;
        formData.altitude = props.marker.value.altitude || 0;
        formData.city = props.marker.value.city || '';
        formData.full_address = props.marker.value.full_address || '';
        formData.collection_id = props.marker.value.collection_id || '';

        // 修改标签处理逻辑 - 从逗号分隔的字符串解析为数组
        if (props.marker.value.tag) {
            if (Array.isArray(props.marker.value.tag)) {
                formData.tag = props.marker.value.tag;
            } else if (typeof props.marker.value.tag === 'string') {
                // 直接按逗号分割字符串
                formData.tag = props.marker.value.tag
                    .split(',')
                    .map((tag) => tag.trim())
                    .filter((tag) => tag);
            } else {
                formData.tag = [];
            }
        } else {
            formData.tag = [];
        }
    }
    console.log('编辑过的表单数据:', formData);
});

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '未知时间';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// 启用自定义位置
const enableCustomPosition = () => {
    emit('enable-custom-position');
};

// 更新自定义位置
const updateCustomPosition = (e) => {
    formData.custom_gcj = {
        lng: e.tagert.lng,
        lat: e.tagert.lat
    };

    // 更新地址信息
    if (window.AMap && window.AMap.Geocoder) {
        const geocoder = new window.AMap.Geocoder();
        geocoder.getAddress([formData.custom_gps.lng, formData.custom_gps.lat], (status, result) => {
            if (status === 'complete' && result.info === 'OK') {
                const address = result.regeocode.formattedAddress;
                const addressComponent = result.regeocode.addressComponent;

                formData.full_address = address;
                formData.city = addressComponent.city || addressComponent.province;
            }
        });
    }
};
void updateCustomPosition;

// 提交表单
const handleSubmit = async () => {
    const isValid = await v$.value.$validate();
    if (!isValid) return;

    loading.value = true;

    try {
        // 将标签数组转换为逗号分隔的字符串
        const tagString = Array.isArray(formData.tag) ? formData.tag.join(',') : typeof formData.tag === 'string' ? formData.tag : '';

        // 构建提交数据
        const submitData = {
            id: formData.id,
            name: formData.name,
            icon: formData.icon,
            tag: tagString,
            link: formData.link,
            create_time: formData.create_time,
            gps: { ...formData.gps },
            gcj: { ...formData.gcj },
            custom_gcj: { ...formData.custom_gcj },
            altitude: formData.altitude,
            city: formData.city,
            full_address: formData.full_address,
            collection_id: formData.collection_id
        };

        // 日志输出用于调试
        console.log('提交更新的Pin标记数据:', submitData);

        emit('update', submitData);
    } catch (error) {
        console.error('更新标记失败:', error);
        // 可以添加失败的UI反馈
        alert('更新标记失败：' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

// 确认删除
const confirmDelete = () => {
    showDeleteDialog.value = true;
};

// 删除标记
const handleDelete = async () => {
    deleting.value = true;

    try {
        emit('delete', formData.id);
    } catch (error) {
        console.error('删除标记失败:', error);
    } finally {
        deleting.value = false;
        showDeleteDialog.value = false;
    }
};

// 返回列表
const handleBackToList = () => {
    emit('back-to-list');
};
</script>

<style lang="scss" scoped>
.pin-info-form {
    padding: 10px;

    .form-group {
        margin-bottom: 15px;

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;

            .required {
                color: red;
            }
        }

        .helper-text {
            display: block;
            color: #666;
            font-size: 0.8rem;
            margin-top: 3px;
        }
    }

    .location-info {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;

        .coordinates {
            display: flex;
            gap: 15px;
            margin-bottom: 5px;

            span {
                font-family: monospace;
            }
        }

        .address {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #666;

            i {
                color: #ff4d4f;
            }
        }
    }

    .icon-selector {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .icon-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
                background-color: #f0f0f0;
            }

            &.selected {
                border-color: #2196f3;
                background-color: #e3f2fd;
            }

            .svg-icon {
                width: 24px;
                height: 24px;
                margin-bottom: 5px;
            }

            span {
                font-size: 0.7rem;
                text-align: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                width: 100%;
                padding: 0 2px;
            }
        }
    }

    .custom-position-controls {
        margin-top: 5px;

        .custom-position-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 10px;
            padding: 8px;
            background-color: #f0f7ff;
            border-radius: 4px;
            border-left: 3px solid #2196f3;
        }
    }

    .meta-info {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;

        .meta-item {
            display: flex;
            margin-bottom: 5px;

            &:last-child {
                margin-bottom: 0;
            }

            .meta-label {
                width: 80px;
                color: #666;
            }

            .meta-value {
                font-weight: 500;
            }
        }
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
    }

    .delete-dialog-content {
        padding: 10px 0;
    }

    :deep(.p-inputtext),
    :deep(.p-chips) {
        width: 100%;
    }
}
</style>
