<template>
    <div class="map">
        <PageEditor :visible="pageEditorVisible" v-model="pageContent" @select-item="handleInfoWindowSelectItem" @close="closePageEditor" />
        <!-- <PageEditor :visible="pageEditorVisible" v-model="pageContent" @select-item="handleInfoWindowSelectItem" @content-change="handlePageContentChange"/> -->
        <!-- 添加Toast组件 -->
        <Toast />
        <div id="container" @click.stop>
            <!-- menu 菜单 -->
            <div class="map-overlay" style="position: relative; z-index: 1021">
                <Menubar :model="nestedMenuitems" position="top">
                    <template #end>
                        <div style="display: flex; align-items: center">
                            <div class="zoom-display">
                                <span> ZOOM: {{ mapStore.mapOptions.zoom }}</span>
                            </div>
                            <IconField iconPosition="left">
                                <InputIcon class="pi pi-search" />
                                <InputText type="text" placeholder="搜索地点" v-model="searchText" @keyup.enter="handleSearch" id="search-input" />
                            </IconField>
                        </div>
                    </template>
                </Menubar>
            </div>
            <!-- 控制面板 -->
            <div id="panel"></div>
            <!-- docker -->
            <Dock
                :model="dockItems"
                position="bottom"
                :pt="{
                    root: { class: 'mb-5' },
                    icon: { class: 'drop-shadow-md' },
                    listContainer: { class: '!bg-blue-100/30 custom-blur !rounded-full !p-3 shadow-sm' }
                }"
            >
                <template #itemicon="{ item }">
                    <img v-tooltip.top="item.label" :alt="item.label" :src="item.icon" style="width: 100%" @click="onDockItemClick($event, item)" />
                </template>
            </Dock>

            <!-- 批量上传照片对话框 -->
            <Dialog v-model:visible="showBatchUploadDialog" header="批量上传" :closable="false" :dismissableMask="false">
                <Tabs v-model:value="batchTabsStatus">
                    <TabList>
                        <Tab value="photo">照片</Tab>
                        <Tab value="pano">全景图</Tab>
                    </TabList>
                    <TabPanel value="photo">
                        <BatchPhotoUpload @saveBatch="savePhotoMarkers" @unlockSubmit="unlockSubmitPhotos" />
                    </TabPanel>
                    <TabPanel value="pano">
                        <BatchPanoUpload @saveBatch="savePanoMarkers" @unlockSubmit="unlockSubmitPanos" />
                    </TabPanel>
                </Tabs>
                <template #footer>
                    <Button label="确认上传" icon="pi pi-check" class="p-button-success" @click="handleBatchFormConfirm" :disabled="!batchUploaded || uploadingFile" :loading="uploadingFile" />
                    <Button label="取消" icon="pi pi-times" class="p-button-secondary" @click="cancelUploadFile" :disabled="uploadingFile" />
                </template>
            </Dialog>

            <!-- 添加加载状态指示器 -->
            <div v-if="isLoading || matrixStore.isGenerating" class="loading-overlay">
                <div class="loading-spinner">
                    <i class="pi pi-spin pi-spinner" style="font-size: 3rem"></i>
                    <div class="loading-text">
                        {{ matrixStore.isGenerating ? '正在生成全景矩阵...' : '正在加载数据...' }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <Dialog v-model:visible="showImageDialog" modal header="图片预览" :style="{ width: '25vw' }">
        <img :src="currentImage" style="width: 100%" />
    </Dialog>

    <!-- 全景组件使用绝对定位显示在地图上方 -->
    <Transition name="fade">
        <div class="panorama-container" v-if="showPanoCube">
            <div class="cubemap-header">
                <span>全景立方体视图</span>
                <button class="close-btn" @click="showPanoCube = false">×</button>
            </div>
            <PanoCube />
        </div>
    </Transition>
    <Transition name="fade">
        <div class="panorama-container" v-if="isPanoEquirect">
            <div class="panorama-header">
                <span>全景等距矩形视图</span>
                <button class="close-btn" @click="isPanoEquirect = false">×</button>
            </div>
            <PanoEquirect />
        </div>
    </Transition>

    <!-- 信息窗口组件 -->
    <InfoWindow
        :key="infoWindowKey"
        :visible="infoWindowVisible"
        :mode="infoWindowMode"
        :list-type="infoWindowListType"
        :items="infoWindowItems"
        :pin-items="pinMarkersData"
        :photo-items="photoMarkersData"
        :pano-items="panoMarkersData"
        :current-marker="currentMarker"
        @close="handleInfoWindowClose"
        @select-item="handleInfoWindowSelectItem"
        @edit-item="handleInfoWindowEditItem"
        @submit="handleInfoWindowSubmit"
        @update="handleInfoWindowUpdate"
        @delete="handleInfoWindowDelete"
        @delete-items="handleInfoWindowDeleteItems"
        @back-to-list="handleBackToList"
        @locate-marker="handleInfoWindowLocateMarker"
        @click.stop
    />

    <!-- SpeedDial 工具组件 -->
    <div
        class="speed-dial-container"
        :style="{
            top: speedDialPosition.y + 'px',
            right: speedDialPosition.x + 'px'
        }"
        @mousedown="startDrag"
        @mousemove="onDrag"
        @mouseup="stopDrag"
        @mouseleave="stopDrag"
    >
        <div class="speed-dial-wrapper">
            <!-- 主按钮 -->
            <Button outlined class="border speed-dial-button" @click="toggleSpeedDial" v-tooltip.left="speedDialVisible ? '隐藏工具' : '显示工具'">
                <i class="pi pi-cog" style="font-size: 1.2rem"></i>
            </Button>

            <!-- 工具项目 -->
            <div v-if="speedDialVisible" class="speed-dial-items">
                <div
                    v-for="(item, index) in speedDialItems"
                    :key="index"
                    class="flex flex-col items-center justify-between gap-2 p-2 border rounded border-surface-200 dark:border-surface-700 w-20 cursor-pointer speed-dial-item"
                    @click="item.command"
                    v-tooltip.left="item.tooltip"
                    :class="{ active: item.active }"
                >
                    <span :class="item.icon" style="font-size: 1.1rem" />
                    <span class="text-xs">{{ item.label }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 懒加载进度指示器 -->
    <div v-if="lazyLoadingProgress.show" class="lazy-loading-indicator">
        <div class="progress-content">
            <div class="progress-text">
                {{ lazyLoadingProgress.text }}
            </div>
            <ProgressBar :value="lazyLoadingProgress.percentage" :showValue="true" style="width: 200px" />
            <div class="progress-type" v-if="lazyLoadingProgress.type">类型: {{ lazyLoadingProgress.type }}</div>
        </div>
    </div>
</template>

<script setup>
import Dialog from 'primevue/dialog';
import BatchPhotoUpload from '@/components/map/BatchPhotoUpload.vue';
import PageEditor from '@/components/map/page_info/PageEditor.vue';
import PanoCube from '@/views/uikit/PanoCube.vue';
import PanoEquirect from '@/views/uikit/PanoEquirect.vue';
import InfoWindow from '@/components/map/InfoWindow.vue';
import Toast from 'primevue/toast';
import ProgressBar from 'primevue/progressbar';
import SpeedDial from 'primevue/speeddial';
import { onMounted, onUnmounted, ref, reactive, nextTick, watch } from 'vue';
import { ICON_SVG } from '@/config/svg.js';
import { ICON_IMG } from '@/config/img.js';
import { useToast } from 'primevue/usetoast';
import { useMapStore } from '@/stores/mapStore';
import { usePinStore } from '@/stores/pinStore';
import { usePhotoStore } from '@/stores/photoStore';
import { usePanoStore } from '@/stores/panoStore';
import { usePageStore } from '@/stores/pageStore';
import { useMatrixStore } from '@/stores/matrixStore';
import { getGeoInfo } from '@/utils/mapTool.js';
import mapService from '@/services/mapService';
import gridService from '@/services/gridService';
//============================================================================================
// 初始化 Toast
const toast = useToast();
// const { addPinMarkerToMap } = useMapMarkers(); // 正确
// 获取 stores
const mapStore = useMapStore();
const matrixStore = useMatrixStore();

let map = null;
let AMap = null;
// 默认缩略图路径
const thumbnail_img = 'place-marker.gif';
let pinIcon = ICON_SVG.PIN;
const pinStatus = ref(false);
// 添加photoStatus状态变量
const photoStatus = ref(false);
// const displayFinder = ref(false);
const panoStatus = ref(false); // 添加全景图状态变量
// 添加pageStatus状态变量
const pageStatus = ref(false);
// 所有类型的 markers 集合
const markers = ref([]);
const batchUploaded = ref(false);
const showImageDialog = ref(false);
const currentImage = ref('');
const showPanoCube = ref(false);
const isPanoEquirect = ref(false);
// 新增地理编码器引用
let geocoder = null;
// 搜索标记
const searchText = ref('');
const searchMarkers = ref([]); // 存储搜索结果的标记

// InfoWindow 相关状态
const infoWindowKey = ref(0);
const pageEditorVisible = ref(false);
const infoWindowVisible = ref(false);
const infoWindowMode = ref('list'); // 'list', 'input', 'form'
const infoWindowListType = ref('pin'); // 'page', 'pin', 'pano', 'photo'
const infoWindowItems = ref([]);
const isLoading = ref(false); // 添加加载状态
// 存储各类型标记的数据
const tempMarker = ref(null);
const currentMarker = reactive({});
const currentPageMarker = reactive({});
const currentPanoMarker = reactive({});
const pinMarkersData = ref([]);
const photoMarkersData = ref([]);
const panoMarkersData = ref([]);
const pageMarkersData = ref([]);
// BatchFileUpload 对话框中的状态
const showBatchUploadDialog = ref(false);
const uploadingFile = ref(false);
const batchPhotosFormData = ref([]);
const batchPanoFormData = ref([]);
const batchTabsStatus = ref('photo');
const pageContent = ref('');

//     ref(`<h1>请输入(Amap)</h1><p>这里是父组件中生成的内容咯：</p><hr><h2>好的</h2><p>这是一段文字</p><ol><li><p>哈哈</p></li><li><p>呵呵</p></li><li><p>嘿嘿</p></li></ol><p>下面是另一些文字</p><ul><li><p>😄</p></li><li><p>😋</p></li><li><p>😍</p></li><li><p></p></li></ul><blockquote><p>yin引用达人的话：让红脖子更红！--川建国</p></blockquote><p><code>echo &gt; good</code></p><p>下面再加一些代码凑数</p><pre><code>Hello 🌏
// world ☀️</code></pre><node-count name="2" count="2"></node-count><p>1</p>`);

// 取消上传图片
function cancelUploadFile() {
    showBatchUploadDialog.value = false;
}
const toggleShowBatchUploadDialog = () => {
    showBatchUploadDialog.value = !showBatchUploadDialog.value;
};

// 初始化搜索功能
const initSearch = () => {
    if (!AMap) return;

    AMap.plugin(['AMap.PlaceSearch'], function () {
        const placeSearch = new AMap.PlaceSearch({
            pageSize: 5,
            pageIndex: 1,
            city: '027',
            citylimit: true,
            map: map,
            panel: 'panel',
            autoFitView: true
        });

        // 保存搜索实例以便后续使用
        mapStore.placeSearch = placeSearch;
    });
};

onMounted(async () => {
    // 添加全局错误处理，忽略浏览器扩展错误
    window.addEventListener('unhandledrejection', (event) => {
        // 忽略浏览器扩展相关的错误
        if (event.reason && event.reason.message && (event.reason.message.includes('message channel closed') || event.reason.message.includes('listener indicated an asynchronous response'))) {
            console.warn('忽略浏览器扩展错误:', event.reason.message);
            event.preventDefault();
            return;
        }
        console.error('未处理的Promise错误:', event.reason);
    });

    // 添加键盘事件监听
    window.addEventListener('keydown', handleKeyDown);
    // 添加全局点击事件监听，用于关闭InfoForm
    document.addEventListener('click', handleDocumentClick);
    // =========================初始化搜索自动补全=====================================
    // 获取 AMap 实例
    const Amap = await mapService.initMap();
    // if (!Amap) return;

    // 异步加载 AutoComplete 插件
    Amap.plugin(['AMap.AutoComplete'], function () {
        const autoOptions = {
            input: 'search-input' // 这里要用 input 的 id
        };
        const autoComplete = new Amap.AutoComplete(autoOptions);

        // 绑定选中事件（可选）
        autoComplete.on('select', function (e) {
            // 你可以在这里处理选中后的逻辑，比如自动填充输入框、自动搜索等
            // 例如：searchText.value = e.poi.name;
            // 你也可以直接调用 handleSearch() 进行定位
            if (e.poi && e.poi.name) {
                searchText.value = e.poi.name;
                handleSearch();
            }
        });
    });

    // =======================================================================
    // 初始化地图原方案
    // window._AMapSecurityConfig = { securityJsCode: '05b99346f0c80233a92e6c1d6b56a9f3' };
    // AMapLoader.load({ key: 'f30bebf0ca8200ba9080214e826ff768', version: '2.0', plugins: ['AMap.Geocoder', 'AMap.Scale', 'AMap.ToolBar', 'AMap.InfoWindow'], useAMapUI: false, mapStyle: 'amap://styles/normal' })
    //     .then((AMap) => {
    //         // 将 AMap 实例提供给子组件
    //         window.AMap = AMap;
    //         map = new AMap.Map('container', { viewMode: '3D', zoom: 11, center: [114.305548, 30.59294] });
    //         // 添加工具条和比例尺
    //         map.addControl(new AMap.ToolBar());
    //         map.addControl(new AMap.Scale());
    //         // 保存地图实例到 store
    //         mapStore.setMapInstance(map);
    //         // 添加地图点击事件
    //         map.on('click', async (e) => {
    //             if (pinStatus.value) {
    //                 const geoInfo = await getGeoInfo(e.lnglat.getLng(), e.lnglat.getLat());
    //                 // 创建临时PIN标记数据
    //                 const tempPin = {
    //                     gcj: {
    //                         lng: null,
    //                         lat: null
    //                     },
    //                     gps: {
    //                         lng: null,
    //                         lat: null
    //                     },
    //                     custom_gcj: {
    //                         lng: e.lnglat.getLng(),
    //                         lat: e.lnglat.getLat()
    //                     },
    //                     name: 'tempPin marker',
    //                     icon: 'PIN', // 使用 ICON_SVG.PIN
    //                     tag: [],
    //                     link: null,
    //                     collection_id: null,
    //                     altitude: null,
    //                     city: geoInfo.city || null,
    //                     full_address: geoInfo.full_address || null
    //                 };
    //                 // 立即在点击位置添加临时标记
    //                 currentMarker.value = tempPin; // 设置当前标记数据，传递到InfoWindow.vue 中的 props.currentMarker
    //                 addTempPinMarker(tempPin);
    //                 console.log('点击地图，将打开标记输入表单', currentMarker.value);
    //             } else if (photoStatus.value) {
    //                 const geoInfo = await getGeoInfo(e.lnglat.getLng(), e.lnglat.getLat());
    //                 // 创建临时PIN标记数据
    //                 const tempPhoto = {
    //                     name: 'tempPhoto',
    //                     tag: [],
    //                     collection_id: null,
    //                     link: null,
    //                     city: geoInfo.city || null,
    //                     full_address: geoInfo.full_address,
    //                     gps: {
    //                         lng: null,
    //                         lat: null
    //                     },
    //                     gcj: {
    //                         lng: null,
    //                         lat: null
    //                     },
    //                     custom_gcj: {
    //                         lng: e.lnglat.getLng(),
    //                         lat: e.lnglat.getLat()
    //                     },
    //                     altitude: null,
    //                     gps_timestamp: '',
    //                     photo_url: null,
    //                     thumbnail_url: thumbnail_img
    //                 };
    //                 currentMarker.value = tempPhoto;
    //                 addTempPhotoMarker(tempPhoto, e.lnglat, AMap);
    //             } else if (panoStatus.value) {
    //                 const geoInfo = await getGeoInfo(e.lnglat.getLng(), e.lnglat.getLat());
    //                 const tempPano = {
    //                     name: 'tempPano',
    //                     tag: [],
    //                     collection_id: null,
    //                     link: null,
    //                     city: geoInfo.city || null,
    //                     full_address: geoInfo.full_address,
    //                     gps: {
    //                         lng: null,
    //                         lat: null
    //                     },
    //                     gcj: {
    //                         lng: null,
    //                         lat: null
    //                     },
    //                     custom_gcj: {
    //                         lng: e.lnglat.getLng(),
    //                         lat: e.lnglat.getLat()
    //                     },
    //                     altitude: null,
    //                     gps_timestamp: '',
    //                     img_url: null,
    //                     thumbnail_url: thumbnail_img,
    //                     config_file_url: null,
    //                     config_json: null,
    //                     type: 'equirectangular'
    //                 };
    //                 currentMarker.value = tempPano;
    //                 addTempPanoMarker(tempPano, e.lnglat, AMap);
    //             }
    //         });

    //         AMap.plugin(['AMap.PlaceSearch'], function () {
    //             //构造地点查询类
    //             // const autoOptions = {
    //             //     input: 'search-input'
    //             // };
    //             // const autoComplete = new AMap.AutoComplete(autoOptions);
    //             const placeSearch = new AMap.PlaceSearch({
    //                 pageSize: 5, // 单页显示结果条数
    //                 pageIndex: 1, // 页码
    //                 city: '027', // 兴趣点城市
    //                 citylimit: true, //是否强制限制在设置的城市内搜索
    //                 map: map, // 展现结果的地图实例
    //                 panel: 'panel', // 结果列表将在此容器中进行展示。
    //                 autoFitView: true // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
    //             });
    //             //关键字查询
    //             placeSearch.search(searchText.value);
    //         });
    //     })
    //     .catch((e) => {
    //         console.log(e);
    //     });
    //=======================================================================

    // 初始化地图新方案
    try {
        // 使用 mapService 初始化地图
        isLoading.value = true;
        AMap = await mapService.initMap();
        map = await mapService.createMap('container');
        const mapInstance = await mapService.getMap();
        mapStore.fetchMapOptions(mapInstance);
        // 添加地图点击事件
        map.on('click', handleMapClick);

        // 初始化搜索功能
        initSearch();

        // 地图初始化完成，不再阻塞式加载数据
        toast.add({
            severity: 'success',
            summary: '地图初始化完成',
            detail: 'MyMap已准备就绪，点击菜单按需加载数据',
            life: 3000
        });

        // 异步预加载并显示少量数据（非阻塞）
        setTimeout(() => {
            preloadAndShowEssentialData();
        }, 1000);
    } catch (error) {
        console.error('地图初始化失败:', error);
        toast.add({
            severity: 'error',
            summary: '错误',
            detail: '地图加载失败，请刷新页面重试',
            life: 5000
        });
    } finally {
        isLoading.value = false;
    }
});

// ==================== 数据缓存和懒加载系统 ====================

// 数据缓存管理
const dataCache = ref({
    pins: { data: [], timestamp: null, version: 0, loaded: false },
    photos: { data: [], timestamp: null, version: 0, loaded: false },
    panos: { data: [], timestamp: null, version: 0, loaded: false },
    pages: { data: [], timestamp: null, version: 0, loaded: false }
});

// 缓存有效期（5分钟）
const CACHE_DURATION = 5 * 60 * 1000;

// ==================== SpeedDial 工具配置 ====================

// 测距工具相关
let rangingTool = null;
let mouseTool = null;
const isRangingActive = ref(false);

// 行政区工具相关
const isDistrictToolActive = ref(false);
let districtPolygons = ref([]);

// SpeedDial 状态管理
const speedDialVisible = ref(true);
const speedDialPosition = ref({ x: 20, y: 120 });
const isDragging = ref(false);

// SpeedDial 菜单项
const speedDialItems = ref([
    {
        label: '测距',
        icon: 'pi pi-arrows-h',
        tooltip: '点击开始测距，双击结束',
        active: false,
        command: () => {
            toggleRangingTool();
        }
    },
    {
        label: '行政区',
        icon: 'pi pi-map',
        tooltip: '点击地图显示行政区边界',
        active: false,
        command: () => {
            toggleDistrictTool();
        }
    },
    {
        label: '清除',
        icon: 'pi pi-trash',
        tooltip: '清除所有测量和行政区',
        active: false,
        command: () => {
            clearAllTools();
        }
    }
]);

// ==================== SpeedDial 控制功能 ====================

// 切换 SpeedDial 显示/隐藏
const toggleSpeedDial = () => {
    speedDialVisible.value = !speedDialVisible.value;
};

// 拖拽功能
let dragStartX = 0;
let dragStartY = 0;

const startDrag = (event) => {
    if (event.target.closest('.speed-dial-button')) {
        isDragging.value = true;
        dragStartX = event.clientX;
        dragStartY = event.clientY;
        event.preventDefault();
    }
};

const onDrag = (event) => {
    if (isDragging.value) {
        const deltaX = dragStartX - event.clientX;
        const deltaY = event.clientY - dragStartY;

        speedDialPosition.value.x = Math.max(20, speedDialPosition.value.x + deltaX);
        speedDialPosition.value.y = Math.max(120, speedDialPosition.value.y + deltaY);

        dragStartX = event.clientX;
        dragStartY = event.clientY;
    }
};

const stopDrag = () => {
    isDragging.value = false;
};

// ==================== SpeedDial 工具功能实现 ====================

// 测距工具
const toggleRangingTool = () => {
    if (!AMap || !map) {
        toast.add({
            severity: 'warn',
            summary: '提示',
            detail: '地图未初始化完成',
            life: 3000
        });
        return;
    }

    if (isRangingActive.value) {
        // 关闭测距工具
        if (rangingTool) {
            rangingTool.turnOff();
            rangingTool = null;
        }
        isRangingActive.value = false;
        speedDialItems.value[0].active = false;
        toast.add({
            severity: 'info',
            summary: '测距工具',
            detail: '已关闭测距工具',
            life: 3000
        });
    } else {
        // 开启测距工具
        AMap.plugin('AMap.RangingTool', () => {
            rangingTool = new AMap.RangingTool(map);

            // 监听测距结束事件
            rangingTool.on('end', (event) => {
                const distance = event.distance;
                toast.add({
                    severity: 'success',
                    summary: '测距结果',
                    detail: `距离: ${(distance / 1000).toFixed(2)} 公里`,
                    life: 5000
                });
            });

            rangingTool.turnOn();
            isRangingActive.value = true;
            speedDialItems.value[0].active = true;

            toast.add({
                severity: 'info',
                summary: '测距工具',
                detail: '点击地图开始测距，双击结束',
                life: 3000
            });
        });
    }
};

// 行政区显示工具
const toggleDistrictTool = () => {
    if (!AMap || !map) {
        toast.add({
            severity: 'warn',
            summary: '提示',
            detail: '地图未初始化完成',
            life: 3000
        });
        return;
    }

    if (isDistrictToolActive.value) {
        // 关闭行政区工具
        map.off('click', handleMapClickForDistrict);
        isDistrictToolActive.value = false;
        speedDialItems.value[1].active = false;
        toast.add({
            severity: 'info',
            summary: '行政区工具',
            detail: '已关闭行政区显示工具',
            life: 3000
        });
    } else {
        // 开启行政区工具
        map.on('click', handleMapClickForDistrict);
        isDistrictToolActive.value = true;
        speedDialItems.value[1].active = true;
        toast.add({
            severity: 'info',
            summary: '行政区工具',
            detail: '点击地图任意位置显示行政区边界',
            life: 3000
        });
    }
};

// 处理地图点击事件，显示行政区
const handleMapClickForDistrict = (event) => {
    const lnglat = event.lnglat;

    // 使用逆地理编码获取行政区信息
    AMap.plugin('AMap.Geocoder', () => {
        const geocoder = new AMap.Geocoder({
            radius: 1000,
            extensions: 'all'
        });

        geocoder.getAddress(lnglat, (status, result) => {
            if (status === 'complete' && result.regeocode) {
                const addressComponent = result.regeocode.addressComponent;
                const district = addressComponent.district;
                const adcode = addressComponent.adcode;

                if (district && adcode) {
                    showDistrictBoundary(adcode, district);
                } else {
                    toast.add({
                        severity: 'warn',
                        summary: '提示',
                        detail: '无法获取该位置的行政区信息',
                        life: 3000
                    });
                }
            } else {
                toast.add({
                    severity: 'error',
                    summary: '错误',
                    detail: '逆地理编码失败',
                    life: 3000
                });
            }
        });
    });
};

// 显示行政区边界
const showDistrictBoundary = (adcode, districtName) => {
    AMap.plugin('AMap.DistrictSearch', () => {
        const district = new AMap.DistrictSearch({
            level: 'district',
            showbiz: false
        });

        district.search(adcode, (status, result) => {
            if (status === 'complete' && result.districtList && result.districtList[0]) {
                const bounds = result.districtList[0].boundaries;
                if (bounds) {
                    // 清除之前的行政区边界
                    clearDistrictPolygons();

                    bounds.forEach((boundary) => {
                        const polygon = new AMap.Polygon({
                            path: boundary,
                            strokeColor: '#ff4757',
                            strokeWeight: 3,
                            fillColor: '#ff4757',
                            fillOpacity: 0.2,
                            strokeOpacity: 1
                        });
                        polygon.setMap(map);
                        districtPolygons.value.push(polygon);
                    });

                    // 调整地图视野
                    map.setFitView(districtPolygons.value);

                    toast.add({
                        severity: 'success',
                        summary: '行政区边界',
                        detail: `已显示 ${districtName} 的边界`,
                        life: 3000
                    });
                }
            } else {
                toast.add({
                    severity: 'error',
                    summary: '错误',
                    detail: '获取行政区边界失败',
                    life: 3000
                });
            }
        });
    });
};

// 清除行政区边界
const clearDistrictPolygons = () => {
    districtPolygons.value.forEach((polygon) => {
        polygon.setMap(null);
    });
    districtPolygons.value = [];
};

// 清除所有工具
const clearAllTools = () => {
    // 关闭测距工具
    if (rangingTool) {
        rangingTool.turnOff();
        rangingTool = null;
    }
    isRangingActive.value = false;
    speedDialItems.value[0].active = false;

    // 关闭行政区工具
    if (map && isDistrictToolActive.value) {
        map.off('click', handleMapClickForDistrict);
    }
    isDistrictToolActive.value = false;
    speedDialItems.value[1].active = false;

    // 清除行政区边界
    clearDistrictPolygons();

    // 清除测距线条（如果有的话）
    if (map) {
        map.clearMap();
        // 重新加载标记
        setTimeout(() => {
            preloadAndShowEssentialData();
        }, 500);
    }

    toast.add({
        severity: 'info',
        summary: '清除完成',
        detail: '已清除所有测量工具和行政区边界',
        life: 3000
    });
};

// 安全执行异步函数，忽略浏览器扩展错误
const safeAsync = async (fn, context = '') => {
    try {
        return await fn();
    } catch (error) {
        if (error.message && error.message.includes('message channel closed')) {
            console.warn(`忽略浏览器扩展错误 [${context}]:`, error.message);
            return null;
        }
        console.error(`执行错误 [${context}]:`, error);
        throw error;
    }
};

// 懒加载进度状态
const lazyLoadingProgress = ref({
    show: false,
    text: '',
    percentage: 0,
    type: ''
});

// 检查缓存是否有效
const isCacheValid = (cacheItem) => {
    if (!cacheItem.timestamp || !cacheItem.loaded) return false;
    return Date.now() - cacheItem.timestamp < CACHE_DURATION;
};

// 显示懒加载进度
const showLazyLoadingProgress = (text, percentage, type = '') => {
    lazyLoadingProgress.value = {
        show: true,
        text: text,
        percentage: percentage,
        type: type
    };
};

// 隐藏懒加载进度
const hideLazyLoadingProgress = () => {
    lazyLoadingProgress.value.show = false;
};

// 预加载并显示核心数据（非阻塞）
const preloadAndShowEssentialData = async () => {
    try {
        console.log('开始预加载并显示核心数据...');

        // 预加载并显示标记数据
        const pinStore = usePinStore();
        const photoStore = usePhotoStore();
        const panoStore = usePanoStore();
        const pageStore = usePageStore();

        // 异步预加载，不阻塞界面
        Promise.all([
            // 加载并显示pin标记
            pinStore.fetchAllPins().then(() => {
                const pinData = pinStore.allPins || [];
                dataCache.value.pins = {
                    data: pinData,
                    timestamp: Date.now(),
                    version: dataCache.value.pins.version + 1,
                    loaded: true
                };
                console.log(`预加载pin完成: ${pinData.length} 个标记`);

                // 显示在地图上
                if (pinData.length > 0) {
                    console.log('开始添加pin标记到地图，数据:', pinData.slice(0, 2)); // 显示前2个数据样本
                    pinData.forEach((item) => {
                        const result = addPinMarkerToMap(item);
                        if (!result) {
                            console.warn('Pin标记添加失败:', item);
                        }
                    });
                    console.log(`已显示 ${pinData.length} 个pin标记到地图`);
                } else {
                    console.log('没有pin数据需要显示');
                }
            }),

            // 加载并显示photo标记
            photoStore.fetchAllPhotos().then(() => {
                const photoData = photoStore.allPhotos || [];
                dataCache.value.photos = {
                    data: photoData,
                    timestamp: Date.now(),
                    version: dataCache.value.photos.version + 1,
                    loaded: true
                };
                console.log(`预加载photo完成: ${photoData.length} 个照片`);

                // 显示在地图上
                if (photoData.length > 0) {
                    console.log('开始添加photo标记到地图，数据:', photoData.slice(0, 2)); // 显示前2个数据样本
                    photoData.forEach((item) => {
                        const result = addPhotoMarkerToMap(item);
                        if (!result) {
                            console.warn('Photo标记添加失败:', item);
                        }
                    });
                    console.log(`已显示 ${photoData.length} 个photo标记到地图`);
                } else {
                    console.log('没有photo数据需要显示');
                }
            }),

            // 加载并显示pano标记
            panoStore.fetchAllPanos().then(() => {
                const panoData = panoStore.allPanos || [];
                dataCache.value.panos = {
                    data: panoData,
                    timestamp: Date.now(),
                    version: dataCache.value.panos.version + 1,
                    loaded: true
                };
                console.log(`预加载pano完成: ${panoData.length} 个全景`);

                // 显示在地图上
                if (panoData.length > 0) {
                    console.log('开始添加pano标记到地图，数据:', panoData.slice(0, 2)); // 显示前2个数据样本
                    panoData.forEach((item) => {
                        const result = addPanoMarkerToMap(item);
                        if (!result) {
                            console.warn('Pano标记添加失败:', item);
                        }
                    });
                    console.log(`已显示 ${panoData.length} 个pano标记到地图`);
                } else {
                    console.log('没有pano数据需要显示');
                }
            }),

            // 加载并显示page标记
            pageStore.fetchAllPages().then(() => {
                const pageData = pageStore.allPages || [];
                dataCache.value.pages = {
                    data: pageData,
                    timestamp: Date.now(),
                    version: dataCache.value.pages.version + 1,
                    loaded: true
                };
                console.log(`预加载page完成: ${pageData.length} 个文章`);

                // 显示在地图上
                if (pageData.length > 0) {
                    console.log('开始添加page标记到地图，数据:', pageData.slice(0, 2)); // 显示前2个数据样本
                    pageData.forEach((item) => {
                        const result = addPageMarkerToMap(item);
                        if (!result) {
                            console.warn('Page标记添加失败:', item);
                        }
                    });
                    console.log(`已显示 ${pageData.length} 个page标记到地图`);
                } else {
                    console.log('没有page数据需要显示');
                }
            })
        ])
            .then(() => {
                toast.add({
                    severity: 'success',
                    summary: '数据加载完成',
                    detail: '所有标记已加载到地图上',
                    life: 3000
                });
            })
            .catch((error) => {
                console.warn('预加载数据失败:', error);
                toast.add({
                    severity: 'warn',
                    summary: '部分数据加载失败',
                    detail: '请手动点击菜单加载数据',
                    life: 3000
                });
            });
    } catch (error) {
        console.warn('预加载过程中出现错误:', error);
    }
};

// 显示缓存的标记数据
const showCachedPinMarkers = () => {
    const cachedData = dataCache.value.pins.data;
    infoWindowItems.value = cachedData;
    infoWindowVisible.value = true;
    infoWindowMode.value = 'list';
    infoWindowListType.value = 'pin';

    toast.add({
        severity: 'info',
        summary: '从缓存加载',
        detail: `已从缓存加载 ${cachedData.length} 个标记`,
        life: 3000
    });
};

const showCachedPhotoMarkers = () => {
    const cachedData = dataCache.value.photos.data;
    infoWindowItems.value = cachedData;
    infoWindowVisible.value = true;
    infoWindowMode.value = 'list';
    infoWindowListType.value = 'photo';

    toast.add({
        severity: 'info',
        summary: '从缓存加载',
        detail: `已从缓存加载 ${cachedData.length} 个照片标记`,
        life: 3000
    });
};

const showCachedPanoMarkers = () => {
    const cachedData = dataCache.value.panos.data;
    infoWindowItems.value = cachedData;
    infoWindowVisible.value = true;
    infoWindowMode.value = 'list';
    infoWindowListType.value = 'pano';

    toast.add({
        severity: 'info',
        summary: '从缓存加载',
        detail: `已从缓存加载 ${cachedData.length} 个全景标记`,
        life: 3000
    });
};

// 分批加载标记到地图
const loadMarkersInBatches = async (data, type, isInitialLoad = false) => {
    if (!data || data.length === 0) return;

    const batchSize = 50; // 每批50个标记
    const totalBatches = Math.ceil(data.length / batchSize);

    for (let i = 0; i < totalBatches; i++) {
        const start = i * batchSize;
        const end = Math.min(start + batchSize, data.length);
        const batch = data.slice(start, end);

        // 更新进度
        const percentage = Math.round(((i + 1) / totalBatches) * 100);
        showLazyLoadingProgress(`正在加载${type}标记... (${i + 1}/${totalBatches})`, percentage, type);

        // 添加当前批次的标记
        batch.forEach((item) => {
            if (type === 'pin') {
                addPinMarkerToMap(item);
            } else if (type === 'photo') {
                addPhotoMarkerToMap(item);
            } else if (type === 'pano') {
                addPanoMarkerToMap(item);
            } else if (type === 'page') {
                addPageMarkerToMap(item);
            }
        });

        // 短暂延迟，让UI有时间更新
        if (i < totalBatches - 1) {
            await new Promise((resolve) => setTimeout(resolve, 50));
        }
    }
};

// 从数据库中加载所有 photomarker 到地图上，即刷新照片
const savePhotoMarkers = async () => {};
const savePanoMarkers = async () => {};

// 解锁提交按钮
const unlockSubmitPhotos = (formDatas) => {
    batchUploaded.value = true;
    // formDatas.value.forEach((item) => {
    //     batchFormData.value = [...batchFormData.value, item];
    // });
    batchPhotosFormData.value = formDatas;
    console.log('formDatas', formDatas);
    console.log('batchPhotosFormData-Amap: ', batchPhotosFormData.value);
};
const unlockSubmitPanos = (formDatas) => {
    batchUploaded.value = true;
    batchPanoFormData.value = formDatas;
    console.log('formDatas', formDatas);
    console.log('batchPanoFormData-Amap: ', batchPanoFormData.value);
};

// 点击图片批量上传的确认按钮
const handleBatchFormConfirm = async () => {
    batchUploaded.value = false;
    // 调用 photoStore 上传
    if (batchTabsStatus.value === 'photo') {
        const photoStore = usePhotoStore();
        await photoStore.addPhoto(batchPhotosFormData.value);
        await loadPhotoMarkers(true);
    } else if (batchTabsStatus.value === 'pano') {
        const panoStore = usePanoStore();
        await panoStore.addPano(batchPanoFormData.value);
        await loadPanoMarkers(true);
    }
    showBatchUploadDialog.value = false;
};

onUnmounted(() => {
    // 清理 SpeedDial 工具
    if (rangingTool) {
        rangingTool.turnOff();
        rangingTool = null;
    }
    if (map && isDistrictToolActive.value) {
        map.off('click', handleMapClickForDistrict);
    }
    clearDistrictPolygons();

    // 移除键盘事件监听
    window.removeEventListener('keydown', handleKeyDown);
    // 移除点击事件监听
    document.removeEventListener('click', handleDocumentClick);

    // 移除地图点击事件
    if (map) {
        map.off('click', handleMapClick);
    }

    // 清理矩阵相关的事件监听器和窗口
    clearMapEventListeners();
    if (hoverInfoWindow) {
        hoverInfoWindow.close();
        hoverInfoWindow = null;
    }

    // 不销毁地图实例，因为它是全局共享的
    // 只清除当前组件添加的标记
    markers.value.forEach((marker) => {
        const markerId = marker.getExtData()?.id || '';
        if (markerId) {
            mapService.removeMarker(`marker-${markerId}`);
        }
    });
    markers.value = [];
    mapService.destroy();
});
//=======================================================================
// 修改 watch 函数，使用 mapService 获取地图实例
watch(
    () => mapStore.locationPickerMode.active,
    (isActive) => {
        if (isActive) {
            // 获取地图实例
            const map = mapService.getMap();
            if (!map) return;

            // 显示提示
            toast.add({
                severity: 'info',
                summary: '选择位置',
                detail: mapStore.locationPickerMode.message,
                life: 5000
            });

            // 创建一次性点击事件处理函数
            const handleMapClick = (e) => {
                // 获取点击位置
                const position = {
                    lng: e.lnglat.getLng(),
                    lat: e.lnglat.getLat()
                };

                // 调用回调函数
                if (typeof mapStore.locationPickerMode.callback === 'function') {
                    mapStore.locationPickerMode.callback(position);
                }

                // 关闭位置选择模式
                mapStore.setLocationPickerMode({ active: false });

                // 移除一次性事件监听
                map.off('click', handleMapClick);
            };

            // 添加一次性点击事件监听
            map.on('click', handleMapClick);
        }
    }
);

watch(
    () => currentMarker.value,
    (newValue) => {
        if (newValue) {
            console.log('📌  currentMarker.value changed: ', newValue);
            console.log('📌  currentMarker.value has id: ', newValue.id ? newValue.id : 'no id');
        }
    }
);

const displayPanoCube = () => {
    showPanoCube.value = !showPanoCube.value;
};
const togglePanoEquirect = () => {
    isPanoEquirect.value = !isPanoEquirect.value;
};

// 新增键盘事件处理函数
const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
        if (pinStatus.value) {
            pinStatus.value = false;
            const mapContainer = document.querySelector('#container');
            mapContainer.classList.remove('pin-cursor');
            mapContainer.style.cursor = 'default';
        }
        if (photoStatus.value) {
            photoStatus.value = false;
            const mapContainer = document.querySelector('#container');
            mapContainer.classList.remove('photo-cursor');
            mapContainer.style.cursor = 'default';
        }
        if (panoStatus.value) {
            panoStatus.value = false;
            const mapContainer = document.querySelector('#container');
            mapContainer.classList.remove('pano-cursor');
            mapContainer.style.cursor = 'default';
        }
    }
};

//=================================================== 逻辑区 =========================================
// 菜单栏
const nestedMenuitems = ref([
    {
        label: '工具',
        icon: 'pi pi-fw pi-wrench',
        items: [
            {
                label: '社群',
                icon: 'pi pi-fw pi-discord',
                items: [
                    {
                        label: '新增社群',
                        icon: 'pi pi-fw pi-user-plus'
                    },
                    { label: '编辑社群', icon: 'pi pi-fw pi-user-edit' }
                ]
            },
            {
                label: '行政区域',
                icon: 'pi pi-fw pi-th-large',
                command: () => {
                    // 显示行政区选择面板
                    // TODO: 显示行政区选择面板
                }
            }
        ]
    },
    {
        label: '资料',
        icon: 'pi pi-fw pi-file',
        items: [
            {
                label: '新文档',
                icon: 'pi pi-fw pi-file-edit',
                command: () => {
                    pageEditorVisible.value = !pageEditorVisible.value;
                    pageStore.pageRefMarkers;
                }
            },
            { label: '新图集', icon: 'pi pi-fw pi-images' }
        ]
    },
    {
        label: '标记📌',
        icon: 'pi pi-fw pi-map-marker',
        items: [
            {
                label: '文章列表',
                icon: 'pi pi-fw pi-file-word',
                command: () => {
                    // 暂时不实现
                    console.log('PageList clicked');
                }
            },
            {
                label: '标记列表',
                icon: 'pi pi-fw pi-thumbtack',
                command: () => {
                    loadPinMarkers();
                }
            },
            {
                label: '全景列表',
                icon: 'pi pi-fw pi-telegram',
                command: () => {
                    loadPanoMarkers();
                    console.log('PanoList clicked');
                }
            },
            {
                label: '照片列表',
                icon: 'pi pi-fw pi-images',
                command: () => {
                    loadPhotoMarkers();
                }
            },
            {
                label: '全景矩阵',
                icon: 'pi pi-fw pi-send',
                command: async () => {
                    await handlePanoMatrixToggle();
                }
            }
        ]
    },
    {
        label: '测试',
        icon: 'pi pi-fw pi-code',
        items: [
            {
                label: 'Cube',
                icon: 'pi pi-fw pi-box',
                command: () => {
                    displayPanoCube();
                }
            },
            {
                label: 'Equirect',
                icon: 'pi pi-fw pi-th-large',
                command: () => {
                    togglePanoEquirect();
                }
            }
        ]
    },
    {
        label: '视图窗口',
        icon: 'pi pi-fw pi-eye',
        items: [
            {
                label: '行政区域窗口',
                icon: 'pi pi-fw pi-th-large',
                command: () => {
                    // 显示行政区域窗口按钮
                }
            },
            {
                label: '其他',
                icon: 'pi pi-fw pi-face-smile',
                command: () => {
                    // 显示其他按钮
                }
            }
        ]
    }
]);
const resetInfoWindow = () => {
    infoWindowVisible.value = false;
    infoWindowMode.value = 'list';
    infoWindowListType.value = 'pin';
    currentMarker.value = null;
    infoWindowKey.value++; // ★★★ 关键：每次重置都+1
};
// Dock 图标
const dockItems = ref([
    {
        label: '上传',
        icon: `${import.meta.env.BASE_URL}demo/images/dock/computer.apng`,
        command: () => {
            toggleShowBatchUploadDialog();
        }
    },
    {
        label: '图集',
        icon: `${import.meta.env.BASE_URL}demo/images/dock/photo.apng`,
        command: async () => {
            await loadAllMarkers(); // 添加这个函数调用
        }
    },
    {
        label: 'Photo',
        icon: `${import.meta.env.BASE_URL}demo/images/dock/camera.apng`,
        command: () => {
            resetInfoWindow(); // ★★★ 先重置 ★★★
            console.log('Photos button clicked');
            // 切换到photo marker添加模式
            photoStatus.value = !photoStatus.value;
            const mapContainer = document.querySelector('#container');

            if (photoStatus.value) {
                mapContainer.classList.add('photo-cursor');
                mapContainer.style.cursor = '';
            } else {
                mapContainer.classList.remove('photo-cursor');
                mapContainer.style.cursor = 'default';
            }
        }
    },
    {
        label: 'Pano',
        icon: `${import.meta.env.BASE_URL}demo/images/dock/360.webp`,
        command: () => {
            console.log('Pano button clicked');
            resetInfoWindow(); // ★★★ 先重置 ★★★
            // 切换到 pano marker 添加模式
            panoStatus.value = !panoStatus.value;
            const mapContainer = document.querySelector('#container');

            if (panoStatus.value) {
                mapContainer.classList.add('pano-cursor');
                mapContainer.style.cursor = '';
            } else {
                mapContainer.classList.remove('pano-cursor');
                mapContainer.style.cursor = 'default';
            }
        }
    },
    {
        label: 'Pin',
        icon: `${import.meta.env.BASE_URL}demo/images/dock/pin.apng`,
        command: () => {
            resetInfoWindow(); // ★★★ 先重置 ★★★
            pinStatus.value = !pinStatus.value;
            const mapContainer = document.querySelector('#container');

            if (pinStatus.value) {
                mapContainer.classList.add('pin-cursor');
                mapContainer.style.cursor = '';
                // 确保模式正确
                infoWindowMode.value = 'input';
                infoWindowListType.value = 'pin';
                console.log('已启用添加引脚标记模式');
            } else {
                mapContainer.classList.remove('pin-cursor');
                mapContainer.style.cursor = 'default';
                console.log('已禁用添加引脚标记模式');
            }
        }
    },
    {
        label: 'Page',
        icon: `${import.meta.env.BASE_URL}demo/images/dock/page.apng`,
        command: () => {
            pageStatus.value = !pageStatus.value;
            const mapContainer = document.querySelector('#container');

            if (pageStatus.value) {
                mapContainer.classList.add('page-cursor');
                mapContainer.style.cursor = '';
            } else {
                mapContainer.classList.remove('page-cursor');
                mapContainer.style.cursor = 'default';
            }
        }
    },
    {
        label: 'Stickers',
        icon: `${import.meta.env.BASE_URL}demo/images/dock/stickers.webp`,
        command: () => {
            pageStatus.value = !pageStatus.value;
            const mapContainer = document.querySelector('#container');

            if (pageStatus.value) {
                mapContainer.classList.add('page-cursor');
                mapContainer.style.cursor = '';
            } else {
                mapContainer.classList.remove('page-cursor');
                mapContainer.style.cursor = 'default';
            }
        }
    }
]);
// 主逻辑-1：点击dock图标，触发鼠标样式改变事件
const onDockItemClick = (event, item) => {
    if (item.command) {
        item.command();
    }
    event.preventDefault();
};
// 主逻辑-2：点击地图，触发标记点击事件
const handleMapClick = async (e) => {
    if (pinStatus.value) {
        await handlePinClick(e);
    } else if (photoStatus.value) {
        await handlePhotoClick(e);
    } else if (panoStatus.value) {
        await handlePanoClick(e);
    } else if (pageStatus.value) {
        await handlePageClick(e);
    } else if (mapStore.locationPickerMode.active) {
        // 位置选择模式的处理已在 mapStore 的 watch 中处理
    }
};
// 主逻辑-3：处理各种类型 marker 的点击
// 3.1 处理 Pin 点击
const handlePinClick = async (e) => {
    if (e.lnglat) {
        const geoInfo = await getGeoInfo(e.lnglat.getLng(), e.lnglat.getLat());
        // 创建临时PIN标记数据
        const tempPin = {
            gcj: { lng: null, lat: null },
            gps: { lng: null, lat: null },
            custom_gcj: { lng: e.lnglat.getLng(), lat: e.lnglat.getLat() },
            name: 'tempPin marker',
            icon: 'PIN',
            tag: [],
            link: null,
            collection_id: null,
            altitude: null,
            city: geoInfo.city || null,
            full_address: geoInfo.full_address || null
        };
        // 设置当前标记数据
        currentMarker.value = tempPin;
        addTempPinMarker(tempPin);
    }
};

// 3.2 处理 Photo 点击
const handlePhotoClick = async (e) => {
    const geoInfo = await getGeoInfo(e.lnglat.getLng(), e.lnglat.getLat());
    // 创建临时Photo标记数据
    const tempPhoto = {
        name: 'tempPhoto',
        tag: [],
        collection_id: null,
        link: null,
        city: geoInfo.city || null,
        full_address: geoInfo.full_address,
        gps: { lng: null, lat: null },
        gcj: { lng: null, lat: null },
        custom_gcj: { lng: e.lnglat.getLng(), lat: e.lnglat.getLat() },
        altitude: null,
        gps_timestamp: '',
        photo_url: null,
        thumbnail_url: thumbnail_img
    };
    //设置当前标记数据
    currentMarker.value = tempPhoto;
    addTempPhotoMarker(tempPhoto);
};

// 3.3 处理 Pano 点击
const handlePanoClick = async (e) => {
    const geoInfo = await getGeoInfo(e.lnglat.getLng(), e.lnglat.getLat());
    // 创建临时Photo标记数据
    const tempPano = {
        name: 'tempPano',
        tag: [],
        collection_id: null,
        link: null,
        city: geoInfo.city || null,
        full_address: geoInfo.full_address,
        gps: { lng: null, lat: null },
        gcj: { lng: null, lat: null },
        custom_gcj: { lng: e.lnglat.getLng(), lat: e.lnglat.getLat() },
        altitude: null,
        gps_timestamp: '',
        img_url: null,
        thumbnail_url: thumbnail_img,
        config_file_url: null,
        config_json: null,
        type: 'equirectangular'
    };
    //设置当前标记数据
    currentMarker.value = tempPano;
    addTempPanoMarker(tempPano);
};

// 3.4 处理 Page 点击
const handlePageClick = async (e) => {
    const geoInfo = await getGeoInfo(e.lnglat.getLng(), e.lnglat.getLat());
    const tempContent = generateDateContent();
    // 创建临时Page标记数据
    const tempPage = {
        name: tempContent,
        tag: [],
        collection_id: null,
        link: null,
        city: geoInfo.city || null,
        full_address: geoInfo.full_address,
        gps: { lng: null, lat: null },
        gcj: { lng: null, lat: null },
        custom_gcj: { lng: e.lnglat.getLng(), lat: e.lnglat.getLat() },
        altitude: null,
        icon: 'DOCUMENT',
        page_url: null,
        first_image_url: null,
        thumbnail_url: null,
        page_content: tempContent, // 确保这个函数返回有效的内容
        page_markers_ids: []
    };

    // 设置当前标记数据
    currentMarker.value = tempPage;
    currentPageMarker.value = tempPage;
    pageStore.setPageMarker(tempPage); // 确保在 store 中也设置了当前标记
    pageStore.setPageContent(tempPage.page_content); // 确保在 store 中也设置了当前标记
    // 添加临时标记到地图
    await nextTick(); // 等待 Vue 更新周期
    addTempPageMarker(tempPage);
};
// 生成带有当前日期的页面内容
const generateDateContent = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const hours = String(today.getHours()).padStart(2, '0');
    const minutes = String(today.getMinutes()).padStart(2, '0');
    const seconds = String(today.getSeconds()).padStart(2, '0');
    const dateString = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

    return `<h1>${dateString}</h1><p></p>`;
};

// 主逻辑-4：添加各种类型的临时标记  TempMarker 到地图
// 4.1 添加 TempPin 标记
const addTempPinMarker = (tempPin) => {
    // 如果已经有临时标记，先移除
    if (tempMarker.value) {
        mapService.removeMarker('temp-marker');
        tempMarker.value = null;
    }

    // 使用 mapService 添加标记
    const marker = mapService.addMarker('temp-marker', {
        position: [tempPin.custom_gcj.lng, tempPin.custom_gcj.lat],
        icon: new AMap.Icon({
            image: ICON_SVG.PIN,
            imageSize: new AMap.Size(24, 24)
        }),
        anchor: 'center',
        title: tempPin.name || '未命名pin标记',
        extData: tempPin,
        animation: 'AMAP_ANIMATION_DROP'
    });

    console.log('添加了一个临时pin标记marker:', marker);

    // 保存临时标记引用
    tempMarker.value = marker;
    // 打开输入表单以完善标记信息
    infoWindowVisible.value = true;
    infoWindowMode.value = 'input';
    infoWindowListType.value = 'pin';

    return marker;
};

// 4.2 添加 TempPhotoMarker 函数，使用 mapService 添加标记
const addTempPhotoMarker = (tempPhoto) => {
    // 如果已经有临时标记，先移除
    if (tempMarker.value) {
        mapService.removeMarker('temp-marker');
        tempMarker.value = null;
    }

    // 使用 mapService 添加标记
    const marker = mapService.addMarker('temp-marker', {
        position: [tempPhoto.custom_gcj.lng, tempPhoto.custom_gcj.lat],
        icon: new AMap.Icon({
            image: tempPhoto.thumbnail_url ? tempPhoto.thumbnail_url : ICON_SVG.CAMERA,
            imageSize: tempPhoto.thumbnail_url ? new AMap.Size(40, 40) : new AMap.Size(24, 24)
        }),
        anchor: 'center',
        title: tempPhoto.name || '新照片标记',
        extData: tempPhoto,
        animation: 'AMAP_ANIMATION_DROP'
    });

    // 创建信息窗体
    const infoWindow = new AMap.InfoWindow({
        isCustom: false,
        content: `<div style="text-align: center;">
            <img src="${tempPhoto.thumbnail_url}" style="width: 100px; height: 100px; object-fit: cover; margin-bottom: 10px;" />
            <p>经度：${tempPhoto.custom_gcj.lng}</p>
            <p>纬度：${tempPhoto.custom_gcj.lat}</p>
        </div>`,
        offset: new AMap.Pixel(0, -30)
    });

    // 添加marker事件
    marker.on('mouseover', () => infoWindow.open(map, marker.getPosition()));
    marker.on('mouseout', () => infoWindow.close());

    // 点击marker时打开编辑表单
    marker.on('click', () => {
        // 设置当前标记
        currentMarker.value = tempPhoto;
        // 显示输入表单
        infoWindowVisible.value = true;
        infoWindowMode.value = 'input';
        infoWindowListType.value = 'photo';
    });

    tempMarker.value = marker;

    // 保存临时标记引用
    infoWindowVisible.value = true;
    infoWindowMode.value = 'input';
    infoWindowListType.value = 'photo';
};

// 4.3 添加 TempPano 全景标记
const addTempPanoMarker = (tempPano) => {
    // 如果已经有临时标记，先移除
    if (tempMarker.value) {
        mapService.removeMarker('temp-marker');
        tempMarker.value = null;
    }
    // 使用 mapService 添加标记
    const marker = mapService.addMarker('temp-marker', {
        position: [tempPano.custom_gcj.lng, tempPano.custom_gcj.lat],
        icon: new AMap.Icon({
            image: ICON_IMG.DRONE,
            imageSize: new AMap.Size(24, 24)
        }),
        anchor: 'center',
        title: tempPano.name || '新照片标记',
        extData: tempPano,
        animation: 'AMAP_ANIMATION_DROP'
    });

    // 创建信息窗体
    const infoWindow = new AMap.InfoWindow({
        isCustom: false,
        content: `<div style="text-align: center;">
            <img src="${tempPano.thumbnail_url}" style="width: 100px; height: 100px; object-fit: cover; margin-bottom: 10px;" />
            <p>经度：${tempPano.custom_gcj.lng}</p>
            <p>纬度：${tempPano.custom_gcj.lat}</p>
        </div>`,
        offset: new AMap.Pixel(0, -30)
    });

    // 添加marker点击事件
    marker.on('mouseover', () => infoWindow.open(map, marker.getPosition()));
    marker.on('mouseout', () => infoWindow.close());

    // 点击marker时打开编辑表单
    marker.on('click', () => {
        // 设置当前标记
        currentMarker.value = tempPano;

        // 显示输入表单
        infoWindowVisible.value = true;
        infoWindowMode.value = 'input';
        infoWindowListType.value = 'pano';
    });
    tempMarker.value = marker;
    currentMarker.value = tempPano;

    infoWindowVisible.value = true;
    infoWindowMode.value = 'input';
    infoWindowListType.value = 'pano';

    // // 关闭添加模式
    // panoStatus.value = false;
    // const mapContainer = document.querySelector('#container');
    // mapContainer.classList.remove('pano-cursor');
    // mapContainer.style.cursor = 'default';
};

// 4.4 添加 TempPage 临时文章标记到地图上
const addTempPageMarker = (tempPage) => {
    // 如果已经有临时标记，先移除
    if (tempMarker.value) {
        mapService.removeMarker('temp-marker');
        tempMarker.value = null;
    }
    // 使用 mapService 添加标记
    const marker = mapService.addMarker('temp-marker', {
        position: [tempPage.custom_gcj.lng, tempPage.custom_gcj.lat],
        icon: new AMap.Icon({
            // image: ICON_SVG.DOCUMENT,
            image: ICON_IMG.PAGE,
            imageSize: new AMap.Size(24, 24)
            // image: tempPage.thumbnail_url ? tempPage.thumbnail_url : ICON_SVG.DOCUMENT,
            // imageSize: tempPage.thumbnail_url ? new AMap.Size(40, 40) : new AMap.Size(24, 24)
        }),
        anchor: 'center',
        title: tempPage.name || '新文章标记',
        extData: tempPage,
        animation: 'AMAP_ANIMATION_DROP'
    });

    // 创建信息窗体
    const infoWindow = new AMap.InfoWindow({
        isCustom: false,
        content: `<div style="text-align: center;">
            <h1>${tempPage.name}</h1>
            <p>经度：${tempPage.custom_gcj.lng} | 纬度：${tempPage.custom_gcj.lat}</p>
            <p>纬度：${tempPage.full_address}</p>
            <p>内容：${tempPage.page_content}</p>
        </div>`,
        offset: new AMap.Pixel(0, -30)
    });

    // 添加marker事件
    marker.on('mouseover', () => infoWindow.open(map, marker.getPosition()));
    marker.on('mouseout', () => infoWindow.close());

    // 点击 tempPage marker 仅用来显示和关闭编辑器
    marker.on('click', () => {
        // 设置当前标记
        currentMarker.value = tempPage;
        // pageStore.setCurrentMarker(currentMarker.value);

        // 点击用来切换编辑器的显示和隐藏
        pageEditorVisible.value = !pageEditorVisible.value;

        // 如果显示编辑器 -> 则将 TempPage 数据 和 编辑器 传递到 store，且 PageEditor 中的内容为 TempPage.editor.getHTML()
        if (pageEditorVisible.value) {
            pageStore.setPageContent(tempPage.page_content);
        }
        // // 如果关闭编辑器 并且 TempPage.editor.content 不为空 -> 则保存 TempPage ，且将其TempPage.editor.content 内容保存到 supabase 数据库中新建的 PageMarker.content
        // if (!pageEditorVisible.value) {
        //     // 如果关闭编辑器，则清空当前标记
        //     currentMarker.value = null;
        // }
    });

    tempMarker.value = marker;
    pageEditorVisible.value = true;
};

// watch 函数，用来查看 tempPage 中的 tempPage.editor.content 的变化，如果有变化，则将最新的值存在 tempPage.page_content 中
// watch(pageStore.editor.value, (newValue) => {
//     // console.log('🌏2: Amap: pageContent received from PageEditor ');
//     console.log('🌏PageEditor-> ', newValue);
//     console.log('🌏$doc-> ', newValue.$doc);
//     console.log('🌏$node-> heading 1 ', newValue.$nodes('heading 1'));
//     console.log('🌏$node-> heading', newValue.$nodes('heading'));
//     console.log('🌏$node-> paragraph', newValue.$nodes('paragraph'));
// });

// 主逻辑-5：添加各种类型的 pin 标记到地图上显示
// 5.1 添加 Pin 标记
// 修改 addPinMarkerToMap 函数，使用 mapService 添加标记
const addPinMarkerToMap = (item) => {
    // 检查必要的坐标数据
    if (!item || (!item.gcj && !item.custom_gcj)) {
        console.warn('Pin标记缺少坐标数据:', item);
        return null;
    }

    // 根据 icon 类型选择不同的图标
    let iconUrl = pinIcon; // 默认图标
    // 如果 item.icon 存在且在 ICON_SVG 中有对应的值，使用该图标
    if (item.icon && ICON_SVG[item.icon]) {
        iconUrl = ICON_SVG[item.icon];
    }

    // 生成唯一ID
    const markerId = `pin-marker-${item.id || Date.now()}`;

    // 安全获取坐标
    const lng = item.custom_gcj ? item.custom_gcj.lng : item.gcj ? item.gcj.lng : null;
    const lat = item.custom_gcj ? item.custom_gcj.lat : item.gcj ? item.gcj.lat : null;

    if (!lng || !lat) {
        console.warn('Pin标记坐标无效:', { lng, lat, item });
        return null;
    }

    // 使用 mapService 添加标记
    const marker = mapService.addMarker(markerId, {
        position: [lng, lat],
        icon: new AMap.Icon({
            image: iconUrl,
            imageSize: new AMap.Size(24, 24)
        }),
        anchor: 'center',
        title: item.name || '未命名标记',
        extData: item
    });

    // 创建信息窗体
    const infoWindow = new AMap.InfoWindow({
        isCustom: false,
        content: `<div>
            <h3>${item.name || '未命名标记'}</h3>
            <p>${item.full_address || '无地址信息'}</p>
        </div>`,
        offset: new AMap.Pixel(0, -30)
    });

    // 添加marker事件
    marker.on('mouseover', () => infoWindow.open(map, marker.getPosition()));
    marker.on('mouseout', () => infoWindow.close());

    // 点击marker时打开编辑表单
    marker.on('click', () => {
        // 先设置数据，再更新视图状态
        const markerData = marker.getExtData();
        // console.log('点击Pin标记:', markerData);
        currentMarker.value = { ...markerData }; // 使用解构复制，避免引用问题
        console.log('currentMarker.value:', currentMarker.value);

        // 使用 nextTick 确保 DOM 更新
        nextTick(() => {
            infoWindowMode.value = 'form';
            infoWindowListType.value = 'pin';

            // 如果当前已经显示，先重置再显示
            if (infoWindowVisible.value) {
                infoWindowVisible.value = false;
                setTimeout(() => {
                    infoWindowVisible.value = true;
                }, 50);
            } else {
                infoWindowVisible.value = true;
            }
        });
    });

    // 不再需要手动设置 map，因为 mapService.addMarker 已经设置了
    // marker.setMap(map);

    // 仍然保存到本地数组，用于组件内部管理
    markers.value.push(marker);
    // console.log('[log from Amap.vue] | pinItem: ', item);
    return marker;
};

// 5.2 添加 Photo 标记 并存储数据
// 修改 addPhotoMarkerToMap 函数，使用 mapService 添加标记
const addPhotoMarkerToMap = (item) => {
    // 检查必要的坐标数据
    if (!item || (!item.gcj && !item.custom_gcj)) {
        console.warn('Photo标记缺少坐标数据:', item);
        return null;
    }

    // 生成唯一ID
    const markerId = `photo-marker-${item.id || Date.now()}`;

    // 安全获取坐标
    const lng = item.custom_gcj && item.is_custom ? item.custom_gcj.lng : item.gcj ? item.gcj.lng : null;
    const lat = item.custom_gcj && item.is_custom ? item.custom_gcj.lat : item.gcj ? item.gcj.lat : null;

    if (!lng || !lat) {
        console.warn('Photo标记坐标无效:', { lng, lat, item });
        return null;
    }

    console.log('添加Photo标记到地图:', { id: item.id, lng, lat, name: item.name });

    const marker = mapService.addMarker(markerId, {
        position: [lng, lat],
        icon: new AMap.Icon({
            image: item.thumbnail_url ? item.thumbnail_url : ICON_SVG.CAMERA,
            imageSize: item.thumbnail_url ? new AMap.Size(40, 40) : new AMap.Size(24, 24)
        }),
        anchor: 'center',
        title: item.name || '未命名照片',
        extData: item,
        draggable: true // 设置标记可拖拽
    });

    // 创建信息窗体
    const infoWindow = new AMap.InfoWindow({
        isCustom: false,
        content: `<div>
            <h3>${item.name || '未命名照片'}</h3>
            <p>${item.full_address || '无地址信息'}</p>
        </div>`,
        offset: new AMap.Pixel(0, -30)
    });

    // 添加marker事件
    marker.on('mouseover', () => infoWindow.open(map, marker.getPosition()));
    marker.on('mouseout', () => infoWindow.close());

    // 鼠标右键事件: 将 marker 的值传递到 NodeImage.vue 中，使得 NodeImage.vue 中的previewPhoto = marker.photo_url,从而可以显示图片
    marker.on('rightclick', () => {
        const markerData = marker.getExtData();
        if (markerData) {
            // 设置当前 marker 到 store
            pageStore.setCurrentMarker(markerData);
            // 在编辑器中插入图片节点
            pageStore.insertImageNode();
        }
    });

    // 点击marker时打开编辑表单
    marker.on('click', () => {
        // 先设置数据，再更新视图状态
        const markerData = marker.getExtData();
        currentMarker.value = { ...markerData }; // 使用解构复制，避免引用问题
        console.log('currentMarker.value:', currentMarker.value);

        // 使用 nextTick 确保 DOM 更新
        nextTick(() => {
            infoWindowMode.value = 'form';
            infoWindowListType.value = 'photo';

            // 如果当前已经显示，先重置再显示
            if (infoWindowVisible.value) {
                infoWindowVisible.value = false;
                setTimeout(() => {
                    infoWindowVisible.value = true;
                }, 50);
            } else {
                infoWindowVisible.value = true;
            }
        });
    });

    // 仍然保存到本地数组，用于组件内部管理
    markers.value.push(marker);
    return marker;
};

// 5.3 添加 Pano 标记 并存储数据
// 添加新函数：将单个 pano 标记添加到地图
const addPanoMarkerToMap = (item) => {
    // 检查必要的坐标数据
    if (!item || !item.gcj || !item.gcj.lng || !item.gcj.lat) {
        console.warn('Pano标记缺少坐标数据:', item);
        return null;
    }

    // 创建图标对象
    const markerId = `pano-marker-${item.id || Date.now()}`;

    console.log('添加Pano标记到地图:', { id: item.id, lng: item.gcj.lng, lat: item.gcj.lat, name: item.name });

    const icon = new window.AMap.Icon({
        image: ICON_IMG.PANO, // 使用全景图标，或者缩略图
        size: new window.AMap.Size(24, 24),
        imageSize: new window.AMap.Size(24, 24)
    });
    const marker = mapService.addMarker(markerId, {
        position: [item.gcj.lng, item.gcj.lat],
        icon: icon,
        anchor: 'center',
        title: item.name || '未命名全景',
        extData: item,
        draggable: true // 设置标记可拖拽
    });

    // const marker = new window.AMap.Marker({
    //     position: [item.gcj.lng, item.gcj.lat],
    //     icon: icon,
    //     anchor: 'center',
    //     title: item.name || '未命名全景',
    //     extData: item,
    //     draggable: true // 设置标记可拖拽
    // });

    // 创建信息窗体
    const infoWindow = new window.AMap.InfoWindow({
        isCustom: false,
        content: `<div>
            <h3>${item.name || '未命名全景'} <span> | 拍摄时间: ${item.gps_timestamp || '无地址信息'}</span></h3>
            <p>${item.full_address || '无地址信息'}</p>
            <img src="${item.thumbnail_url}" alt="全景缩略图">
        </div>`,
        offset: new window.AMap.Pixel(0, -30)
    });

    // 添加marker事件
    marker.on('mouseover', () => infoWindow.open(map, marker.getPosition()));
    marker.on('mouseout', () => infoWindow.close());

    // 点击marker时打开编辑表单
    // marker.on('click', () => {
    //     // 确保关闭之前可能打开的InfoForm
    //     if (infoWindowVisible.value && infoWindowMode.value === 'form') {
    //         // 先关闭再打开，确保重新渲染
    //         infoWindowVisible.value = false;
    //         // 使用setTimeout确保DOM有时间更新
    //         setTimeout(() => {
    //             // 设置当前标记
    //             currentMarker.value = marker.getExtData();
    //             console.log('点击全景标记:', currentMarker.value); // 添加调试日志

    //             // 更改模式和类型
    //             infoWindowMode.value = 'form';
    //             infoWindowListType.value = 'pano'; // 明确设置为pano类型

    //             // 显示编辑表单
    //             infoWindowVisible.value = true;
    //         }, 10);
    //     } else {
    //         // 直接显示编辑表单
    //         currentMarker.value = marker.getExtData();
    //         console.log('点击全景标记:', currentMarker.value); // 添加调试日志

    //         // 更改模式和类型
    //         infoWindowMode.value = 'form';
    //         infoWindowListType.value = 'pano'; // 明确设置为pano类型

    //         // 显示编辑表单
    //         infoWindowVisible.value = true;
    //     }
    // });

    marker.on('click', () => {
        console.log('点击全景标记:', marker.getExtData());
        currentMarker.value = marker.getExtData();
        currentPanoMarker.value = marker.getExtData();
        const panoStore = usePanoStore();
        panoStore.currentPanoMarker = currentPanoMarker.value;
        togglePanoEquirect();
    });

    marker.setMap(map);
    markers.value.push(marker);

    return marker;
};

// 5.4 添加 Page 标记 并存储数据
// 函数逻辑:
// 1. 当 addTempPageMarker 执行后，地图上显示一个临时的 tempPage 标记;
// 2. 当点击 tempPage 标记时，编辑器中显示 tempPage 的内容;
// 3. 当编辑器处于打开状态，tempPage.page_content 且内容发生变化时，则将tempPage中的所有数据保存到数据库中(pageStore -> PageService -> supabase)
// 4. 此时时 tempPage 标记从地图上移除, 移除后，添加新的 pageMarker 到地图上;
const addPageMarkerToMap = (item) => {
    // 检查必要的坐标数据
    if (!item || !item.custom_gcj || !item.custom_gcj.lng || !item.custom_gcj.lat) {
        console.warn('Page标记缺少坐标数据:', item);
        return null;
    }

    console.log('添加Page标记到地图:', { id: item.id, lng: item.custom_gcj.lng, lat: item.custom_gcj.lat, name: item.name });
    const markerId = `page-marker-${item.id || Date.now()}`;

    const marker_position_lng = item.custom_gcj.lng;
    const marker_position_lat = item.custom_gcj.lat;
    const marker = mapService.addMarker(markerId, {
        position: [marker_position_lng, marker_position_lat],
        icon: new AMap.Icon({
            // image: ICON_SVG.DOCUMENT,
            image: ICON_IMG.PAGE,
            imageSize: new AMap.Size(24, 24)
        }),
        anchor: 'center',
        title: item.name || '文章',
        extData: item,
        draggable: true // 设置标记可拖拽
    });

    // 创建信息窗体
    const infoWindow = new AMap.InfoWindow({
        isCustom: false,
        content: `<div>
        <h3>${item.name || '文章'}</h3>
        <p>${item.full_address || '无地址信息'}</p>
    </div>`,
        offset: new AMap.Pixel(0, -30)
    });

    // 添加marker事件
    marker.on('mouseover', () => infoWindow.open(map, marker.getPosition()));
    marker.on('mouseout', () => infoWindow.close());
    marker.on('click', async () => {
        // 先设置数据，再更新视图状态
        const markerData = marker.getExtData();
        await pageStore.fetchPageMarkerById(markerData.id);
        currentPageMarker.value = pageStore.selectedPage;
        // currentPageMarker.value = { ...markerData }; // 使用解构复制，避免引用问题
        console.log('currentPageMarker.content:', currentPageMarker.value.page_content, currentPageMarker.value);
        pageContent.value = currentPageMarker.value.page_content;
        pageEditorVisible.value = !pageEditorVisible.value;
        if (pageEditorVisible.value) {
            pageStore.setPageContent(currentPageMarker.value.page_content);
        }

        // 使用 nextTick 确保 DOM 更新
        // nextTick(() => {
        //     infoWindowMode.value = 'form';
        //     infoWindowListType.value = 'photo';

        //     // 如果当前已经显示，先重置再显示
        //     if (infoWindowVisible.value) {
        //         infoWindowVisible.value = false;
        //         setTimeout(() => {
        //             infoWindowVisible.value = true;
        //         }, 50);
        //     } else {
        //         infoWindowVisible.value = true;
        //     }
        // });
    });

    // 仍然保存到本地数组，用于组件内部管理
    markers.value.push(marker);
    return marker;
};
let updateTimer = null; // 在 setup 作用域声明
watch([pageContent, currentPageMarker], async ([newPageContent, newCurrentPageMarker], [prevPageContent, prevCurrentPageMarker]) => {
    // console.log('🌏$doc.textContent -> : ', pageStore.editor.$doc.textContent, pageStore.editor.$doc);
    console.log('pageContent.value:', pageContent.value);
    console.log('<<< prevPageContent:', prevPageContent);
    console.log('newPageContent >>>:', newPageContent);
    console.log('currentPageMarker:', currentPageMarker);
    console.log('<<< prevCurrentPageMarker:', prevCurrentPageMarker);
    console.log('newCurrentPageMarker >>>:', newCurrentPageMarker);
    // 更新函数
    if (pageStore.editor.$node('paragraph')?.textContent !== '""' && newCurrentPageMarker.value?.id && pageEditorVisible.value === true) {
        // 如果没有定时器，才启动
        if (!updateTimer && newPageContent !== newCurrentPageMarker.value.page_content) {
            console.log('⏰⏰⏰⏰🔛🔛🔛🔛 Timer is running 【update】!');
            updateTimer = setTimeout(async () => {
                if (!pageContent.value) {
                    console.log('pageContent.value:', pageContent.value);
                    updateTimer = null;
                    console.log('💥⏰💥 【update】Stoped 💥⏰💥');
                    return;
                }
                let pageName = parsePageName(pageContent.value);
                newCurrentPageMarker.value.page_content = pageContent.value;
                newCurrentPageMarker.value.name = pageName;
                currentPageMarker.value = { ...newCurrentPageMarker.value };
                await pageStore.updatePageMarker(newCurrentPageMarker.value.id, newCurrentPageMarker.value);
                console.log(' 【update】Timer is UP 🔚🔚🔚🔚🔚⏰⏰⏰⏰⏰');
                mapService.removeMarker(`page-marker-${currentPageMarker.value.id}`);
                addPageMarkerToMap(newCurrentPageMarker.value);
                updateTimer = null; // 结束后允许下次再启动
            }, 10000);
        }
    }
    // 新增函数；
    if (pageStore.editor.$node('paragraph')?.textContent !== '""' && pageStore.editor.$doc.children.length > 3 && !currentPageMarker.value?.id) {
        console.log('☀️ pageContent 有变化，且 currentMarker 无 id, 则代表这是一个新的 pageMarker,这时使用新增函数;');
        // pageStore.addPageMarker(currentPageMarker.value);
        console.log('💻 currentPageMarker.value 💻 : ', currentPageMarker.value);
        if (!updateTimer) {
            console.log('⏰⏰⏰⏰🔛🔛🔛🔛 Timer is running 【add】!');
            updateTimer = setTimeout(async () => {
                if (!pageContent.value) {
                    console.log('pageContent.value:', pageContent.value);
                    updateTimer = null;
                    console.log('💥⏰💥 【add】Stoped 💥⏰💥');
                    return;
                }
                let pageName = parsePageName(pageContent.value);
                currentPageMarker.value.name = pageName;
                const newPage = await pageStore.addPageMarker(currentPageMarker.value);
                console.log('💻 newPage 💻 : ', newPage);
                currentPageMarker.value = { ...newPage };
                //pageStore.setPageContent(currentPageMarker.value.page_content); //tempPage 的 page_content
                // 添加 Page 标记到地图
                if (newPage) {
                    addPageMarkerToMap(newPage); // 更新列表数据
                }
                // 💻  移除临时标记
                if (tempMarker.value) {
                    tempMarker.value.setMap(null);
                    tempMarker.value = null;
                }
                console.log('【add】 Timer is UP 🔚🔚🔚🔚🔚⏰⏰⏰⏰⏰');
                updateTimer = null; // 结束后允许下次再启动
            }, 10000);
        }
    }
});

// 解析 pageContent 作为 currentPageMarker.name 的函数
const parsePageName = (pageContent) => {
    // 匹配第一个 h1~h5 或 p 标签（允许嵌套标签）
    const match = pageContent.match(/<(h[1-5]|p)[^>]*>([\s\S]*?)<\/\1>/i);
    if (match) {
        // 提取标签内的纯文本内容（去除所有 html 标签）
        const innerHtml = match[2];
        // 利用浏览器 DOM 解析（更健壮），如果在 node 环境可用第三方库
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = innerHtml;
        const text = tempDiv.textContent.trim();
        return text || '无标题';
    }
    return '无标题';
};

// 关闭 PageEditor
const closePageEditor = () => {
    pageEditorVisible.value = false;
};

// 主逻辑-6：更新 pin 标记到地图上显示
// 6.1 增量更新 pin 标记
const updatePinMarkers = (newData) => {
    if (!newData || newData.length === 0) return;

    // 只处理 pin 类型的标记
    const pinMarkers = markers.value.filter((marker) => {
        const data = marker.getExtData ? marker.getExtData() : null;
        return data && data.markerType === 'pin'; // 添加类型判断
    });

    // 获取现有标记的ID集合
    const existingMarkerIds = new Set(pinMarkers.map((marker) => (marker.getExtData ? marker.getExtData().id : null)).filter((id) => id !== null));
    // 获取新数据的ID集合
    const newDataIds = new Set(newData.map((item) => item.id));

    // 1. 添加新标记
    newData.forEach((item) => {
        if (!existingMarkerIds.has(item.id)) {
            // 这是一个新标记，添加到地图
            addPinMarkerToMap(item);
        }
    });

    // 2. 只删除 pin 类型的不存在标记
    for (let i = markers.value.length - 1; i >= 0; i--) {
        const marker = markers.value[i];
        const markerData = marker.getExtData ? marker.getExtData() : null;

        if (markerData && markerData.markerType === 'pin' && !newDataIds.has(markerData.id)) {
            marker.setMap(null);
            markers.value.splice(i, 1);
        }
    }

    // 3. 更新现有标记（如果有变化）
    markers.value.forEach((marker) => {
        const markerData = marker.getExtData ? marker.getExtData() : null;
        if (!markerData || !markerData.id) return;

        // 查找对应的新数据
        const newItem = newData.find((item) => item.id === markerData.id);
        if (!newItem) return;

        // 检查是否有变化（简化版，实际可能需要更详细的比较）
        if (JSON.stringify(markerData) !== JSON.stringify(newItem)) {
            // 有变化，更新标记
            // 从地图移除旧标记
            marker.setMap(null);
            // 添加新标记
            const index = markers.value.indexOf(marker);
            if (index !== -1) {
                const newMarker = addPinMarkerToMap(newItem);
                markers.value.splice(index, 1, newMarker);
            }
        }
    });
};
// 6.2 增量更新照片标记
const updatePhotoMarkers = (newData) => {
    if (!newData || newData.length === 0) return;

    // 获取现有标记的ID集合
    const existingMarkerIds = new Set(markers.value.map((marker) => (marker.getExtData ? marker.getExtData().id : null)).filter((id) => id !== null));

    // 获取新数据的ID集合
    const newDataIds = new Set(newData.map((item) => item.id));

    // 1. 添加新标记
    newData.forEach((item) => {
        if (!existingMarkerIds.has(item.id)) {
            // 这是一个新标记，添加到地图
            addPhotoMarkerToMap(item);
        }
    });

    // 2. 删除不再存在的标记
    for (let i = markers.value.length - 1; i >= 0; i--) {
        const marker = markers.value[i];
        const markerData = marker.getExtData ? marker.getExtData() : null;

        if (markerData && markerData.id && !newDataIds.has(markerData.id)) {
            // 这个标记不再存在于新数据中，从地图移除
            marker.setMap(null);
            markers.value.splice(i, 1);
        }
    }

    // 3. 更新现有标记（如果有变化）
    markers.value.forEach((marker) => {
        const markerData = marker.getExtData ? marker.getExtData() : null;
        if (!markerData || !markerData.id) return;

        // 查找对应的新数据
        const newItem = newData.find((item) => item.id === markerData.id);
        if (!newItem) return;

        // 检查是否有变化（简化版，实际可能需要更详细的比较）
        if (JSON.stringify(markerData) !== JSON.stringify(newItem)) {
            // 有变化，更新标记
            // 从地图移除旧标记
            marker.setMap(null);
            // 添加新标记
            const index = markers.value.indexOf(marker);
            if (index !== -1) {
                const newMarker = addPhotoMarkerToMap(newItem);
                markers.value.splice(index, 1, newMarker);
            }
        }
    });
};

// 主逻辑-7：加载 各个类型的 marker 标记
// 7.1 加载 pin 标记
// 改进的 loadPinMarkers - 支持懒加载和缓存
const loadPinMarkers = async (isInitialLoad = false) => {
    try {
        // 检查缓存是否有效
        if (!isInitialLoad && isCacheValid(dataCache.value.pins)) {
            console.log('使用缓存的pin数据');
            showCachedPinMarkers();
            return;
        }

        // 显示懒加载进度
        showLazyLoadingProgress('正在加载标记数据...', 0, 'pin');

        const pinStore = usePinStore();

        // 只有在初始加载时才清除现有的pin markers
        if (isInitialLoad) {
            clearPinMarkers();
        }

        // 如果缓存中有数据但已过期，先显示缓存数据，再更新
        if (dataCache.value.pins.loaded && dataCache.value.pins.data.length > 0) {
            console.log('显示缓存数据，同时后台更新...');
            showCachedPinMarkers();
        }

        // 获取最新数据
        await pinStore.fetchAllPins();
        const pinData = pinStore.allPins || [];

        // 更新缓存
        dataCache.value.pins = {
            data: pinData,
            timestamp: Date.now(),
            version: dataCache.value.pins.version + 1,
            loaded: true
        };

        // 分批加载标记到地图
        await loadMarkersInBatches(pinData, 'pin', isInitialLoad);

        hideLazyLoadingProgress();

        // 更新列表数据
        infoWindowItems.value = pinData;

        // 非初始加载时，显示信息窗口
        if (!isInitialLoad) {
            infoWindowVisible.value = true;
            infoWindowMode.value = 'list';
            infoWindowListType.value = 'pin';
        }

        toast.add({
            severity: 'info',
            summary: '加载完成',
            detail: `已加载 ${pinData.length} 个标记`,
            life: 3000
        });
    } catch (error) {
        hideLazyLoadingProgress();
        console.error('加载 pin marker 失败:', error);
        toast.add({
            severity: 'error',
            summary: '加载失败',
            detail: '加载标记失败，请稍后重试',
            life: 3000
        });
    }
};

// 7.2 加载 photo 标记
// 改进的 loadPhotoMarkers - 支持懒加载和缓存
const loadPhotoMarkers = async (isInitialLoad = false) => {
    try {
        // 检查缓存是否有效
        if (!isInitialLoad && isCacheValid(dataCache.value.photos)) {
            console.log('使用缓存的photo数据');
            showCachedPhotoMarkers();
            return;
        }

        // 显示懒加载进度
        showLazyLoadingProgress('正在加载照片数据...', 0, 'photo');

        const photoStore = usePhotoStore();

        // 只有在初始加载时才清除现有的photo markers
        if (isInitialLoad) {
            clearPhotoMarkers();
        }

        // 如果缓存中有数据但已过期，先显示缓存数据，再更新
        if (dataCache.value.photos.loaded && dataCache.value.photos.data.length > 0) {
            console.log('显示缓存照片数据，同时后台更新...');
            showCachedPhotoMarkers();
        }

        // 获取最新数据
        await photoStore.fetchAllPhotos();
        const data = photoStore.allPhotos || [];

        // 更新缓存
        dataCache.value.photos = {
            data: data,
            timestamp: Date.now(),
            version: dataCache.value.photos.version + 1,
            loaded: true
        };

        // 分批加载标记到地图
        await loadMarkersInBatches(data, 'photo', isInitialLoad);

        hideLazyLoadingProgress();

        // 更新列表数据
        infoWindowItems.value = data;

        // 非初始加载时，显示信息窗口
        if (!isInitialLoad) {
            infoWindowVisible.value = true;
            infoWindowMode.value = 'list';
            infoWindowListType.value = 'photo';

            toast.add({
                severity: 'info',
                summary: '加载完成',
                detail: `已加载 ${data.length} 个照片标记`,
                life: 3000
            });
        }
    } catch (error) {
        hideLazyLoadingProgress();
        console.error('加载照片标记失败:', error);
        if (!isInitialLoad) {
            toast.add({
                severity: 'error',
                summary: '加载失败',
                detail: '加载照片标记失败，请稍后重试',
                life: 3000
            });
        }
    }
};
// 7.3 加载 pano 标记
// 添加全景图标记加载函数，使用Pinia store
const loadPanoMarkers = async (isInitialLoad = false) => {
    try {
        isLoading.value = true;

        // 只有在初始加载时才清除
        if (isInitialLoad) {
            clearPanoMarkers();
        }

        // 使用Pinia store加载pano标记
        const panoStore = usePanoStore();
        await panoStore.fetchAllPanos();
        const data = panoStore.allPanos;

        // 如果不是为了列表显示，先不更新infoWindowItems
        if (!isInitialLoad || infoWindowListType.value === 'pano') {
            infoWindowItems.value = data || [];
        }

        // 将标记添加到地图
        if (data && data.length > 0) {
            data.forEach((item) => {
                addPanoMarkerToMap(item);
            });
        }

        // 仅在非初始加载时显示信息窗口
        if (!isInitialLoad) {
            infoWindowVisible.value = true;
            infoWindowMode.value = 'list';
            infoWindowListType.value = 'pano';

            toast.add({ severity: 'info', summary: '加载完成', detail: `已加载 ${data ? data.length : 0} 个全景标记`, life: 3000 });
        }
    } catch (error) {
        console.error('加载全景标记失败:', error);
        if (!isInitialLoad) {
            toast.add({ severity: 'error', summary: '加载失败', detail: '加载全景标记失败，请稍后重试', life: 3000 });
        }
    } finally {
        isLoading.value = false;
    }
};
//============================================================================================
// 修改 handleInfoWindowSubmit 方法，使用Pinia store
const handleInfoWindowSubmit = async (formData) => {
    try {
        // 显示加载状态
        isLoading.value = true;
        // 📌 保存 Pin 标记
        if (infoWindowListType.value === 'pin') {
            console.log('提交引脚标记数据:', formData);
            // 📌 1. 确保转换的坐标数据
            if (!formData.gcj) {
                formData.gcj = {
                    lng: formData.custom_gcj.lng || 0,
                    lat: formData.custom_gcj.lat || 0
                };
            }
            // 📌 2. 将标签数组转换为逗号分隔的字符串
            if (Array.isArray(formData.tag)) {
                formData.tag = formData.tag.join(',');
            } else if (typeof formData.tag !== 'string') {
                formData.tag = '';
            }
            // 📌 3. 使用 Pinia store 添加pin标记
            const pinStore = usePinStore();
            const newPin = await pinStore.addPin(formData);
            // 📌 4. 添加 Pin 记到地图
            if (newPin) {
                addPinMarkerToMap(newPin); // 更新列表数据
                infoWindowItems.value = [...infoWindowItems.value, newPin];
            }
            // 📌 5. 移除临时标记
            if (tempMarker.value) {
                tempMarker.value.setMap(null);
                tempMarker.value = null;
            }
            // 📌 6. 刷新标记列表
            await loadPinMarkers();
            toast.add({ severity: 'success', summary: '添加成功', detail: '新引脚标记已成功添加', life: 3000 });
        } // ⛵️ 保存 Photo 标记
        else if (infoWindowListType.value === 'photo') {
            console.log('提交照片标记数据:', formData);
            // ⛵ 2. 将标签数组转换为逗号分隔的字符串
            if (Array.isArray(formData.tag)) {
                formData.tag = formData.tag.join(',');
            } else if (typeof formData.tag !== 'string') {
                formData.tag = '';
            }
            // ⛵ 3. 使用Pinia store添加photo标记
            const photoStore = usePhotoStore();
            const newPhoto = await photoStore.addPhoto(formData);
            // ⛵ 4. 添加 Photo 标记到地图
            if (newPhoto) {
                addPhotoMarkerToMap(newPhoto); // 更新列表数据
                infoWindowItems.value = [...infoWindowItems.value, newPhoto];
            }
            // ⛵ 5. 移除临时标记
            if (tempMarker.value) {
                tempMarker.value.setMap(null);
                tempMarker.value = null;
            }
            // ⛵ 6. 刷新标记列表
            await loadPhotoMarkers();
            toast.add({ severity: 'success', summary: '添加成功', detail: '新照片标记已成功添加', life: 3000 });
        } else if (infoWindowListType.value === 'pano') {
            console.log('提交全景标记数据:', formData);
            // ✈️ 1. 将标签数组转换为逗号分隔的字符串
            if (Array.isArray(formData.tag)) {
                formData.tag = formData.tag.join(',');
            } else if (typeof formData.tag !== 'string') {
                formData.tag = '';
            }
            // ✈️ 2.使用Pinia store添加pano标记
            const panoStore = usePanoStore();
            const newPano = await panoStore.addPano(formData);
            // ✈️ 3. 添加 Pano 标记到地图
            if (newPano) {
                addPanoMarkerToMap(newPano); // 更新列表数据
                infoWindowItems.value = [...infoWindowItems.value, newPano];
            }
            // ✈️ 4. 移除临时标记
            if (tempMarker.value) {
                tempMarker.value.setMap(null);
                tempMarker.value = null;
            }
            // ✈️ 5.刷新标记列表
            await loadPanoMarkers();
            toast.add({ severity: 'success', summary: '添加成功', detail: '新全景标记已成功添加', life: 3000 });
        } else {
            console.warn('未知的标记类型:', infoWindowListType.value);
        }

        // 关闭标记添加模式
        if (pinStatus.value) {
            pinStatus.value = false;
            const mapContainer = document.querySelector('#container');
            mapContainer.classList.remove('pin-cursor');
            mapContainer.style.cursor = 'default';
        }

        // 关闭信息窗口
        infoWindowVisible.value = false;
    } catch (error) {
        console.error('处理表单提交失败:', error);
        toast.add({ severity: 'error', summary: '提交失败', detail: error.message || '添加标记失败，请稍后重试', life: 3000 });
    } finally {
        isLoading.value = false;
    }
};
// 修改 handleInfoWindowUpdate 方法，使用Pinia store
const handleInfoWindowUpdate = async (formData) => {
    try {
        // 1. 显示加载状态
        isLoading.value = true;

        // 2. 查找地图上对应的marker
        const markerIndex = markers.value.findIndex((marker) => {
            // 这里需要获取marker关联的数据来比较
            // 我们可以通过比较位置来找到对应的marker
            if (formData && formData.gcj && marker.getPosition) {
                const position = marker.getPosition();
                return position && Math.abs(position.getLng() - formData.gcj.lng) < 0.000001 && Math.abs(position.getLat() - formData.gcj.lat) < 0.000001;
            }
            return false;
        });

        // 3. 如果找到了对应的marker，从地图上删除
        if (markerIndex !== -1) {
            // 获取原marker
            const oldMarker = markers.value[markerIndex];
            // 从地图上移除旧marker
            oldMarker.setMap(null);
            console.log('已更新单个标记');
        } else {
            // 如果没找到，进行增量更新而不是全量重载
            console.log('未找到对应标记，进行增量更新');
        }

        // 使用Pinia store更新数据
        if (infoWindowListType.value === 'pin') {
            const pinStore = usePinStore();
            await pinStore.updatePin({ id: formData.id, pinData: formData });
        } else if (infoWindowListType.value === 'photo') {
            const photoStore = usePhotoStore();
            await photoStore.updatePhoto({ id: formData.id, photoData: formData });
        } else if (infoWindowListType.value === 'pano') {
            const panoStore = usePanoStore();
            await panoStore.updatePano({ id: formData.id, panoData: formData });
        }

        // 更新列表数据 - 无论如何都更新列表
        if (infoWindowListType.value === 'pin') {
            await loadPinMarkers(false);
        } else if (infoWindowListType.value === 'photo') {
            await loadPhotoMarkers(false);
        } else if (infoWindowListType.value === 'pano') {
            await loadPanoMarkers(false);
        }

        // 关闭信息窗口
        infoWindowVisible.value = false;

        toast.add({ severity: 'success', summary: '更新成功', detail: '标记信息已成功更新', life: 3000 });
    } catch (error) {
        console.error('处理表单更新失败:', error);
        toast.add({ severity: 'error', summary: '更新失败', detail: '更新标记失败，请稍后重试', life: 3000 });
    } finally {
        // 隐藏加载状态
        isLoading.value = false;
    }
};
// 添加处理删除的方法，使用Pinia store
const handleInfoWindowDelete = async (markerId) => {
    try {
        // 显示加载状态
        isLoading.value = true;

        console.log('删除标记开始:', markerId);
        console.log('当前标记数量:', markers.value.length);
        console.log(
            '标记数据:',
            markers.value.map((m) => (m.getExtData ? m.getExtData().id : 'unknown'))
        );

        // 使用Pinia store删除数据
        if (infoWindowListType.value === 'pin') {
            const pinStore = usePinStore();
            await pinStore.removePin(markerId);
        } else if (infoWindowListType.value === 'photo') {
            const photoStore = usePhotoStore();
            await photoStore.removePhoto(markerId);
        } else if (infoWindowListType.value === 'pano') {
            const panoStore = usePanoStore();
            await panoStore.removePano(markerId);
        }

        console.log('数据库删除成功，开始从地图移除标记');

        // 查找要删除的marker - 通过ID查找更可靠
        const markerIndex = markers.value.findIndex((marker) => {
            const markerData = marker.getExtData ? marker.getExtData() : null;
            console.log('检查标记:', markerData ? markerData.id : 'unknown', '是否等于', markerId);
            return markerData && markerData.id === markerId;
        });

        console.log('找到的标记索引:', markerIndex);

        // 从地图上移除marker
        if (markerIndex !== -1) {
            const markerToRemove = markers.value[markerIndex];
            console.log('移除前标记的map属性:', markerToRemove.getMap());
            // 确保从地图上移除标记
            markerToRemove.setMap(null);
            console.log('移除后标记的map属性:', markerToRemove.getMap());
            // 从数组中移除标记
            markers.value.splice(markerIndex, 1);
            console.log('已从地图上移除标记，剩余标记数量:', markers.value.length);
        } else {
            console.warn('未找到要删除的标记，进行增量更新而不是全量重载');
            // 如果找不到特定标记，进行增量更新而不是全量重载
            if (infoWindowListType.value === 'photo') {
                loadPhotoMarkers();
            } else if (infoWindowListType.value === 'pano') {
                loadPanoMarkers();
            } else {
                loadPinMarkers();
            }
        }

        // 从列表中移除
        infoWindowItems.value = infoWindowItems.value.filter((item) => item.id !== markerId);

        // 关闭InfoWindow
        infoWindowVisible.value = false;

        // 使用toast提示成功
        toast.add({ severity: 'success', summary: '删除成功', detail: '标记已成功删除', life: 3000 });

        console.log('标记已删除，剩余标记数量:', markers.value.length);
    } catch (error) {
        console.error('删除标记失败:', error);
        toast.add({ severity: 'error', summary: '删除失败', detail: error.message || '请稍后重试', life: 3000 });
    } finally {
        // 隐藏加载状态
        isLoading.value = false;
    }
};

// 处理 InfoWindow 关闭
const handleInfoWindowClose = () => {
    // 关闭信息窗口
    infoWindowVisible.value = false;

    // 如果存在临时标记，移除它
    if (tempMarker.value) {
        tempMarker.value.setMap(null);
        tempMarker.value = null;
    }
};

// 📌 定位 处理 InfoWindow 中选择的项目
const handleInfoWindowSelectItem = (item) => {
    // 定位到选中的标记
    if (item && item.gcj) {
        map.setCenter([item.gcj.lng, item.gcj.lat]);
        map.setZoom(16);
    }
};

// 处理 InfoWindow 中编辑的项目
const handleInfoWindowEditItem = (item) => {
    // 设置当前标记
    currentMarker.value = item;

    // 确定标记类型
    if (item.markerType) {
        infoWindowListType.value = item.markerType;
    } else if (item.thumbnail_url) {
        infoWindowListType.value = 'photo';
    } else if (item.isPano) {
        infoWindowListType.value = 'pano';
    } else {
        infoWindowListType.value = 'pin';
    }

    // 显示编辑表单
    infoWindowVisible.value = true;
    infoWindowMode.value = 'form';
};

// 处理批量删除
const handleInfoWindowDeleteItems = async (markerIds) => {
    try {
        // 显示加载状态
        isLoading.value = true;

        // 检查是否为带类型信息的标记数组
        const hasTypeInfo = markerIds.length > 0 && 'markerType' in markerIds[0];

        if (hasTypeInfo) {
            // 按类型分组
            const pinIds = markerIds.filter((item) => item.markerType === 'pin').map((item) => item.id);
            const photoIds = markerIds.filter((item) => item.markerType === 'photo').map((item) => item.id);
            const panoIds = markerIds.filter((item) => item.markerType === 'pano').map((item) => item.id);

            // 并行删除不同类型的标记
            const deletePromises = [];

            if (pinIds.length > 0) {
                const pinStore = usePinStore();
                deletePromises.push(pinStore.removePins(pinIds));
            }

            if (photoIds.length > 0) {
                const photoStore = usePhotoStore();
                deletePromises.push(photoStore.removePhotos(photoIds));
            }

            if (panoIds.length > 0) {
                const panoStore = usePanoStore();
                deletePromises.push(panoStore.removePanos(panoIds));
            }

            await Promise.all(deletePromises);

            // 重新加载标记
            await loadPinMarkers(true);
            await loadPhotoMarkers(true);
            await loadPanoMarkers(true);

            // 重新加载所有标记列表
            await loadAllMarkers();
        } else {
            // 使用原来的逻辑处理单一类型标记删除
            if (infoWindowListType.value === 'pin') {
                const pinStore = usePinStore();
                await pinStore.removePins(markerIds);
            } else if (infoWindowListType.value === 'photo') {
                const photoStore = usePhotoStore();
                await photoStore.removePhotos(markerIds);
            } else if (infoWindowListType.value === 'pano') {
                const panoStore = usePanoStore();
                await panoStore.removePanos(markerIds);
            }

            // 从地图上删除标记
            for (const markerId of markerIds) {
                const markerIndex = markers.value.findIndex((marker) => {
                    const markerData = marker.getExtData ? marker.getExtData() : null;
                    return markerData && markerData.id === markerId;
                });

                if (markerIndex !== -1) {
                    // 从地图移除标记
                    markers.value[markerIndex].setMap(null);
                    // 从数组移除标记
                    markers.value.splice(markerIndex, 1);
                }
            }

            // 从列表中移除
            infoWindowItems.value = infoWindowItems.value.filter((item) => !markerIds.includes(item.id));
        }

        // 提示成功
        toast.add({
            severity: 'success',
            summary: '批量删除成功',
            detail: `已删除 ${hasTypeInfo ? markerIds.length : markerIds.length} 个标记`,
            life: 3000
        });
    } catch (error) {
        console.error('批量删除标记失败:', error);
        toast.add({
            severity: 'error',
            summary: '批量删除失败',
            detail: error.message || '请稍后重试',
            life: 3000
        });
    } finally {
        // 隐藏加载状态
        isLoading.value = false;
    }
};

// 处理返回 list 列表
const handleBackToList = () => {
    infoWindowMode.value = 'list';
};

//============================================================================================

// 新增搜索处理函数
const handleSearch = async () => {
    const Amap = mapService.getAMap();
    const map = mapService.getMap();
    // 构造地点查询类
    // const autoOptions = {
    //     input: 'search-input'
    //     // input: 'panel'
    // };
    // new AMap.AutoComplete(autoOptions);
    if (!geocoder) {
        geocoder = new Amap.Geocoder({
            city: '武汉市', // 限制搜索范围
            radius: 1000 // 搜索半径
        });
    }
    console.log('搜索1 - searchText.value: ', searchText.value);
    try {
        // 执行地理编码
        const result = await new Promise((resolve, reject) => {
            geocoder.getLocation(searchText.value, (status, result) => {
                status === 'complete' ? resolve(result) : reject(new Error('地理编码失败'));
            });
        });
        console.log('搜索2 - result: ', result);

        // 清除旧标记
        //clearSearchMarkers();

        if (result.info === 'OK' && result.geocodes.length > 0) {
            const { location, formattedAddress } = result.geocodes[0];

            // 添加新标记
            const searchMarker = new Amap.Marker({
                position: [location.lng, location.lat],
                icon: new Amap.Icon({
                    // image: `${import.meta.env.BASE_URL}demo/images/dock/pin-roll.apng`,
                    image: ICON_SVG.PIN,
                    imageSize: new Amap.Size(24, 24) // 设置图标尺寸为 24x24
                }),
                anchor: 'center', // 将锚点设置为中心
                title: formattedAddress
            });
            searchMarker.setMap(map);
            searchMarkers.value.push(searchMarker);

            // 调整地图视野
            map.setCenter([location.lng, location.lat]);
            map.setZoom(16);

            AMap.plugin(['AMap.PlaceSearch'], function () {
                const placeSearch = new AMap.PlaceSearch({
                    pageSize: 5, // 单页显示结果条数
                    pageIndex: 1, // 页码
                    city: '027', // 兴趣点城市
                    citylimit: true, //是否强制限制在设置的城市内搜索
                    map: map, // 展现结果的地图实例
                    panel: 'panel', // 结果列表将在此容器中进行展示。
                    autoFitView: true // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
                });
                //关键字查询
                placeSearch.search(searchText.value);
            });
            // Amap.plugin(['AMap.AutoComplete'], function () {
            //     const autoOptions = {
            //         input: 'search-input' // 这里要用 input 的 id
            //     };
            //     const autoComplete = new Amap.AutoComplete(autoOptions);
            //     // 你可以在这里绑定 autoComplete 的事件
            // });
        }
    } catch (error) {
        console.error('搜索失败:', error);
        // 可以添加用户提示
    }
};

// 新增清除搜索标记函数
const clearSearchMarkers = () => {
    searchMarkers.value.forEach((marker) => marker.setMap(null));
    searchMarkers.value = [];
};

// 修改 handleImageUpload 方法
const handleImageUpload = () => {
    // 不再清除现有的markers，只进行增量更新
    loadPinMarkers();
};

// 定义类型与表名的映射关系
const markerTableMap = {
    pin: 'amap_marker_pins',
    photo: 'amap_marker_photos',
    pano: 'amap_marker_panos',
    page: 'amap_marker_pages', // 预留将来可能添加的类型
    all: 'amap_marker_pins' // 'all'类型默认使用pins表，实际操作会根据具体选择的项目类型决定
};
// 使用映射获取表名，如果没有匹配的类型则提供默认值或错误处理
const tableName = markerTableMap[infoWindowListType.value] || 'amap_pin';
void tableName;

// 新增函数：仅更新列表数据，使用Pinia store
const updatePinList = async () => {
    try {
        // 根据当前标记类型使用对应的store获取数据
        if (infoWindowListType.value === 'pin') {
            const pinStore = usePinStore();
            await pinStore.fetchAllPins();
            infoWindowItems.value = pinStore.allPins || [];
        } else if (infoWindowListType.value === 'photo') {
            const photoStore = usePhotoStore();
            await photoStore.fetchAllPhotos();
            infoWindowItems.value = photoStore.allPhotos || [];
        } else if (infoWindowListType.value === 'pano') {
            const panoStore = usePanoStore();
            await panoStore.fetchAllPanos();
            infoWindowItems.value = panoStore.allPanos || [];
        }
    } catch (error) {
        console.error('更新列表数据失败:', error);
    }
};
void updatePinList;

// 修改 clearPinMarkers 和 clearPhotoMarkers 函数，使其只清除特定类型的标记
const clearPinMarkers = () => {
    // 找出所有 pin 类型的标记
    const pinMarkers = markers.value.filter((marker) => {
        const data = marker.getExtData ? marker.getExtData() : null;
        // 根据数据结构判断是否为 pin 标记
        return data && !data.thumbnail_url; // 简单判断：没有缩略图的是 pin 标记
    });

    // 从地图上移除这些标记
    pinMarkers.forEach((marker) => marker.setMap(null));

    // 从数组中移除这些标记
    markers.value = markers.value.filter((marker) => {
        const data = marker.getExtData ? marker.getExtData() : null;
        return !(data && !data.thumbnail_url);
    });
};

const clearPhotoMarkers = () => {
    // 找出所有照片类型的标记
    const photoMarkers = markers.value.filter((marker) => {
        const data = marker.getExtData ? marker.getExtData() : null;
        // 根据数据结构判断是否为照片标记
        return data && data.thumbnail_url;
    });

    // 从地图上移除这些标记
    photoMarkers.forEach((marker) => marker.setMap(null));

    // 从数组中移除这些标记
    markers.value = markers.value.filter((marker) => {
        const data = marker.getExtData ? marker.getExtData() : null;
        return !(data && data.thumbnail_url);
    });
};

// 处理文档点击事件
const handleDocumentClick = (e) => {
    console.log('📌 handleDocumentClick: ', currentMarker.value);
    /*
    // 如果InfoForm已显示
    if (infoWindowVisible.value) {
        // 检查点击是否在InfoForm外部
        const infoWindow = document.querySelector('.info-window');
        if (infoWindow && !infoWindow.contains(event.target) && !event.target.closest('.amap-marker')) {
            // 如果点击在InfoForm和地图标记之外，则关闭InfoForm
            infoWindowVisible.value = false;
        }
    }
    */
    e.stopPropagation();
};

// 清除全景标记函数
const clearPanoMarkers = () => {
    // 找出所有全景类型的标记
    const panoMarkers = markers.value.filter((marker) => {
        const data = marker.getExtData ? marker.getExtData() : null;
        // 根据数据结构判断是否为全景标记
        return data && data.isPano; // 假设全景标记有 isPano 属性
    });

    // 从地图上移除这些标记
    panoMarkers.forEach((marker) => marker.setMap(null));

    // 从数组中移除这些标记
    markers.value = markers.value.filter((marker) => {
        const data = marker.getExtData ? marker.getExtData() : null;
        return !(data && data.isPano);
    });
};

// 文章标记加载函数（空实现，为未来开发准备）
const loadPageMarkers = async (isInitialLoad = false) => {
    try {
        // 显示加载状态
        isLoading.value = true;
        const pageStore = usePageStore();

        // 只有在初始加载时才清除现有的pin markers
        if (isInitialLoad) {
            clearPageMarkers();
            await pageStore.fetchAllPages();
        }

        // 使用Pinia store加载page标记
        const pageData = pageStore.allPages;

        // 更新列表数据
        infoWindowItems.value = pageData || [];

        // 如果是初始加载，直接添加所有标记
        if (isInitialLoad) {
            pageData.forEach((item) => {
                addPageMarkerToMap(item);
            });
        } else {
            // 增量更新：比较现有标记和新数据
            updatePageMarkers(pageData);

            // 非初始加载时，显示信息窗口
            infoWindowVisible.value = true;
            infoWindowMode.value = 'list';
            infoWindowListType.value = 'page';
        }

        // 显示信息窗口
        infoWindowVisible.value = false;
        infoWindowMode.value = 'list';
        infoWindowListType.value = 'page';

        toast.add({ severity: 'info', summary: '加载完成', detail: `已加载 ${pageData ? pageData.length : 0} 个文章标记`, life: 3000 });
    } catch (error) {
        console.error('加载文章标记失败:', error);
        toast.add({ severity: 'error', summary: '加载失败', detail: '加载文章标记失败，请稍后重试', life: 3000 });
    } finally {
        // 隐藏加载状态
        isLoading.value = false;
    }
};
void loadPageMarkers;

// 清除文章标记函数
const clearPageMarkers = () => {
    // 找出所有文章类型的标记
    const pageMarkers = markers.value.filter((marker) => {
        const data = marker.getExtData ? marker.getExtData() : null;
        // 根据数据结构判断是否为文章标记
        return data && data.page_content; // 假设文章标记有 page_content 属性
    });

    // 从地图上移除这些标记
    pageMarkers.forEach((marker) => marker.setMap(null));

    // 从数组中移除这些标记
    markers.value = markers.value.filter((marker) => {
        const data = marker.getExtData ? marker.getExtData() : null;
        return !(data && data.page_content);
    });
};

// 加载所有类型的标记并显示
const loadAllMarkers = async () => {
    try {
        isLoading.value = true;
        console.log('开始加载所有标记...');

        // 初始化 stores
        const pinStore = usePinStore();
        const photoStore = usePhotoStore();
        const panoStore = usePanoStore();
        const pageStore = usePageStore();

        // 并行加载所有类型的标记数据（通过 Pinia）
        await Promise.all([pinStore.fetchAllPins(), photoStore.fetchAllPhotos(), panoStore.fetchAllPanos(), pageStore.fetchAllPages()]);

        // 存储数据（直接从 store 获取）
        pinMarkersData.value = pinStore.allPins;
        photoMarkersData.value = photoStore.allPhotos;
        panoMarkersData.value = panoStore.allPanos;
        pageMarkersData.value = pageStore.allPages;
        console.log('加载的标记数据:', {
            pin: pinMarkersData.value.length,
            photo: photoMarkersData.value.length,
            pano: panoMarkersData.value.length,
            page: pageMarkersData.value.length,
            pinSample: pinMarkersData.value.length > 0 ? pinMarkersData.value[0] : null,
            photoSample: photoMarkersData.value.length > 0 ? photoMarkersData.value[0] : null,
            panoSample: panoMarkersData.value.length > 0 ? panoMarkersData.value[0] : null,
            pageSample: pageMarkersData.value.length > 0 ? pageMarkersData.value[0] : null
        });

        // ... 后续代码保持不变 ...
    } catch (error) {
        console.error('加载所有标记失败:', error);
        toast.add({
            severity: 'error',
            summary: '加载失败',
            detail: '加载标记失败，请稍后重试',
            life: 3000
        });
    } finally {
        isLoading.value = false;
    }
};

// 定位标记
const handleInfoWindowLocateMarker = (marker) => {
    // 判断标记类型并确定坐标位置
    let position;

    if (marker.gcj) {
        // 照片标记
        position = [marker.gcj.lng, marker.gcj.lat];
    } else if (marker.custom_gcj) {
        // 全景标记
        position = [marker.custom_gcj.lng, marker.custom_gcj.lat];
    } else if (marker.lng && marker.lat) {
        // 引脚标记或其他类型
        position = [marker.lng, marker.lat];
    } else {
        console.warn('无法确定标记位置:', marker);
        return;
    }

    // 定位到标记
    map.setCenter(position);
    map.setZoom(16);

    // 可选：添加高亮效果
    const highlightMarker = new window.AMap.Marker({
        position: position,
        icon: new window.AMap.Icon({
            image: ICON_SVG.PIN_HIGHLIGHT || ICON_SVG.PIN,
            imageSize: new window.AMap.Size(36, 36)
        }),
        anchor: 'center',
        zIndex: 110 // 确保在其他标记之上
    });

    highlightMarker.setMap(map);

    // 2秒后移除高亮
    setTimeout(() => {
        highlightMarker.setMap(null);
    }, 2000);
};

// 在 setup 中添加
const pageStore = usePageStore();

/**
 * 处理全景矩阵按钮切换
 * 显示/隐藏1km网格的全景照片标记
 */
const handlePanoMatrixToggle = async () => {
    try {
        // 切换显示状态
        matrixStore.toggleVisibility();

        if (matrixStore.isVisible) {
            // 显示矩阵标记
            toast.add({
                severity: 'info',
                summary: '正在生成全景矩阵',
                detail: '正在生成武汉三环内1km网格标记...',
                life: 3000
            });

            // 生成并显示矩阵标记
            await generateAndDisplayMatrixMarkers();

            toast.add({
                severity: 'success',
                summary: '全景矩阵已显示',
                detail: `已生成 ${matrixStore.matrixMarkers.length} 个网格标记`,
                life: 3000
            });
        } else {
            // 隐藏矩阵标记
            hideMatrixMarkers();

            toast.add({
                severity: 'info',
                summary: '全景矩阵已隐藏',
                detail: '已隐藏所有网格标记',
                life: 3000
            });
        }
    } catch (error) {
        console.error('全景矩阵切换失败:', error);
        toast.add({
            severity: 'error',
            summary: '操作失败',
            detail: '全景矩阵切换失败，请稍后重试',
            life: 3000
        });
    }
};

/**
 * 生成并显示矩阵标记（带数据库集成、缓存和懒加载）
 */
const generateAndDisplayMatrixMarkers = async () => {
    if (!map || !AMap) {
        console.error('地图未初始化');
        return;
    }

    // 设置生成状态
    matrixStore.setGenerating(true);

    try {
        let gridPoints;

        // 优先级：内存缓存 > 数据库 > 重新生成
        if (matrixStore.matrixMarkers.length > 0) {
            console.log('使用内存缓存的网格数据');
            gridPoints = matrixStore.matrixMarkers;
        } else {
            // 检查数据库中是否已有数据
            const dbDataExists = await matrixStore.checkDbDataExists();

            if (dbDataExists) {
                console.log('从数据库加载网格数据');
                const loadSuccess = await matrixStore.loadMatrixMarkersFromDb();

                if (loadSuccess) {
                    gridPoints = matrixStore.matrixMarkers;

                    toast.add({
                        severity: 'success',
                        summary: '数据加载成功',
                        detail: `从数据库加载了 ${gridPoints.length} 个矩阵标记`,
                        life: 3000
                    });
                } else {
                    throw new Error('从数据库加载数据失败');
                }
            } else {
                console.log('生成新的网格数据并保存到数据库');

                // 生成网格标记数据
                gridPoints = await gridService.generateGridPoints();

                // 存储到store缓存
                matrixStore.setMatrixMarkers(gridPoints);

                // 保存到数据库
                const saveSuccess = await matrixStore.saveMatrixMarkersToDb(gridPoints);

                if (saveSuccess) {
                    toast.add({
                        severity: 'success',
                        summary: '数据保存成功',
                        detail: `已生成并保存 ${gridPoints.length} 个矩阵标记到数据库`,
                        life: 3000
                    });
                } else {
                    // 保存失败但不影响显示
                    toast.add({
                        severity: 'warn',
                        summary: '保存警告',
                        detail: '矩阵数据生成成功，但保存到数据库失败',
                        life: 4000
                    });
                }
            }
        }

        // 懒加载：只显示当前视图范围内的标记
        displayVisibleMarkers(gridPoints);

        // 监听地图移动事件，实现动态懒加载
        setupMapEventListeners();
    } catch (error) {
        console.error('生成矩阵标记失败:', error);
        throw error;
    } finally {
        matrixStore.setGenerating(false);
    }
};

/**
 * 显示可见区域内的标记（懒加载）
 * @param {Array} allMarkers - 所有标记数据
 */
const displayVisibleMarkers = (allMarkers) => {
    if (!map || !AMap) return;

    // 获取当前地图边界
    const bounds = map.getBounds();
    const northeast = bounds.getNorthEast();
    const southwest = bounds.getSouthWest();

    // 获取当前缩放级别
    const zoom = map.getZoom();

    // 根据缩放级别决定是否显示标记
    if (zoom < 10) {
        console.log('缩放级别过低，不显示矩阵标记');
        return;
    }

    // 计算可见区域边界（添加一些缓冲区）
    const buffer = 0.01; // 约1km的缓冲区
    const visibleBounds = {
        north: northeast.lat + buffer,
        south: southwest.lat - buffer,
        east: northeast.lng + buffer,
        west: southwest.lng - buffer
    };

    // 过滤出可见区域内的标记
    const visibleMarkers = allMarkers.filter((marker) => {
        // 安全地获取位置信息，优先使用 position，其次使用 gcj
        const position = marker.position || marker.gcj;
        if (!position || typeof position.lng !== 'number' || typeof position.lat !== 'number') {
            console.warn('标记缺少有效的位置信息:', marker);
            return false;
        }
        const { lng, lat } = position;
        return lng >= visibleBounds.west && lng <= visibleBounds.east && lat >= visibleBounds.south && lat <= visibleBounds.north;
    });

    console.log(`显示 ${visibleMarkers.length} / ${allMarkers.length} 个矩阵标记`);

    // 清除现有的矩阵标记
    clearCurrentMatrixMarkers();

    // 添加可见的标记到地图
    visibleMarkers.forEach((marker) => {
        addMatrixMarkerToMap(marker);
    });

    // 如果是首次显示，显示性能优化提示
    if (visibleMarkers.length < allMarkers.length && allMarkers.length > 100) {
        toast.add({
            severity: 'info',
            summary: '性能优化',
            detail: `为提升性能，仅显示可见区域内的 ${visibleMarkers.length} 个标记（共 ${allMarkers.length} 个）`,
            life: 4000
        });
    }
};

/**
 * 清除当前显示的矩阵标记（不清除缓存）
 */
const clearCurrentMatrixMarkers = () => {
    // 找出所有矩阵标记
    const matrixMarkers = markers.value.filter((marker) => {
        const data = marker.getExtData ? marker.getExtData() : null;
        return data && data.type === 'matrix';
    });

    // 从地图上移除
    matrixMarkers.forEach((marker) => marker.setMap(null));

    // 从标记数组中移除
    markers.value = markers.value.filter((marker) => {
        const data = marker.getExtData ? marker.getExtData() : null;
        return !(data && data.type === 'matrix');
    });
};

// 地图事件监听器引用
let mapEventListeners = [];

/**
 * 设置地图事件监听器（用于懒加载）
 */
const setupMapEventListeners = () => {
    if (!map || !AMap) return;

    // 清除之前的监听器
    clearMapEventListeners();

    // 防抖函数
    let updateTimeout;
    const debouncedUpdate = () => {
        clearTimeout(updateTimeout);
        updateTimeout = setTimeout(() => {
            if (matrixStore.isVisible) {
                displayVisibleMarkers(matrixStore.matrixMarkers);
            }
        }, 300); // 300ms防抖
    };

    // 监听地图移动和缩放事件
    const moveEndListener = map.on('moveend', debouncedUpdate);
    const zoomEndListener = map.on('zoomend', debouncedUpdate);

    // 保存监听器引用
    mapEventListeners.push(moveEndListener, zoomEndListener);
};

/**
 * 清除地图事件监听器
 */
const clearMapEventListeners = () => {
    mapEventListeners.forEach((listener) => {
        if (listener && typeof listener.off === 'function') {
            listener.off();
        }
    });
    mapEventListeners = [];
};

/**
 * 添加矩阵标记到地图
 * @param {Object} matrixMarker - 矩阵标记数据
 */
const addMatrixMarkerToMap = (matrixMarker) => {
    if (!map || !AMap) return;

    // 安全地获取位置信息
    const position = matrixMarker.position || matrixMarker.gcj;
    if (!position || typeof position.lng !== 'number' || typeof position.lat !== 'number') {
        console.warn('矩阵标记缺少有效的位置信息:', matrixMarker);
        return;
    }

    // 根据状态确定颜色方案
    const getStatusColors = (status) => {
        switch (status) {
            case 'planned':
            case 'available':
                return {
                    fill: '#2196F3', // 蓝色 - 计划中/可用
                    stroke: '#1976D2',
                    opacity: '0.85'
                };
            case 'captured':
                return {
                    fill: '#4CAF50', // 绿色 - 已拍摄
                    stroke: '#2E7D32',
                    opacity: '0.9'
                };
            case 'processing':
                return {
                    fill: '#FF9800', // 橙色 - 处理中
                    stroke: '#F57C00',
                    opacity: '0.8'
                };
            case 'unavailable':
                return {
                    fill: '#9E9E9E', // 灰色 - 不可用
                    stroke: '#616161',
                    opacity: '0.6'
                };
            default:
                return {
                    fill: '#2196F3', // 默认蓝色
                    stroke: '#1976D2',
                    opacity: '0.85'
                };
        }
    };

    const colors = getStatusColors(matrixMarker.status);

    // 创建专业的网格标记图标
    const icon = new AMap.Icon({
        image:
            'data:image/svg+xml;base64,' +
            btoa(`<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <circle cx="10" cy="10" r="9" fill="${colors.fill}" stroke="${colors.stroke}" stroke-width="1.5" opacity="${colors.opacity}"/>
                <line x1="10" y1="3" x2="10" y2="17" stroke="#FFFFFF" stroke-width="2" opacity="0.9"/>
                <line x1="3" y1="10" x2="17" y2="10" stroke="#FFFFFF" stroke-width="2" opacity="0.9"/>
                <circle cx="10" cy="10" r="2.5" fill="#FFFFFF" opacity="0.95"/>
                <circle cx="10" cy="10" r="1" fill="${colors.stroke}"/>
                <rect x="5" y="5" width="2" height="2" fill="#FFFFFF" opacity="0.7"/>
                <rect x="13" y="5" width="2" height="2" fill="#FFFFFF" opacity="0.7"/>
                <rect x="5" y="13" width="2" height="2" fill="#FFFFFF" opacity="0.7"/>
                <rect x="13" y="13" width="2" height="2" fill="#FFFFFF" opacity="0.7"/>
            </svg>`),
        size: new AMap.Size(20, 20),
        imageSize: new AMap.Size(20, 20)
    });

    // 创建地图标记
    const marker = new AMap.Marker({
        position: [position.lng, position.lat],
        icon: icon,
        anchor: 'center',
        zIndex: 100,
        extData: {
            type: 'matrix',
            id: matrixMarker.id,
            gridIndex: matrixMarker.gridIndex,
            status: matrixMarker.status
        }
    });

    // 添加悬停事件
    marker.on('mouseover', () => {
        handleMatrixMarkerHover(matrixMarker);
    });

    marker.on('mouseout', () => {
        handleMatrixMarkerHoverOut();
    });

    // 添加点击事件
    marker.on('click', () => {
        handleMatrixMarkerClick(matrixMarker);
    });

    // 添加到地图
    marker.setMap(map);

    // 添加到标记数组
    markers.value.push(marker);
};

/**
 * 隐藏矩阵标记
 */
const hideMatrixMarkers = () => {
    // 清理悬停信息窗口
    if (hoverInfoWindow) {
        hoverInfoWindow.close();
        hoverInfoWindow = null;
    }

    // 清理地图事件监听器
    clearMapEventListeners();

    // 找出所有矩阵标记
    const matrixMarkers = markers.value.filter((marker) => {
        const data = marker.getExtData ? marker.getExtData() : null;
        return data && data.type === 'matrix';
    });

    // 从地图上移除
    matrixMarkers.forEach((marker) => marker.setMap(null));

    // 从标记数组中移除
    markers.value = markers.value.filter((marker) => {
        const data = marker.getExtData ? marker.getExtData() : null;
        return !(data && data.type === 'matrix');
    });
};

// 悬停信息窗口引用
let hoverInfoWindow = null;

/**
 * 处理矩阵标记悬停事件
 * @param {Object} matrixMarker - 矩阵标记数据
 */
const handleMatrixMarkerHover = (matrixMarker) => {
    if (!AMap || !map) return;

    // 安全地获取位置信息
    const position = matrixMarker.position || matrixMarker.gcj;
    if (!position) return;

    // 创建悬停信息窗口
    const content = `
        <div style="padding: 8px; font-size: 12px; line-height: 1.4; min-width: 180px;">
            <div style="font-weight: bold; color: #1976D2; margin-bottom: 4px;">
                网格点 ${matrixMarker.id}
            </div>
            <div style="color: #666;">
                坐标: ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}
            </div>
            <div style="color: #666;">
                网格: (${matrixMarker.gridIndex.x}, ${matrixMarker.gridIndex.y})
            </div>
            <div style="color: #666; margin-top: 4px;">
                状态: <span style="color: ${getStatusColor(matrixMarker.status)}; font-weight: bold;">${getStatusText(matrixMarker.status)}</span>
            </div>
        </div>
    `;

    // 移除之前的悬停窗口
    if (hoverInfoWindow) {
        hoverInfoWindow.close();
    }

    // 创建新的信息窗口
    hoverInfoWindow = new AMap.InfoWindow({
        content: content,
        offset: new AMap.Pixel(0, -30),
        closeWhenClickMap: false,
        autoMove: true
    });

    // 在标记位置显示
    hoverInfoWindow.open(map, [position.lng, position.lat]);
};

/**
 * 处理矩阵标记悬停离开事件
 */
const handleMatrixMarkerHoverOut = () => {
    if (hoverInfoWindow) {
        hoverInfoWindow.close();
        hoverInfoWindow = null;
    }
};

/**
 * 获取状态对应的颜色
 * @param {string} status - 状态
 * @returns {string} 颜色值
 */
const getStatusColor = (status) => {
    switch (status) {
        case 'planned':
        case 'available':
            return '#2196F3';
        case 'captured':
            return '#4CAF50';
        case 'processing':
            return '#FF9800';
        case 'unavailable':
            return '#9E9E9E';
        default:
            return '#2196F3';
    }
};

/**
 * 获取状态对应的文本
 * @param {string} status - 状态
 * @returns {string} 状态文本
 */
const getStatusText = (status) => {
    switch (status) {
        case 'planned':
            return '计划中';
        case 'available':
            return '可用';
        case 'captured':
            return '已拍摄';
        case 'processing':
            return '处理中';
        case 'unavailable':
            return '不可用';
        default:
            return '未知';
    }
};

/**
 * 处理矩阵标记点击事件
 * @param {Object} matrixMarker - 矩阵标记数据
 */
const handleMatrixMarkerClick = async (matrixMarker) => {
    console.log('矩阵标记被点击:', matrixMarker);

    // 安全地获取位置信息
    const position = matrixMarker.position || matrixMarker.gcj;
    if (!position) {
        console.warn('矩阵标记缺少位置信息:', matrixMarker);
        return;
    }

    // 根据状态确定Toast的严重程度和消息
    const getStatusInfo = (status) => {
        switch (status) {
            case 'available':
                return {
                    severity: 'info',
                    summary: '可用网格点',
                    statusText: '可拍摄全景照片',
                    nextStatus: 'captured',
                    nextStatusText: '标记为已拍摄'
                };
            case 'captured':
                return {
                    severity: 'success',
                    summary: '已拍摄网格点',
                    statusText: '已有全景照片',
                    nextStatus: 'processing',
                    nextStatusText: '标记为处理中'
                };
            case 'processing':
                return {
                    severity: 'warn',
                    summary: '处理中网格点',
                    statusText: '照片处理中',
                    nextStatus: 'available',
                    nextStatusText: '重置为可用'
                };
            case 'unavailable':
                return {
                    severity: 'error',
                    summary: '不可用网格点',
                    statusText: '无法拍摄',
                    nextStatus: 'available',
                    nextStatusText: '标记为可用'
                };
            default:
                return {
                    severity: 'info',
                    summary: '网格点',
                    statusText: '状态未知',
                    nextStatus: 'available',
                    nextStatusText: '标记为可用'
                };
        }
    };

    const statusInfo = getStatusInfo(matrixMarker.status);

    // 显示详细的标记信息和操作选项
    toast.add({
        severity: statusInfo.severity,
        summary: statusInfo.summary,
        detail: `ID: ${matrixMarker.id}\n网格坐标: (${matrixMarker.gridIndex.x}, ${matrixMarker.gridIndex.y})\n地理位置: ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}\n状态: ${statusInfo.statusText}\n\n点击此消息可${statusInfo.nextStatusText}`,
        life: 8000,
        closable: true,
        // 添加点击事件来更新状态
        onClick: async () => {
            try {
                // 更新数据库中的状态
                const updateSuccess = await matrixStore.updateMarkerStatusInDb(matrixMarker.id, statusInfo.nextStatus);

                if (updateSuccess) {
                    // 重新显示可见标记以反映状态变化
                    displayVisibleMarkers(matrixStore.matrixMarkers);

                    toast.add({
                        severity: 'success',
                        summary: '状态更新成功',
                        detail: `网格点 ${matrixMarker.id} 已${statusInfo.nextStatusText}`,
                        life: 3000
                    });
                } else {
                    toast.add({
                        severity: 'error',
                        summary: '状态更新失败',
                        detail: '无法更新网格点状态，请稍后重试',
                        life: 3000
                    });
                }
            } catch (error) {
                console.error('更新矩阵标记状态失败:', error);
                toast.add({
                    severity: 'error',
                    summary: '操作失败',
                    detail: '状态更新时发生错误',
                    life: 3000
                });
            }
        }
    });

    // 可以在这里添加更多交互逻辑，比如：
    // - 显示详细信息窗口
    // - 上传全景照片
    // - 查看附近的全景照片等
};

// 处理页面内容变化
// const handlePageContentChange = (content) => {
//     if (currentPageMarker.value) {
//         currentPageMarker.value.page_content = content;
//     }
// };

watch(searchText, (newVal) => {
    if (!newVal) {
        // 清空 id=panel 的内容
        const panelDiv = document.getElementById('panel');
        if (panelDiv) {
            panelDiv.innerHTML = '';
        }
    }
});
</script>

<style lang="scss" scoped>
.map {
    height: calc(100vh - 7vh);
    background-color: beige;
    position: relative;
    overflow: hidden;
}

/* 添加自定义模糊效果 */
:deep(.custom-blur) {
    backdrop-filter: blur(1.5px);
}

/* 移除之前的 Dock 相关样式 */
// ... existing code ...

#container {
    height: 100%;
    width: 100%;
    position: relative;
    overflow: hidden;

    :deep(.amap-layers) {
        z-index: 0 !important;
    }

    .map-overlay {
        position: relative;
        z-index: 1021;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
}

/* 自定义 pin-cursor 样式 */
.pin-cursor {
    cursor:
        url('data:image/svg+xml;charset=utf-8;base64,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')
            7 7,
        auto !important;
}

/* 加载状态指示器样式 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.loading-spinner {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.loading-text {
    margin-top: 10px;
    color: #333;
    font-size: 1rem;
}

.photo-cursor {
    cursor:
        url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAAC40lEQVR4nO2V60tTYRzHT/SuVwXRy173TyS7ZOlmTQknZuKlmU4nLi/bTNdMs9Cltqlga2oapjUvY7pLGuY1EySNSsEXNS8nXW5Hd4yiJL9xDricU1jam6AHPvDjeX7n+3ngPM85BPF/HHTQNH2coqhOiqLm3G73/BaLi4vWqampE38URpLkEZqmNSsrK50ul8tMkqTW6/W2jr96i7QUHVIl91iYmpmbmJiwEgRxKNiditbW1l7PTH/AQ6OVhalpmoYsVY+4BCPikxtZmDozTQ+Kor6RJDnqcrkGt1heXrYsLCxc9AtfX1/netzUZlODDYkxGgzW57AwdXVVO2LFpUhIeeTHJfFtdm0nzY12MFkmk0nsE3i93jsO2xikUiPUtxwo1w2zqEvsKLhph7r0OYrKBv1g5pi1nTAZz2xj6Ovr6/AJPB6PzmIeRlrmY8gLBwKQZvcgUlSOsDMFLFGR5ZBmW3ftZTKYrP7+fub9HPYTZGS1QVn80o8r0laEchTg81S4kHSfhc9VIpSjhEQa2J+R9QTmrj0EqgIHDM2ffFTWTCOMp8L5+Fpcs31B/ZsNaEZ/Qtazjoi4GoTx81FVO+P3TL66D6anQ8EJkhIN4HEUkNtoTJJf0fLuB1RDm5C/AGTdNPicPCQnGfcvEAmL2d3XTW6g7f13Nng7EZdrIBIU718QKSyBME6PwpFN3863I4zVsT37FkgkDeCF5CHDTAWEp3d5wAvJxVVJ0/4FurpZhPOvQxBdgfROz+/wDjcE0XfZA6Cvmw1ekKu0Qm/46EdB0QB7WrincyAUV0IgrmDrc1wV1JqhgH5Fvh3tpj0Ee100mcIBcYwe4WdvsIhjqiFTPgv+ornd7ooeywgkqY3IVPUeCElqI6yWUfT29nb7BKurq1FLS5+RIzdAmqI/ENnyB2CytFptmd+nfHx8vNLpdM6TJOk6CE6nc85kMrUQBHFqt9/CMYIgTv4Fjgb1E/onxi9ZWB4vQxIuTgAAAABJRU5ErkJggg==')
            16 16,
        auto !important;
}
// 24 x 24 pixels
.pano-cursor {
    cursor:
        url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAADj0lEQVR4nMVVS09TQRQeNBjSRGWh0YXxtSMxxqWU+Ack/AOibFQeIoZWkFRaE7cmaAVDMYJKSjQ+FgoGBAwqFCwoBRUoIJQWgvS2M7c3CMTNMd9QzL20+NjIJCe5mfnO435nzjeMbdYSQqQT0dY/4YDhnO/8q6CRSGSHpmk3VVUVmqaRqqrLwWDwud/vP5ykgINCiEfAAMs5V2dmZuoURdm+YXAhxPDY6DRV2uqpuKiaLpXWUk/3EJyXI5FIUG+qqn7veTckMcDCB76hUGjS5XLtSkigaVrV2MgUFZ5zkmc0Sn5O5AstUfnle9Te5iVUqbf2Vq88AwbYnpEIFZy7KZP09fU9Wc9jiqqqHFUguMe/SNWNX6j1vUKfF35QUb4zIUFRgVOeAQNs7/iiTGK31VM4HNZMJtPe9dxTSXGNrAYOF690UanjjXS0lNaREo7+Co5v7OEMGGDhA98L56slxmw2ZxpuAppVZnHRYGhJVrXm6Lj+nvLP3iD3vWYa9wekue83yz2crRUCH/giBud8xWQyHTPQhNuChpaXN9Cnbz9kdQjguPaCQs9OUZ3DSnl5tykvr0Z+Yw9nwAALn7LL9eTpGaaBgYFuxtg+QwJcRSHEMhoKzkEBqkSgD24rWeyvZbUwfGMPZwVnqiQWPh2v+mX1OTk5uYyxlISbxDmf1fMMKmrtlwzB9UlclVZyNzQb+jM/P68wxg4lnQV9AtjE+IykZX3wNcMZeqL3iSc4uPkJFElRC91xlJLV3pmcIvuGFCUm4JwfUFV1CROKIdI3+aPbIgOWOIelGZt8Y7XJBU458WhyRUXFiYQEQoiH0BaMPyZUf01nn56W1ebm35WGxmNPf03hgyuOq+7z+d4wxrYYBi0Wiy1hSHwbDFrTgxaanAjS18kgNTW+/O2gCSFW0tPTD/yTVESUJFIxsUiWq2+TSkVmZqZZ/wcp0WhUQKggWAha3ThCrV5FTmhx4a2Nxc4LsRtJEDvG2BFDDwKBQC2kFnINIKoZDH6X49/Z3p8o121eyTloAbb7iyJ9/WMB6ujoaGaM7TYkwEuExwIAVIFfLbO6yOMZlg/O3Nzcgt6wB90B58DCBzMxNTU1nZGRcTLpHOAl6u3tfYxfjD+DKxCu7Ozs04wxNG3/mtlsNrPP5+sCBthwOBxD5fHg25Im0K09WVlZx1NTU4/GVTFRuFbXlrS0tP1x7QfnRlr+x/oJz9fLHujXVTUAAAAASUVORK5CYII=')
            12 12,
        auto !important;
}

// .page-cursor {
//     cursor: var(--page-cursor);
// }
.page-cursor {
    cursor:
        url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAAC/UlEQVR4nN2Va0hTYRjHX7sQBH4qiIjW8KP2QQgv6NKZFrjwUooaoUg0Dc3ph1KTLuQqMimw1EARNSNCSWdKWcYyNdRUsrxsRw1buZob7j07EhZG/zivOko38/apB37wPu/Z8/z2bOe8h5D/KgBsopRKeZ53Ww2UUqnYw2FzQRAibTabIAgC1gKldKq3tzd+4Td34XneejT2LHwC0+AdqFodASpExZ6B2WwWXF1dt9kFNpvNR7T7ylUIK+3GhaYPDsnR6BjOrou1Yg+xl0KhCLALeJ7/eFV9G76yNGQ2DIOzYhH6SUCj1TPEtaPPiLW+8nQmCA0NPcCaG43GreKGv18CfJMicam2zmExZwXaBy0MZ9eXFMhk8ZBnH8TlR/edTlD3YoixnAlaW1vvrkjALWOC3NpByPafRvghJaxW67RUKt0j3kEbKaVCVEQqvAJSkdUw5HSCxy3DjD8nGDBN29evC5uhD8rGYZ8TbAp3d3dvNkV/f3/m7F2U7vRP5qxAc/dnhn3vC0VpfjGGzd+XFgiCsP1fAv3kLzzrMjDEtbhnGazBt+7rmOCaWP68ph71ynCE+iUygYeHh9cKBEBj2yhj/iea7L6FH00RML+vYvnbxlLQa54ICzi+8gk4K/Cy7yta3pns+boK9OxB4/560BYKihracUSRiBD/ePFM+imRSPYtEiRVD+AJJzik+o2RMZ8b2vKYgOssY7lY6yNPh8k0gY6OjleEEOn8YbeF5/kZmTwFnjF58Iy7sSzKClRMkHslc3YvJg9iD0rpjEQiCSGEuNjPo9HR0cqxMQOKi+7hTkH5P4mLzsC5hAw03ixBckQykk+eZ7UGwydotdqnhJDdC18JLpWVlac0Gk25RqOpWoqRkRFdTnY+1IpE6FRZSAmKR2nJA/T19fWo1eqLhJC9ZC1hsVjU1Q8bEB2sxLFgJaKClWhv60JFRUUhWY+YmpraodPpOo1G44TI+Pi4qaenp93NzS14XQRzsZkQsosQIpljJyFkw3oKlhW/AUDS4PWtXJtRAAAAAElFTkSuQmCC')
            12 12,
        auto !important;
}

.custom-marker {
    width: 24px;
    height: 24px;
    border: 0px solid red;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAADh0lEQVR4nO1SbWxTVRhuYjCK0RglBom/NNHEH0CEnXMtE1C5t2IsWzLKZpxbz7mlW2vsems3amUYRsYQAScEIpSPoGSMmZBp5JsMsN0tvfdcV/qZThgZM2PJxG5+EBMSrnnvRKrbItl+6ps8ycn7Pu/znPOe12T6z4SZRh5eSJSnAXC+50bOJj9o9SX4VWuSZaXB3ubS+nQtnAUpOa+Qt27P1URn5MYvgMY9Vy8V1nh/z3zosa5Ju0rXZpqNsy/BL6k+94CpxJ+aL27MdZavTcklwczNN/zJUeem3mMVjZn9nJh6jHMq1sWuWJL3aPnX6npGALyH5Re7Y0mzGHm96J3M4xWN6QPQs1xKjCz3J26CFmhaPYm5d0fgVD4UJFm3SPH8ho784B2Ur/9+CPIT4c31vUOFXIsvnoc8aI0b1SKH4uW90Bgf3tAx8lPowi39UEzXndv6DYOV9WH9s80nDZTVRwyDmm39Q8AJnb+lQ4/gjQ+DBmiNM1jgZDMXOWI7zY7YajPV5qxoyJLKpivbVwTSWRCz1Yf1L7YeN2BrGDMoCaSzBqchS6CnuIatBg3QuuclKK5V9k42ouIaJWSaTsAWWOuUsFVSf54Qdeq3xqZMNd4OKOVHPtdGv27Xfp8I7Qe/GwXOtF7B2S+98MmRH35t6xrWg6FrBg53/ahDDmqm6YaZanOOhm/kg3sH9LLWHv3jb/r14L4BHXJQm7aBzdZx3+6vrl92t/bp9q6sAfenffquzsHLUJuy8AInm2GuYs8gO3vFsTF3uqVt8DbZmtPplpz+0eHrt8WW3CksqvxCqjz3vC11/78Kzq2MP4SJRhBV2zFRZUTZGUzV3cjO3seEvVfVlDnp33FlwL+971pVU+YEJ2oSJiyAKNuFCDuNCItgqh4tomrtvOqeR/8mzokqQpR1Y8JcyHHxqX+aW7zyMsEX/UDwRV28FK0QJLnVUtftEaTIy4W8FyvjT4xdkoXhdX8VEGUt2K5Num7LpDDHS/I+Xuo+JHijmwVJXid4uwOT8bGoLkVU3X/X4K2LjyDKQpiwUzAOIBRVx2bfqb/qiz37kjsaWOpSyg0zr2y1eC88WfBfszBhxRxV30WUHcOUtcFrxjnD7BBlpZiozfAXxj8Q9ezY+FQZE/YbtqvnIf9nDmpnMGVfIsI2cYStAjPTVAKLMQ+i2pYl7tRs2LApifwfpgniD0XCN2tjFQabAAAAAElFTkSuQmCC')
        no-repeat center;
    background-size: contain;
}
#panel {
    position: relative;
    z-index: 1010;
    background-color: white;
    max-height: calc(90vh - 60px);
    min-height: 0px;
    width: 280px;
    overflow-y: auto;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    align-self: flex-end;
    margin-right: 10px;
    pointer-events: none;
}

#panel > * {
    pointer-events: auto;
}

.map-uploader-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1003;
}

.map-uploader {
    width: 80%;
    height: 60%;
    background: white;
    border-radius: 8px;
    padding: 20px;
}

.panorama-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    max-width: 1000px;
    max-height: 80vh;
    z-index: 2000;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.cubemap-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #f0f0f0;
    border-bottom: 1px solid #ddd;
    flex-shrink: 0;

    span {
        font-weight: bold;
        color: #333;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        line-height: 1;
        cursor: pointer;
        color: #666;
        padding: 0 8px;

        &:hover {
            color: #ff0000;
        }
    }
}

.panorama-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #f0f0f0;
    border-bottom: 1px solid #ddd;
    flex-shrink: 0;

    span {
        font-weight: bold;
        color: #333;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        line-height: 1;
        cursor: pointer;
        color: #666;
        padding: 0 8px;

        &:hover {
            color: #ff0000;
        }
    }
}

/* 添加媒体查询以适应移动设备 */
@media (max-width: 768px) {
    .panorama-container {
        width: 95%;
        max-height: 90vh;
    }
}

.fade-enter-active,
.fade-leave-active {
    transition:
        opacity 0.3s ease,
        transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
    transform: translate(-50%, -45%);
}
.zoom-display {
    display: flex;
    align-items: center;
    margin-right: 4px; /* 右侧留点间距，避免贴太近 */
    font-size: 15px;
    color: #333;
    min-width: 120px; /* 保证宽度一致 */
    height: 38px; /* 与输入框高度一致或略小 */
}

/* 懒加载进度指示器样式 */
.lazy-loading-indicator {
    position: absolute;
    bottom: 100px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 16px;
    z-index: 1500;
    min-width: 250px;
}

.progress-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.progress-text {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    text-align: center;
}

.progress-type {
    font-size: 12px;
    color: #6b7280;
    text-align: center;
}

/* SpeedDial 样式 */
.speed-dial-container {
    position: absolute;
    z-index: 1500;
    cursor: move;
}

.speed-dial-wrapper {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    gap: 10px;
}

.speed-dial-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.speed-dial-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.speed-dial-items {
    display: flex;
    flex-direction: row-reverse;
    gap: 8px;
    align-items: center;
}

.speed-dial-item {
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    min-height: 60px;
    opacity: 0.9;
}

.speed-dial-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: #f8f9fa;
    opacity: 1;
}

.speed-dial-item.active {
    background: #e3f2fd;
    border-color: #2196f3;
    color: #1976d2;
}

.speed-dial-item.active:hover {
    background: #bbdefb;
}
</style>

<style>
/* 禁用body滚动条 */
body {
    overflow: hidden !important;
}
</style>
