<template>
    <div class="map">
        <PageEditor :visible="pageEditorVisible" v-model="pageContent" @select-item="handleInfoWindowSelectItem" @close="closePageEditor" />
        <Toast />
        <div id="my-container" @click.stop>
            <!-- menu 菜单 -->
            <div class="map-overlay" style="position: relative; z-index: 1021">
                <Menubar :model="nestedMenuitems" position="top">
                    <template #end>
                        <div style="display: flex; align-items: center">
                            <div class="zoom-display">
                                <span> ZOOM: {{ mapStore.mapOptions.zoom }}</span>
                            </div>
                            <IconField iconPosition="left">
                                <InputIcon class="pi pi-search" />
                                <InputText type="text" placeholder="搜索地点" v-model="searchText" @keyup.enter="handleSearch" id="search-input" />
                            </IconField>
                        </div>
                    </template>
                </Menubar>

                <!-- 行政区域工具面板 -->
                <Transition name="slide-down">
                    <div v-if="showDistrictTool" class="district-tool-panel">
                        <div class="district-tool-content">
                            <div class="district-section">
                                <label>城市选择:</label>
                                <Dropdown v-model="selectedCity" :options="cityOptions" optionLabel="name" optionValue="code" placeholder="选择城市" @change="onCityChange" />
                            </div>
                            <div class="district-section">
                                <label>行政区:</label>
                                <Dropdown v-model="selectedDistrict" :options="districtOptions" optionLabel="name" optionValue="code" placeholder="选择行政区" @change="onDistrictChange" />
                            </div>
                            <div class="district-actions">
                                <Button label="清除边界" icon="pi pi-times" size="small" severity="secondary" @click="clearDistrictBoundaries" />
                            </div>
                        </div>
                    </div>
                </Transition>
            </div>

            <!-- 控制面板 -->
            <div id="panel"></div>

            <!-- docker -->
            <Dock
                :model="dockItems"
                position="bottom"
                :pt="{
                    root: { class: 'mb-5' },
                    icon: { class: 'drop-shadow-md' },
                    listContainer: { class: '!bg-blue-100/30 custom-blur !rounded-full !p-3 shadow-sm' }
                }"
            >
                <template #itemicon="{ item }">
                    <img v-tooltip.top="item.label" :alt="item.label" :src="item.icon" style="width: 100%" @click="onDockItemClick($event, item)" />
                </template>
            </Dock>

            <!-- 批量上传照片对话框 -->
            <Dialog v-model:visible="showBatchUploadDialog" header="批量上传" :closable="false" :dismissableMask="false">
                <Tabs v-model:value="batchTabsStatus">
                    <TabList>
                        <Tab value="photo">照片</Tab>
                        <Tab value="pano">全景图</Tab>
                    </TabList>
                    <TabPanel value="photo">
                        <BatchPhotoUpload @saveBatch="savePhotoMarkers" @unlockSubmit="unlockSubmitPhotos" />
                    </TabPanel>
                    <TabPanel value="pano">
                        <BatchPanoUpload @saveBatch="savePanoMarkers" @unlockSubmit="unlockSubmitPanos" />
                    </TabPanel>
                </Tabs>
                <template #footer>
                    <Button label="确认上传" icon="pi pi-check" class="p-button-success" @click="handleBatchFormConfirm" :disabled="!batchUploaded || uploadingFile" :loading="uploadingFile" />
                    <Button label="取消" icon="pi pi-times" class="p-button-secondary" @click="cancelUploadFile" :disabled="uploadingFile" />
                </template>
            </Dialog>

            <!-- 改进的加载状态指示器 -->
            <div v-if="isInitialLoading" class="loading-overlay">
                <div class="loading-spinner">
                    <i class="pi pi-spin pi-spinner" style="font-size: 3rem"></i>
                    <div class="loading-text">正在初始化地图...</div>
                </div>
            </div>

            <!-- 懒加载进度指示器 -->
            <div v-if="lazyLoadingProgress.show" class="lazy-loading-indicator">
                <div class="progress-content">
                    <div class="progress-text">
                        {{ lazyLoadingProgress.text }}
                    </div>
                    <ProgressBar :value="lazyLoadingProgress.percentage" :showValue="true" style="width: 200px" />
                </div>
            </div>
        </div>
    </div>

    <Dialog v-model:visible="showImageDialog" modal header="图片预览" :style="{ width: '25vw' }">
        <img :src="currentImage" style="width: 100%" />
    </Dialog>

    <!-- 全景组件使用绝对定位显示在地图上方 -->
    <Transition name="fade">
        <div class="panorama-container" v-if="showPanoCube">
            <div class="cubemap-header">
                <span>全景立方体视图</span>
                <button class="close-btn" @click="showPanoCube = false">×</button>
            </div>
            <PanoCube />
        </div>
    </Transition>
    <Transition name="fade">
        <div class="panorama-container" v-if="isPanoEquirect">
            <div class="panorama-header">
                <span>全景等距矩形视图</span>
                <button class="close-btn" @click="isPanoEquirect = false">×</button>
            </div>
            <PanoEquirect />
        </div>
    </Transition>

    <!-- 信息窗口组件 -->
    <InfoWindow
        :key="infoWindowKey"
        :visible="infoWindowVisible"
        :mode="infoWindowMode"
        :items="infoWindowItems"
        :listType="infoWindowListType"
        :position="infoWindowPosition"
        :formData="infoWindowFormData"
        @close="closeInfoWindow"
        @submit="handleInfoWindowSubmit"
        @edit="handleInfoWindowEdit"
        @delete="handleInfoWindowDelete"
        @batch-delete="handleBatchDelete"
        @item-click="handleInfoWindowItemClick"
    />
</template>

<script setup>
// 导入所有必要的组件和工具
import PageEditor from '@/components/map/page_info/PageEditor.vue';
import PanoCube from '@/views/uikit/PanoCube.vue';
import PanoEquirect from '@/views/uikit/PanoEquirect.vue';
import InfoWindow from '@/components/map/InfoWindow.vue';
import BatchPhotoUpload from '@/components/map/BatchPhotoUpload.vue';
import BatchPanoUpload from '@/components/map/BatchPanoUpload.vue';
import Toast from 'primevue/toast';
import Menubar from 'primevue/menubar';
import Dock from 'primevue/dock';
import Dialog from 'primevue/dialog';
import Tabs from 'primevue/tabs';
import TabList from 'primevue/tablist';
import Tab from 'primevue/tab';
import TabPanel from 'primevue/tabpanel';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import Dropdown from 'primevue/dropdown';
import ProgressBar from 'primevue/progressbar';

import { onMounted, onUnmounted, ref, reactive, nextTick, watch } from 'vue';
import { ICON_SVG } from '@/config/svg.js';
import { ICON_IMG } from '@/config/img.js';
import { useToast } from 'primevue/usetoast';
import { useMapStore } from '@/stores/mapStore';
import { usePinStore } from '@/stores/pinStore';
import { usePhotoStore } from '@/stores/photoStore';
import { usePanoStore } from '@/stores/panoStore';
import { usePageStore } from '@/stores/pageStore';
import { useMatrixStore } from '@/stores/matrixStore';
import { useInfoWindowStore } from '@/stores/useInfoWindowStore';
import mapService from '@/services/mapService';

// 响应式数据
const toast = useToast();
const mapStore = useMapStore();
const pinStore = usePinStore();
const photoStore = usePhotoStore();
const panoStore = usePanoStore();
const pageStore = usePageStore();
const matrixStore = useMatrixStore();
const infoWindowStore = useInfoWindowStore();

// 地图相关
let map = null;
let AMap = null;
let hoverInfoWindow = null;

// 状态变量
const isInitialLoading = ref(false);
const searchText = ref('');
const markers = ref([]);
const tempMarker = ref(null);

// 懒加载进度
const lazyLoadingProgress = ref({
    show: false,
    text: '',
    percentage: 0
});

// 行政区域工具相关
const showDistrictTool = ref(false);
const selectedCity = ref('wuhan');
const selectedDistrict = ref(null);
const districtBoundaries = ref([]);

// 城市和行政区选项
const cityOptions = ref([
    { name: '武汉市', code: 'wuhan' },
    { name: '长沙市', code: 'changsha' }
]);

const districtOptions = ref([]);

// 武汉市行政区数据
const wuhanDistricts = [
    { name: '江岸区', code: 'jiangan', adcode: '420102' },
    { name: '江汉区', code: 'jianghan', adcode: '420103' },
    { name: '硚口区', code: 'qiaokou', adcode: '420104' },
    { name: '汉阳区', code: 'hanyang', adcode: '420105' },
    { name: '武昌区', code: 'wuchang', adcode: '420106' },
    { name: '青山区', code: 'qingshan', adcode: '420107' },
    { name: '洪山区', code: 'hongshan', adcode: '420111' },
    { name: '东西湖区', code: 'dongxihu', adcode: '420112' },
    { name: '汉南区', code: 'hannan', adcode: '420113' },
    { name: '蔡甸区', code: 'caidian', adcode: '420114' },
    { name: '江夏区', code: 'jiangxia', adcode: '420115' },
    { name: '黄陂区', code: 'huangpi', adcode: '420116' },
    { name: '新洲区', code: 'xinzhou', adcode: '420117' }
];

// 长沙市行政区数据
const changshaDistricts = [
    { name: '芙蓉区', code: 'furong', adcode: '430102' },
    { name: '天心区', code: 'tianxin', adcode: '430103' },
    { name: '岳麓区', code: 'yuelu', adcode: '430104' },
    { name: '开福区', code: 'kaifu', adcode: '430105' },
    { name: '雨花区', code: 'yuhua', adcode: '430111' },
    { name: '望城区', code: 'wangcheng', adcode: '430112' }
];

// 数据缓存管理
const dataCache = ref({
    pins: { data: [], timestamp: null, version: 0 },
    photos: { data: [], timestamp: null, version: 0 },
    panos: { data: [], timestamp: null, version: 0 },
    pages: { data: [], timestamp: null, version: 0 }
});

// 缓存有效期（5分钟）
const CACHE_DURATION = 5 * 60 * 1000;

// 菜单配置
const nestedMenuitems = ref([
    {
        label: '工具',
        icon: 'pi pi-fw pi-wrench',
        items: [
            {
                label: '社群',
                icon: 'pi pi-fw pi-discord',
                items: [
                    {
                        label: '新增社群',
                        icon: 'pi pi-fw pi-user-plus'
                    },
                    { label: '编辑社群', icon: 'pi pi-fw pi-user-edit' }
                ]
            },
            {
                label: '行政区域',
                icon: 'pi pi-fw pi-th-large',
                command: () => {
                    toggleDistrictTool();
                }
            }
        ]
    },
    {
        label: '资料',
        icon: 'pi pi-fw pi-file',
        items: [
            {
                label: '新文档',
                icon: 'pi pi-fw pi-file-edit',
                command: () => {
                    pageEditorVisible.value = !pageEditorVisible.value;
                }
            },
            { label: '新图集', icon: 'pi pi-fw pi-images' }
        ]
    },
    {
        label: '标记📌',
        icon: 'pi pi-fw pi-map-marker',
        items: [
            {
                label: '文章列表',
                icon: 'pi pi-fw pi-file-word',
                command: () => {
                    console.log('PageList clicked');
                }
            },
            {
                label: '标记列表',
                icon: 'pi pi-fw pi-thumbtack',
                command: () => {
                    loadPinMarkersLazy();
                }
            },
            {
                label: '全景列表',
                icon: 'pi pi-fw pi-telegram',
                command: () => {
                    loadPanoMarkersLazy();
                }
            },
            {
                label: '照片列表',
                icon: 'pi pi-fw pi-images',
                command: () => {
                    loadPhotoMarkersLazy();
                }
            },
            {
                label: '全景矩阵',
                icon: 'pi pi-fw pi-send',
                command: () => {
                    matrixStore.generateMatrix();
                }
            },
            {
                label: '批量上传',
                icon: 'pi pi-fw pi-upload',
                command: () => {
                    showBatchUploadDialog.value = true;
                }
            }
        ]
    }
]);

// Dock配置
const dockItems = ref([
    {
        label: '标记',
        icon: ICON_IMG.CAMERA,
        command: () => {
            enableLocationPicker('pin');
        }
    },
    {
        label: '照片',
        icon: ICON_IMG.HOUSE,
        command: () => {
            enableLocationPicker('photo');
        }
    },
    {
        label: '全景',
        icon: ICON_IMG.PANO,
        command: () => {
            enableLocationPicker('pano');
        }
    }
]);

// 信息窗口相关
const infoWindowVisible = ref(false);
const infoWindowMode = ref('form');
const infoWindowItems = ref([]);
const infoWindowListType = ref('pin');
const infoWindowPosition = ref({ x: 0, y: 0 });
const infoWindowFormData = ref({});
const infoWindowKey = ref(0);

// 其他状态
const pageEditorVisible = ref(false);
const pageContent = ref('');
const showBatchUploadDialog = ref(false);
const batchTabsStatus = ref('photo');
const batchUploaded = ref(false);
const uploadingFile = ref(false);
const batchPhotosFormData = ref([]);
const batchPanoFormData = ref([]);
const showImageDialog = ref(false);
const currentImage = ref('');
const showPanoCube = ref(false);
const isPanoEquirect = ref(false);

// ==================== 核心功能函数 ====================

// 行政区域工具切换
const toggleDistrictTool = () => {
    showDistrictTool.value = !showDistrictTool.value;
    if (showDistrictTool.value) {
        // 初始化行政区选项
        onCityChange();
    }
};

// 城市变化处理
const onCityChange = () => {
    selectedDistrict.value = null;
    if (selectedCity.value === 'wuhan') {
        districtOptions.value = wuhanDistricts;
        loadCityBoundary('420100'); // 武汉市adcode
    } else if (selectedCity.value === 'changsha') {
        districtOptions.value = changshaDistricts;
        loadCityBoundary('430100'); // 长沙市adcode
    }
};

// 行政区变化处理
const onDistrictChange = () => {
    if (selectedDistrict.value) {
        const district = districtOptions.value.find((d) => d.code === selectedDistrict.value);
        if (district) {
            loadDistrictBoundary(district.adcode);
        }
    } else {
        onCityChange(); // 重新加载城市边界
    }
};

// 加载城市边界
const loadCityBoundary = async (adcode) => {
    try {
        clearDistrictBoundaries();

        if (!AMap || !map) return;

        AMap.plugin('AMap.DistrictSearch', () => {
            const district = new AMap.DistrictSearch({
                level: 'district',
                showbiz: false
            });

            district.search(adcode, (status, result) => {
                if (status === 'complete' && result.districtList && result.districtList[0]) {
                    const bounds = result.districtList[0].boundaries;
                    if (bounds) {
                        bounds.forEach((boundary, index) => {
                            const polygon = new AMap.Polygon({
                                path: boundary,
                                strokeColor: getDistrictColor(index),
                                strokeWeight: 2,
                                fillColor: getDistrictColor(index),
                                fillOpacity: 0.15,
                                strokeOpacity: 0.8
                            });
                            polygon.setMap(map);
                            districtBoundaries.value.push(polygon);
                        });

                        // 调整地图视野
                        map.setFitView(districtBoundaries.value);
                    }
                }
            });
        });
    } catch (error) {
        console.error('加载城市边界失败:', error);
        toast.add({
            severity: 'error',
            summary: '错误',
            detail: '加载城市边界失败',
            life: 3000
        });
    }
};

// 加载行政区边界
const loadDistrictBoundary = async (adcode) => {
    try {
        clearDistrictBoundaries();

        if (!AMap || !map) return;

        AMap.plugin('AMap.DistrictSearch', () => {
            const district = new AMap.DistrictSearch({
                level: 'district',
                showbiz: false
            });

            district.search(adcode, (status, result) => {
                if (status === 'complete' && result.districtList && result.districtList[0]) {
                    const bounds = result.districtList[0].boundaries;
                    if (bounds) {
                        bounds.forEach((boundary) => {
                            const polygon = new AMap.Polygon({
                                path: boundary,
                                strokeColor: '#00ff88',
                                strokeWeight: 3,
                                fillColor: '#00ff88',
                                fillOpacity: 0.15,
                                strokeOpacity: 1
                            });
                            polygon.setMap(map);
                            districtBoundaries.value.push(polygon);
                        });

                        // 调整地图视野
                        map.setFitView(districtBoundaries.value);
                    }
                }
            });
        });
    } catch (error) {
        console.error('加载行政区边界失败:', error);
        toast.add({
            severity: 'error',
            summary: '错误',
            detail: '加载行政区边界失败',
            life: 3000
        });
    }
};

// 清除行政区边界
const clearDistrictBoundaries = () => {
    districtBoundaries.value.forEach((polygon) => {
        polygon.setMap(null);
    });
    districtBoundaries.value = [];
};

// 获取行政区颜色
const getDistrictColor = (index) => {
    const colors = ['#ff4757', '#2ed573', '#1e90ff', '#ffa502', '#ff6b81', '#70a1ff', '#5352ed', '#ff9ff3', '#54a0ff', '#ff6348', '#ff9f43', '#10ac84', '#ee5a24'];
    return colors[index % colors.length];
};

// ==================== 数据缓存和懒加载功能 ====================

// 检查缓存是否有效
const isCacheValid = (cacheItem) => {
    if (!cacheItem.timestamp) return false;
    return Date.now() - cacheItem.timestamp < CACHE_DURATION;
};

// 懒加载标记数据
const loadPinMarkersLazy = async () => {
    try {
        // 检查缓存
        if (isCacheValid(dataCache.value.pins) && dataCache.value.pins.data.length > 0) {
            showCachedPinMarkers();
            return;
        }

        // 显示懒加载进度
        showLazyLoadingProgress('正在加载标记数据...', 0);

        await pinStore.fetchAllPins();
        const pinData = pinStore.allPins;

        // 更新缓存
        dataCache.value.pins = {
            data: pinData,
            timestamp: Date.now(),
            version: dataCache.value.pins.version + 1
        };

        // 分批加载标记到地图
        await loadMarkersInBatches(pinData, 'pin');

        hideLazyLoadingProgress();

        // 显示信息窗口
        infoWindowItems.value = pinData || [];
        infoWindowVisible.value = true;
        infoWindowMode.value = 'list';
        infoWindowListType.value = 'pin';

        toast.add({
            severity: 'info',
            summary: '加载完成',
            detail: `已加载 ${pinData ? pinData.length : 0} 个标记`,
            life: 3000
        });
    } catch (error) {
        hideLazyLoadingProgress();
        console.error('加载 pin marker 失败:', error);
        toast.add({
            severity: 'error',
            summary: '加载失败',
            detail: '加载标记失败，请稍后重试',
            life: 3000
        });
    }
};

// 懒加载照片标记
const loadPhotoMarkersLazy = async () => {
    try {
        if (isCacheValid(dataCache.value.photos) && dataCache.value.photos.data.length > 0) {
            showCachedPhotoMarkers();
            return;
        }

        showLazyLoadingProgress('正在加载照片数据...', 0);

        await photoStore.fetchAllPhotos();
        const photoData = photoStore.allPhotos;

        dataCache.value.photos = {
            data: photoData,
            timestamp: Date.now(),
            version: dataCache.value.photos.version + 1
        };

        await loadMarkersInBatches(photoData, 'photo');

        hideLazyLoadingProgress();

        infoWindowItems.value = photoData || [];
        infoWindowVisible.value = true;
        infoWindowMode.value = 'list';
        infoWindowListType.value = 'photo';

        toast.add({
            severity: 'info',
            summary: '加载完成',
            detail: `已加载 ${photoData ? photoData.length : 0} 个照片标记`,
            life: 3000
        });
    } catch (error) {
        hideLazyLoadingProgress();
        console.error('加载 photo marker 失败:', error);
        toast.add({
            severity: 'error',
            summary: '加载失败',
            detail: '加载照片标记失败，请稍后重试',
            life: 3000
        });
    }
};

// 懒加载全景标记
const loadPanoMarkersLazy = async () => {
    try {
        if (isCacheValid(dataCache.value.panos) && dataCache.value.panos.data.length > 0) {
            showCachedPanoMarkers();
            return;
        }

        showLazyLoadingProgress('正在加载全景数据...', 0);

        await panoStore.fetchAllPanos();
        const panoData = panoStore.allPanos;

        dataCache.value.panos = {
            data: panoData,
            timestamp: Date.now(),
            version: dataCache.value.panos.version + 1
        };

        await loadMarkersInBatches(panoData, 'pano');

        hideLazyLoadingProgress();

        infoWindowItems.value = panoData || [];
        infoWindowVisible.value = true;
        infoWindowMode.value = 'list';
        infoWindowListType.value = 'pano';

        toast.add({
            severity: 'info',
            summary: '加载完成',
            detail: `已加载 ${panoData ? panoData.length : 0} 个全景标记`,
            life: 3000
        });
    } catch (error) {
        hideLazyLoadingProgress();
        console.error('加载 pano marker 失败:', error);
        toast.add({
            severity: 'error',
            summary: '加载失败',
            detail: '加载全景标记失败，请稍后重试',
            life: 3000
        });
    }
};

// ==================== 辅助函数 ====================

// 显示懒加载进度
const showLazyLoadingProgress = (text, percentage) => {
    lazyLoadingProgress.value = {
        show: true,
        text: text,
        percentage: percentage
    };
};

// 隐藏懒加载进度
const hideLazyLoadingProgress = () => {
    lazyLoadingProgress.value.show = false;
};

// 分批加载标记到地图
const loadMarkersInBatches = async (data, type) => {
    const batchSize = 50; // 每批50个标记
    const totalBatches = Math.ceil(data.length / batchSize);

    for (let i = 0; i < totalBatches; i++) {
        const start = i * batchSize;
        const end = Math.min(start + batchSize, data.length);
        const batch = data.slice(start, end);

        // 更新进度
        const percentage = Math.round(((i + 1) / totalBatches) * 100);
        showLazyLoadingProgress(`正在加载${type}标记... (${i + 1}/${totalBatches})`, percentage);

        // 添加当前批次的标记
        batch.forEach((item) => {
            if (type === 'pin') {
                addPinMarkerToMap(item);
            } else if (type === 'photo') {
                addPhotoMarkerToMap(item);
            } else if (type === 'pano') {
                addPanoMarkerToMap(item);
            }
        });

        // 短暂延迟，让UI有时间更新
        await new Promise((resolve) => setTimeout(resolve, 50));
    }
};

// 显示缓存的标记
const showCachedPinMarkers = () => {
    const cachedData = dataCache.value.pins.data;
    infoWindowItems.value = cachedData;
    infoWindowVisible.value = true;
    infoWindowMode.value = 'list';
    infoWindowListType.value = 'pin';

    toast.add({
        severity: 'info',
        summary: '从缓存加载',
        detail: `已从缓存加载 ${cachedData.length} 个标记`,
        life: 3000
    });
};

const showCachedPhotoMarkers = () => {
    const cachedData = dataCache.value.photos.data;
    infoWindowItems.value = cachedData;
    infoWindowVisible.value = true;
    infoWindowMode.value = 'list';
    infoWindowListType.value = 'photo';

    toast.add({
        severity: 'info',
        summary: '从缓存加载',
        detail: `已从缓存加载 ${cachedData.length} 个照片标记`,
        life: 3000
    });
};

const showCachedPanoMarkers = () => {
    const cachedData = dataCache.value.panos.data;
    infoWindowItems.value = cachedData;
    infoWindowVisible.value = true;
    infoWindowMode.value = 'list';
    infoWindowListType.value = 'pano';

    toast.add({
        severity: 'info',
        summary: '从缓存加载',
        detail: `已从缓存加载 ${cachedData.length} 个全景标记`,
        life: 3000
    });
};

// 添加标记到地图的函数（简化版，需要根据实际需求完善）
const addPinMarkerToMap = (item) => {
    if (!map || !AMap || !item.gcj_lng || !item.gcj_lat) return;

    const marker = new AMap.Marker({
        position: [item.gcj_lng, item.gcj_lat],
        icon: new AMap.Icon({
            image: ICON_IMG.CAMERA,
            size: new AMap.Size(32, 32),
            imageSize: new AMap.Size(32, 32)
        }),
        extData: { id: item.id, type: 'pin', data: item }
    });

    marker.setMap(map);
    markers.value.push(marker);
    mapService.addMarker(`pin-${item.id}`, marker);
};

const addPhotoMarkerToMap = (item) => {
    if (!map || !AMap || !item.gcj_lng || !item.gcj_lat) return;

    const marker = new AMap.Marker({
        position: [item.gcj_lng, item.gcj_lat],
        icon: new AMap.Icon({
            image: ICON_IMG.photo,
            size: new AMap.Size(32, 32),
            imageSize: new AMap.Size(32, 32)
        }),
        extData: { id: item.id, type: 'photo', data: item }
    });

    marker.setMap(map);
    markers.value.push(marker);
    mapService.addMarker(`photo-${item.id}`, marker);
};

const addPanoMarkerToMap = (item) => {
    if (!map || !AMap || !item.gcj_lng || !item.gcj_lat) return;

    const marker = new AMap.Marker({
        position: [item.gcj_lng, item.gcj_lat],
        icon: new AMap.Icon({
            image: ICON_IMG.PANO,
            size: new AMap.Size(32, 32),
            imageSize: new AMap.Size(32, 32)
        }),
        extData: { id: item.id, type: 'pano', data: item }
    });

    marker.setMap(map);
    markers.value.push(marker);
    mapService.addMarker(`pano-${item.id}`, marker);
};

// ==================== 事件处理函数 ====================

// 位置选择器
const enableLocationPicker = (type) => {
    mapStore.enableLocationPicker(type);
};

// 信息窗口事件处理
const closeInfoWindow = () => {
    infoWindowVisible.value = false;
};

const handleInfoWindowSubmit = (formData) => {
    console.log('提交表单数据:', formData);
    // 这里添加提交逻辑
};

const handleInfoWindowEdit = (item) => {
    console.log('编辑项目:', item);
    // 这里添加编辑逻辑
};

const handleInfoWindowDelete = (item) => {
    console.log('删除项目:', item);
    // 这里添加删除逻辑
};

const handleBatchDelete = (items) => {
    console.log('批量删除项目:', items);
    // 这里添加批量删除逻辑
};

const handleInfoWindowItemClick = (item) => {
    console.log('点击项目:', item);
    // 这里添加项目点击逻辑
};

const handleInfoWindowSelectItem = (item) => {
    console.log('选择项目:', item);
    // 这里添加项目选择逻辑
};

// 页面编辑器
const closePageEditor = () => {
    pageEditorVisible.value = false;
};

// 搜索处理
const handleSearch = () => {
    console.log('搜索:', searchText.value);
    // 这里添加搜索逻辑
};

// Dock项目点击
const onDockItemClick = (event, item) => {
    item.command();
};

// 批量上传相关
const savePhotoMarkers = async () => {
    // 批量保存照片标记
};

const savePanoMarkers = async () => {
    // 批量保存全景标记
};

const unlockSubmitPhotos = () => {
    batchUploaded.value = true;
};

const unlockSubmitPanos = () => {
    batchUploaded.value = true;
};

const handleBatchFormConfirm = async () => {
    uploadingFile.value = true;
    try {
        if (batchTabsStatus.value === 'photo') {
            await savePhotoMarkers();
        } else {
            await savePanoMarkers();
        }
        showBatchUploadDialog.value = false;
    } finally {
        uploadingFile.value = false;
        batchUploaded.value = false;
    }
};

const cancelUploadFile = () => {
    showBatchUploadDialog.value = false;
    batchUploaded.value = false;
};

// ==================== 生命周期 ====================

onMounted(async () => {
    try {
        isInitialLoading.value = true;

        // 初始化地图
        AMap = await mapService.initMap();
        map = await mapService.createMap('my-container');
        const mapInstance = await mapService.getMap();
        mapStore.fetchMapOptions(mapInstance);

        // 初始化城市选项
        onCityChange();

        toast.add({
            severity: 'success',
            summary: '地图加载完成',
            detail: 'MyMap已准备就绪，支持懒加载和缓存',
            life: 3000
        });
    } catch (error) {
        console.error('地图初始化失败:', error);
        toast.add({
            severity: 'error',
            summary: '错误',
            detail: '地图加载失败，请刷新页面重试',
            life: 5000
        });
    } finally {
        isInitialLoading.value = false;
    }
});

onUnmounted(() => {
    // 清理资源
    clearDistrictBoundaries();

    // 清除所有标记
    markers.value.forEach((marker) => {
        marker.setMap(null);
    });
    markers.value = [];

    // 销毁地图实例
    if (map) {
        mapService.destroy();
        map = null;
        AMap = null;
    }
});
</script>

<style lang="scss" scoped>
.map {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

#my-container {
    width: 100%;
    height: 100%;
}

.map-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1021;
}

.district-tool-panel {
    position: absolute;
    top: 100%;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 16px;
    min-width: 300px;
    z-index: 1020;
}

.district-tool-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.district-section {
    display: flex;
    align-items: center;
    gap: 8px;
}

.district-section label {
    min-width: 70px;
    font-weight: 500;
    color: #374151;
}

.district-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-spinner {
    text-align: center;
    color: #6366f1;
}

.loading-text {
    margin-top: 16px;
    font-size: 16px;
    font-weight: 500;
}

.lazy-loading-indicator {
    position: absolute;
    bottom: 100px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 16px;
    z-index: 1500;
}

.progress-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.progress-text {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
}

.panorama-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1500;
    background: rgba(0, 0, 0, 0.9);
}

.cubemap-header,
.panorama-header {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    font-size: 18px;
    font-weight: 500;
    z-index: 1501;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 24px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.zoom-display {
    margin-right: 16px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    color: #374151;
}

// 过渡动画
.slide-down-enter-active,
.slide-down-leave-active {
    transition: all 0.3s ease;
}

.slide-down-enter-from {
    opacity: 0;
    transform: translateY(-10px);
}

.slide-down-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>
