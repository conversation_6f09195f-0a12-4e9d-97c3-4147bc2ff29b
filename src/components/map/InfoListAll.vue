<template>
    <div class="all-info-list">
        <div class="list-header">
            <h3>所有标记列表</h3>
            <div class="list-actions">
                <Button icon="pi pi-trash" class="p-button-danger p-button-sm" :disabled="selectedItems.length === 0" @click="handleDeleteSelected" />
            </div>
        </div>

        <TabView>
            <TabPanel header="所有标记">
                <DataTable
                    :value="allMarkers"
                    v-model:selection="selectedItems"
                    dataKey="id"
                    :paginator="true"
                    :rows="10"
                    :rowsPerPageOptions="[5, 10, 20]"
                    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
                    responsiveLayout="scroll"
                >
                    <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>
                    <Column field="markerType" header="类型" sortable>
                        <template #body="slotProps">
                            <Tag :value="slotProps.data.markerType" :severity="getTypeSeverity(slotProps.data.markerType)" />
                        </template>
                    </Column>
                    <Column field="name" header="名称" sortable>
                        <template #body="slotProps">
                            <span class="marker-name">{{ slotProps.data.name }}</span>
                        </template>
                    </Column>
                    <Column field="created_at" header="创建时间" sortable>
                        <template #body="slotProps">
                            {{ formatDate(slotProps.data.created_at) }}
                        </template>
                    </Column>
                    <Column field="label" header="标签">
                        <template #body="slotProps">
                            <div class="tag-container">
                                <Tag v-for="(tag, index) in getTagsArray(slotProps.data.label)" :key="index" :value="tag" :severity="getTagSeverity(tag)" />
                                <span v-if="getTagsArray(slotProps.data.label).length === 0" class="no-tags">无标签</span>
                            </div>
                        </template>
                    </Column>
                    <Column header="预览">
                        <template #body="slotProps">
                            <img v-if="slotProps.data.thumbnail_url" :src="slotProps.data.thumbnail_url" alt="缩略图" class="thumbnail-preview" />
                            <i v-else-if="slotProps.data.icon" :class="'pi ' + getIconClass(slotProps.data.icon)" style="font-size: 1.5rem"></i>
                            <i v-else class="pi pi-map-marker" style="font-size: 1.5rem"></i>
                        </template>
                    </Column>
                    <Column header="操作">
                        <template #body="slotProps">
                            <Button icon="pi pi-eye" class="p-button-rounded p-button-info p-button-sm" @click="handleView(slotProps.data)" />
                            <Button icon="pi pi-pencil" class="p-button-rounded p-button-success p-button-sm" @click="handleEdit(slotProps.data)" />
                        </template>
                    </Column>
                </DataTable>
            </TabPanel>

            <TabPanel header="引脚标记">
                <DataTable :value="pinMarkers" v-model:selection="selectedPinItems" dataKey="id" :paginator="true" :rows="10" :rowsPerPageOptions="[5, 10, 20]">
                    <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>
                    <Column field="name" header="名称" sortable></Column>
                    <Column field="category" header="分类" sortable>
                        <template #body="slotProps">
                            <Tag :value="slotProps.data.category" :severity="getCategorySeverity(slotProps.data.category)" />
                        </template>
                    </Column>
                    <Column field="created_at" header="创建时间" sortable>
                        <template #body="slotProps">
                            {{ formatDate(slotProps.data.created_at) }}
                        </template>
                    </Column>
                    <Column header="操作">
                        <template #body="slotProps">
                            <Button icon="pi pi-eye" class="p-button-rounded p-button-info p-button-sm" @click="handleViewPin(slotProps.data)" />
                            <Button icon="pi pi-pencil" class="p-button-rounded p-button-success p-button-sm" @click="handleEditPin(slotProps.data)" />
                        </template>
                    </Column>
                </DataTable>
            </TabPanel>

            <TabPanel header="照片标记">
                <DataTable :value="photoMarkers" v-model:selection="selectedPhotoItems" dataKey="id" :paginator="true" :rows="10" :rowsPerPageOptions="[5, 10, 20]">
                    <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>
                    <Column field="name" header="名称" sortable></Column>
                    <Column field="city" header="城市" sortable></Column>
                    <Column field="gps_timestamp" header="拍摄时间" sortable>
                        <template #body="slotProps">
                            {{ formatDate(slotProps.data.gps_timestamp) }}
                        </template>
                    </Column>
                    <Column header="缩略图">
                        <template #body="slotProps">
                            <img v-if="slotProps.data.thumbnail_url" :src="slotProps.data.thumbnail_url" alt="缩略图" class="thumbnail-preview" />
                        </template>
                    </Column>
                    <Column header="操作">
                        <template #body="slotProps">
                            <Button icon="pi pi-eye" class="p-button-rounded p-button-info p-button-sm" @click="handleViewPhoto(slotProps.data)" />
                            <Button icon="pi pi-pencil" class="p-button-rounded p-button-success p-button-sm" @click="handleEditPhoto(slotProps.data)" />
                        </template>
                    </Column>
                </DataTable>
            </TabPanel>

            <TabPanel header="全景标记">
                <DataTable :value="panoMarkers" v-model:selection="selectedPanoItems" dataKey="id" :paginator="true" :rows="10" :rowsPerPageOptions="[5, 10, 20]">
                    <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>
                    <Column field="name" header="名称" sortable></Column>
                    <Column field="created_at" header="创建时间" sortable>
                        <template #body="slotProps">
                            {{ formatDate(slotProps.data.created_at) }}
                        </template>
                    </Column>
                    <Column header="缩略图">
                        <template #body="slotProps">
                            <img v-if="slotProps.data.thumbnail_url" :src="slotProps.data.thumbnail_url" alt="缩略图" class="thumbnail-preview" />
                        </template>
                    </Column>
                    <Column header="操作">
                        <template #body="slotProps">
                            <Button icon="pi pi-eye" class="p-button-rounded p-button-info p-button-sm" @click="handleViewPano(slotProps.data)" />
                            <Button icon="pi pi-pencil" class="p-button-rounded p-button-success p-button-sm" @click="handleEditPano(slotProps.data)" />
                        </template>
                    </Column>
                </DataTable>
            </TabPanel>
        </TabView>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';
import Tag from 'primevue/tag';
import TabView from 'primevue/tabview';
import TabPanel from 'primevue/tabpanel';

const props = defineProps({
    pinItems: {
        type: Array,
        default: () => []
    },
    photoItems: {
        type: Array,
        default: () => []
    },
    panoItems: {
        type: Array,
        default: () => []
    }
});

const emit = defineEmits(['select-item', 'edit-item', 'delete-items', 'locate-marker']);

// 各类型标记的选中项
const selectedItems = ref([]);
const selectedPinItems = ref([]);
const selectedPhotoItems = ref([]);
const selectedPanoItems = ref([]);

// 计算属性：所有标记的列表
const allMarkers = computed(() => {
    const pinMarkers = props.pinItems.map((item) => ({
        ...item,
        markerType: 'pin'
    }));

    const photoMarkers = props.photoItems.map((item) => ({
        ...item,
        markerType: 'photo'
    }));

    const panoMarkers = props.panoItems.map((item) => ({
        ...item,
        markerType: 'pano'
    }));

    return [...pinMarkers, ...photoMarkers, ...panoMarkers];
});

// 各类型标记的列表
const pinMarkers = computed(() => props.pinItems);
const photoMarkers = computed(() => props.photoItems);
const panoMarkers = computed(() => props.panoItems);

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '未知时间';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// 获取类型样式
const getTypeSeverity = (type) => {
    const severityMap = {
        pin: 'info',
        photo: 'success',
        pano: 'warning',
        page: 'secondary'
    };
    return severityMap[type] || 'info';
};

// 获取分类样式
const getCategorySeverity = (category) => {
    const severityMap = {
        景点: 'success',
        餐饮: 'warning',
        住宿: 'info',
        购物: 'danger',
        交通: 'primary'
    };
    return severityMap[category] || 'secondary';
};

// 获取图标类
const getIconClass = (iconName) => {
    const iconMap = {
        default: 'pi-map-marker',
        attraction: 'pi-camera',
        food: 'pi-shopping-bag',
        hotel: 'pi-home',
        shopping: 'pi-shopping-cart',
        transport: 'pi-car'
    };
    return iconMap[iconName] || 'pi-map-marker';
};

// 将标签字段转换为数组
const getTagsArray = (label) => {
    if (!label) return [];
    if (Array.isArray(label)) return label;
    if (typeof label === 'string') {
        // 直接按逗号分割字符串
        return label
            .split(',')
            .map((tag) => tag.trim())
            .filter((tag) => tag);
    }
    return [];
};

// 获取标签样式
const getTagSeverity = (tag) => {
    const tagMap = {
        景点: 'success',
        餐饮: 'warning',
        住宿: 'info',
        购物: 'danger',
        交通: 'primary'
    };

    return tagMap[tag] || 'secondary';
};

// 处理查看标记（统一处理所有类型）
const handleView = (item) => {
    emit('select-item', item);
    emit('locate-marker', item); // 添加定位功能
};

// 处理编辑标记（统一处理所有类型）
const handleEdit = (item) => {
    emit('edit-item', item);
};

// 处理特定类型的标记查看和编辑
const handleViewPin = (item) => {
    emit('select-item', { ...item, markerType: 'pin' });
    emit('locate-marker', item);
};

const handleEditPin = (item) => {
    emit('edit-item', { ...item, markerType: 'pin' });
};

const handleViewPhoto = (item) => {
    emit('select-item', { ...item, markerType: 'photo' });
    emit('locate-marker', item);
};

const handleEditPhoto = (item) => {
    emit('edit-item', { ...item, markerType: 'photo' });
};

const handleViewPano = (item) => {
    emit('select-item', { ...item, markerType: 'pano' });
    emit('locate-marker', item);
};

const handleEditPano = (item) => {
    emit('edit-item', { ...item, markerType: 'pano' });
};

// 处理批量删除标记
const handleDeleteSelected = () => {
    if (selectedItems.value.length > 0) {
        const itemsToDelete = selectedItems.value.map((item) => ({
            id: item.id,
            markerType: item.markerType
        }));
        emit('delete-items', itemsToDelete);
        selectedItems.value = [];
    }
};
</script>

<style lang="scss" scoped>
.all-info-list {
    padding: 10px;

    .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        h3 {
            margin: 0;
            font-size: 1.1rem;
        }

        .list-actions {
            display: flex;
            gap: 5px;
        }
    }

    .marker-name {
        font-weight: 500;
    }

    .thumbnail-preview {
        width: 40px;
        height: 30px;
        object-fit: cover;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .tag-container {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        max-width: 200px;

        .no-tags {
            color: #999;
            font-style: italic;
            font-size: 0.9rem;
        }
    }
}
</style>
