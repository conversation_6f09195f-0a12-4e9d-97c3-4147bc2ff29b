<template>
    <div class="fang-map">
        <Toast />
        <div id="fang-container" class="map-container">
            <!-- 顶部菜单 -->
            <div class="map-overlay">
                <Menubar :model="fangMenuItems" position="top">
                    <template #end>
                        <div class="toolbar">
                            <div class="zoom-display">
                                <span>ZOOM: {{ mapStore.mapOptions.zoom }}</span>
                            </div>
                            <IconField iconPosition="left">
                                <InputIcon class="pi pi-search" />
                                <InputText id="search-input" v-model="searchText" placeholder="搜索小区" @keyup.enter="handleSearch" />
                            </IconField>
                            <Button icon="pi pi-search" @click="handleSearch" />
                        </div>
                    </template>
                </Menubar>
            </div>

            <!-- 加载状态 -->
            <div v-if="isLoading" class="loading-overlay">
                <div class="loading-content">
                    <i class="pi pi-spin pi-spinner" style="font-size: 2rem"></i>
                    <p>房产数据加载中...</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import Toast from 'primevue/toast';
import Menubar from 'primevue/menubar';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import { onMounted, onUnmounted, ref, nextTick } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useMapStore } from '@/stores/mapStore';

import mapService from '@/services/mapService';
import { supabase } from '@/services/supabase';

// 响应式数据
const toast = useToast();
const mapStore = useMapStore();

let map = null;
let AMap = null;
let currentInfoWindow = null;
let hoverTimer = null;

// 状态变量
const isLoading = ref(false);
const searchText = ref('');

// 房产标记集合
const fangMarkers = ref([]);

// 菜单配置
const fangMenuItems = ref([
    {
        label: '房产地图',
        items: [
            {
                label: '加载所有小区',
                icon: 'pi pi-fw pi-home',
                command: () => loadAllFangMarkers()
            },
            {
                label: '清除标记',
                icon: 'pi pi-fw pi-times',
                command: () => clearAllMarkers()
            },
            {
                label: '刷新数据',
                icon: 'pi pi-fw pi-refresh',
                command: () => refreshFangData()
            }
        ]
    }
]);

// 从数据库获取房产数据
const getFangData = async () => {
    try {
        const { data, error } = await supabase.from('fang_data').select('*').not('gcj', 'is', null);

        if (error) throw error;
        return data || [];
    } catch (error) {
        console.error('获取房产数据失败:', error);
        throw error;
    }
};

// 搜索处理
const handleSearch = async () => {
    if (!searchText.value.trim()) return;

    try {
        const { data, error } = await supabase.from('fang_data').select('*').ilike('name', `%${searchText.value}%`).not('gcj', 'is', null).limit(10);

        if (error) throw error;

        if (data && data.length > 0) {
            // 清除现有标记
            clearAllMarkers();

            // 添加搜索结果标记
            data.forEach((item) => addFangMarker(item));

            // 定位到第一个结果
            if (data[0].gcj) {
                map.setCenter([data[0].gcj.lng, data[0].gcj.lat]);
                map.setZoom(14);
            }

            toast.add({
                severity: 'success',
                summary: '搜索完成',
                detail: `找到 ${data.length} 个相关小区`,
                life: 3000
            });
        } else {
            toast.add({
                severity: 'warn',
                summary: '搜索结果',
                detail: '未找到相关小区',
                life: 3000
            });
        }
    } catch (error) {
        console.error('搜索失败:', error);
        toast.add({
            severity: 'error',
            summary: '搜索失败',
            detail: '搜索过程中发生错误',
            life: 3000
        });
    }
};

// 加载所有房产标记
const loadAllFangMarkers = async () => {
    try {
        isLoading.value = true;
        const fangData = await getFangData();

        // 清除现有标记
        clearAllMarkers();

        // 添加新标记
        if (fangData && fangData.length > 0) {
            fangData.forEach((item) => {
                addFangMarker(item);
            });

            toast.add({
                severity: 'success',
                summary: '加载完成',
                detail: `已加载 ${fangData.length} 个小区标记`,
                life: 3000
            });
        }
    } catch (error) {
        console.error('加载房产标记失败:', error);
        toast.add({
            severity: 'error',
            summary: '加载失败',
            detail: '加载房产数据失败，请稍后重试',
            life: 3000
        });
    } finally {
        isLoading.value = false;
    }
};

// 添加房产标记到地图
const addFangMarker = (fangData) => {
    if (!map || !AMap || !fangData.gcj) return;

    // 根据sort类型选择图标
    const iconUrl = fangData.sort === 'resale' ? '/fang-red-32.png' : '/fang-green-32.png';

    const marker = new AMap.Marker({
        position: [fangData.gcj.lng, fangData.gcj.lat],
        icon: new AMap.Icon({
            image: iconUrl,
            imageSize: new AMap.Size(32, 32)
        }),
        anchor: 'center',
        title: fangData.name,
        extData: fangData
    });

    // 创建信息窗口内容
    const createInfoContent = (data) => {
        let content = `
            <div class="fang-info-panel" style="padding: 15px; min-width: 250px; font-family: Arial, sans-serif;">
                <h3 style="margin: 0 0 10px 0; color: #333; font-size: 16px;">${data.name}</h3>
                <p style="margin: 5px 0; color: #666;"><strong>地址:</strong> ${data.district} ${data.location}</p>
                <p style="margin: 5px 0; color: #e74c3c; font-weight: bold; font-size: 14px;"><strong>房价:</strong> ${data.price} 元/㎡</p>
        `;

        // 根据sort类型显示不同信息
        if (data.sort === 'new') {
            if (data.tags) content += `<p style="margin: 5px 0; color: #666;"><strong>标签:</strong> ${data.tags}</p>`;
            if (data.jianmian) content += `<p style="margin: 5px 0; color: #666;"><strong>建筑面积:</strong> ${data.jianmian}</p>`;
        } else if (data.sort === 'resale') {
            if (data.establish) content += `<p style="margin: 5px 0; color: #666;"><strong>交房时间:</strong> ${data.establish}</p>`;
            if (data.on_sold) content += `<p style="margin: 5px 0; color: #666;"><strong>在售量:</strong> ${data.on_sold}</p>`;
        }

        // 添加链接图标
        content += '<div style="margin-top: 10px; display: flex; gap: 10px;">';
        if (data.ajk_pano) {
            content += `<img src="/ajk-pano_icon.png" style="width: 24px; height: 24px; cursor: pointer;" onclick="window.open('${data.ajk_pano}', '_blank')" title="安居客全景图" />`;
        }
        if (data.ajk_url) {
            content += `<img src="/ajk-icon.png" style="width: 24px; height: 24px; cursor: pointer;" onclick="window.open('${data.ajk_url}', '_blank')" title="安居客链接" />`;
        }
        content += '</div></div>';

        return content;
    };

    // 创建信息窗体
    const infoWindow = new AMap.InfoWindow({
        isCustom: false,
        content: createInfoContent(fangData),
        offset: new AMap.Pixel(0, -30)
    });

    // 添加悬停事件
    marker.on('mouseover', () => {
        // 清除之前的定时器
        if (hoverTimer) {
            clearTimeout(hoverTimer);
            hoverTimer = null;
        }

        // 关闭当前信息窗口
        if (currentInfoWindow) {
            currentInfoWindow.close();
        }

        // 打开新的信息窗口
        infoWindow.open(map, marker.getPosition());
        currentInfoWindow = infoWindow;
    });

    marker.on('mouseout', () => {
        // 设置延迟关闭
        hoverTimer = setTimeout(() => {
            if (currentInfoWindow === infoWindow) {
                infoWindow.close();
                currentInfoWindow = null;
            }
        }, 1000);
    });

    // 添加到地图和集合
    map.add(marker);
    fangMarkers.value.push(marker);
};

// 清除所有标记
const clearAllMarkers = () => {
    if (fangMarkers.value.length > 0) {
        fangMarkers.value.forEach((marker) => {
            map.remove(marker);
        });
        fangMarkers.value = [];
    }

    // 关闭信息窗口
    if (currentInfoWindow) {
        currentInfoWindow.close();
        currentInfoWindow = null;
    }

    // 清除定时器
    if (hoverTimer) {
        clearTimeout(hoverTimer);
        hoverTimer = null;
    }
};

// 刷新房产数据
const refreshFangData = async () => {
    await loadAllFangMarkers();
    toast.add({
        severity: 'info',
        summary: '刷新完成',
        detail: '房产数据已刷新',
        life: 3000
    });
};

// 组件挂载
onMounted(async () => {
    try {
        isLoading.value = true;
        AMap = await mapService.initMap();
        map = await mapService.createMap('fang-container');
        const mapInstance = await mapService.getMap();
        mapStore.fetchMapOptions(mapInstance);

        // 首次加载房产标记
        await loadAllFangMarkers();

        toast.add({
            severity: 'success',
            summary: '地图加载完成',
            detail: '房产地图已准备就绪',
            life: 3000
        });
    } catch (error) {
        console.error('地图初始化失败:', error);
        toast.add({
            severity: 'error',
            summary: '错误',
            detail: '地图加载失败，请刷新页面重试',
            life: 5000
        });
    } finally {
        isLoading.value = false;
    }
});

// 组件卸载
onUnmounted(() => {
    clearAllMarkers();
});

// 组件挂载
onMounted(async () => {
    try {
        isLoading.value = true;
        AMap = await mapService.initMap();
        map = await mapService.createMap('container');
        const mapInstance = await mapService.getMap();
        mapStore.fetchMapOptions(mapInstance);

        // 首次加载房产标记
        await loadAllFangMarkers();
    } catch (error) {
        console.error('地图初始化失败:', error);
        toast.add({
            severity: 'error',
            summary: '错误',
            detail: '地图加载失败，请刷新页面重试',
            life: 5000
        });
    } finally {
        isLoading.value = false;
    }
});

// 组件卸载
onUnmounted(() => {
    clearAllMarkers();
});
</script>

<style lang="scss" scoped>
.fang-map {
    height: calc(100vh - 7vh);
    background-color: #f5f5f5;
    position: relative;
    overflow: hidden;
}

.map-container {
    height: 100%;
    width: 100%;
    position: relative;
    overflow: hidden;
}

#fang-container {
    height: 100%;
    width: 100%;
    position: relative;
    overflow: hidden;

    :deep(.amap-layers) {
        z-index: 0 !important;
    }
}

.map-overlay {
    position: relative;
    z-index: 1021;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toolbar {
    display: flex;
    align-items: center;
    gap: 10px;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.loading-content {
    text-align: center;

    p {
        margin-top: 1rem;
        font-size: 1.1rem;
        color: #666;
    }
}

.zoom-display {
    padding: 0.5rem;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    font-weight: bold;
    font-size: 12px;
}

/* 全局样式 - 信息窗口 */
:global(.fang-info-panel) {
    font-family: Arial, sans-serif;

    h3 {
        margin: 0 0 10px 0;
        color: #333;
        font-size: 16px;
    }

    p {
        margin: 5px 0;
        color: #666;
        font-size: 13px;
    }

    img {
        transition: transform 0.2s;

        &:hover {
            transform: scale(1.1);
        }
    }
}
</style>
