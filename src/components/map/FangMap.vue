<template>
    <div class="map">
        <!-- 添加Toast组件 -->
        <Toast />
        <div id="container" @click.stop>
            <!-- menu 菜单 -->
            <div class="map-overlay" style="position: relative; z-index: 1021">
                <Menubar :model="fangMenuItems" position="top">
                    <template #end>
                        <div style="display: flex; align-items: center">
                            <div class="zoom-display">
                                <span> ZOOM: {{ mapStore.mapOptions.zoom }}</span>
                            </div>
                            <IconField iconPosition="left">
                                <InputIcon class="pi pi-search" />
                                <InputText id="search-input" v-model="searchText" placeholder="搜索小区" @keyup.enter="handleSearch" />
                            </IconField>
                            <Button icon="pi pi-search" @click="handleSearch" />
                        </div>
                    </template>
                </Menubar>
            </div>

            <!-- 搜索结果面板 -->
            <div id="panel" style="position: absolute; top: 60px; right: 10px; z-index: 1000; background: white; border-radius: 5px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); max-width: 300px; max-height: 400px; overflow-y: auto"></div>

            <!-- 加载状态 -->
            <div v-if="isLoading" class="loading-overlay">
                <div class="loading-content">
                    <i class="pi pi-spin pi-spinner" style="font-size: 2rem"></i>
                    <p>房产数据加载中...</p>
                </div>
            </div>

            <!-- 房产信息窗口 -->
            <Dialog v-model:visible="fangDetailVisible" modal header="小区详情" :style="{ width: '80vw', height: '80vh' }" :maximizable="true">
                <div v-if="currentFangData" class="fang-detail">
                    <div class="fang-header">
                        <h2>{{ currentFangData.name }}</h2>
                        <span class="fang-price">{{ currentFangData.price }} 元/㎡</span>
                    </div>
                    <div class="fang-info">
                        <p><strong>区域：</strong>{{ currentFangData.district }} - {{ currentFangData.location }}</p>
                        <p><strong>建筑面积：</strong>{{ currentFangData.jianmian || '暂无数据' }}</p>
                        <p><strong>建成年份：</strong>{{ currentFangData.establish || '暂无数据' }}</p>
                        <p><strong>在售套数：</strong>{{ currentFangData['on-sold'] || '暂无数据' }}</p>
                        <p v-if="currentFangData.tags"><strong>标签：</strong>{{ currentFangData.tags }}</p>
                    </div>
                    <div class="fang-actions">
                        <Button v-if="currentFangData.ajk_pano" label="查看全景图" icon="pi pi-eye" @click="openPanorama" />
                        <Button v-if="currentFangData.ajk_url" label="查看详情" icon="pi pi-external-link" @click="openAjkUrl" />
                    </div>
                </div>
            </Dialog>

            <!-- 全景图显示 -->
            <Dialog v-model:visible="panoVisible" modal header="小区全景图" :style="{ width: '90vw', height: '90vh' }" :maximizable="true">
                <iframe v-if="currentPanoUrl" :src="currentPanoUrl" style="width: 100%; height: 100%; border: none"></iframe>
            </Dialog>
        </div>
    </div>
</template>

<script setup>
import Dialog from 'primevue/dialog';
import Toast from 'primevue/toast';
import Menubar from 'primevue/menubar';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import { onMounted, onUnmounted, ref } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useMapStore } from '@/stores/mapStore';
import { useFangStore } from '@/stores/fangStore';
import mapService from '@/services/mapService';
import geocodingService from '@/services/geocodingService';
import { ICON_IMG } from '@/config/img.js';

// 响应式数据
const toast = useToast();
const mapStore = useMapStore();
const fangStore = useFangStore();

let map = null;
let AMap = null;
let geocoder = null;

// 状态变量
const isLoading = ref(false);
const searchText = ref('');
const fangDetailVisible = ref(false);
const panoVisible = ref(false);
const currentFangData = ref(null);
const currentPanoUrl = ref('');

// 房产标记集合
const fangMarkers = ref([]);

// 菜单配置
const fangMenuItems = ref([
    {
        label: '房产',
        items: [
            {
                label: '显示所有小区',
                icon: 'pi pi-fw pi-home',
                command: () => loadFangMarkers()
            },
            {
                label: '导入数据',
                icon: 'pi pi-fw pi-upload',
                command: () => importFangData()
            },
            {
                label: '清除标记',
                icon: 'pi pi-fw pi-times',
                command: () => clearFangMarkers()
            },
            {
                label: '测试地理编码',
                icon: 'pi pi-fw pi-map-marker',
                command: () => testGeocoding()
            },
            {
                label: '批量地理编码',
                icon: 'pi pi-fw pi-cog',
                command: () => batchGeocoding()
            }
        ]
    }
]);

// 初始化搜索功能
const initSearch = () => {
    if (!AMap) return;

    AMap.plugin(['AMap.Geocoder'], () => {
        geocoder = new AMap.Geocoder({
            city: '武汉',
            radius: 1000
        });
    });
};

// 搜索处理
const handleSearch = () => {
    if (!searchText.value || !geocoder) return;

    geocoder.getLocation(searchText.value, (status, result) => {
        if (status === 'complete' && result.geocodes.length) {
            const lnglat = result.geocodes[0].location;
            map.setCenter(lnglat);
            map.setZoom(15);
        } else {
            toast.add({
                severity: 'warn',
                summary: '搜索失败',
                detail: '未找到相关地点',
                life: 3000
            });
        }
    });
};

// 加载房产标记
const loadFangMarkers = async () => {
    try {
        isLoading.value = true;
        await fangStore.fetchAllFangData();
        const fangData = fangStore.allFangData;

        // 清除现有标记
        clearFangMarkers();

        // 添加新标记
        if (fangData && fangData.length > 0) {
            fangData.forEach((item) => {
                addFangMarkerToMap(item);
            });

            toast.add({
                severity: 'success',
                summary: '加载完成',
                detail: `已加载 ${fangData.length} 个小区标记`,
                life: 3000
            });
        }
    } catch (error) {
        console.error('加载房产标记失败:', error);
        toast.add({
            severity: 'error',
            summary: '加载失败',
            detail: '加载房产数据失败，请稍后重试',
            life: 3000
        });
    } finally {
        isLoading.value = false;
    }
};

// 添加房产标记到地图
const addFangMarkerToMap = (fangData) => {
    if (!map || !AMap || !fangData.gcj_lng || !fangData.gcj_lat) return;

    const marker = new AMap.Marker({
        position: [parseFloat(fangData.gcj_lng), parseFloat(fangData.gcj_lat)],
        icon: new AMap.Icon({
            image: ICON_IMG.HOUSE,
            imageSize: new AMap.Size(32, 32)
        }),
        anchor: 'center',
        title: fangData.name,
        extData: fangData
    });

    // 创建信息窗体
    const infoWindow = new AMap.InfoWindow({
        isCustom: false,
        content: `<div style="text-align: center; padding: 10px;">
            <h4>${fangData.name}</h4>
            <p>${fangData.district} - ${fangData.location}</p>
            <p style="color: #e74c3c; font-weight: bold;">${fangData.price} 元/㎡</p>
        </div>`,
        offset: new AMap.Pixel(0, -30)
    });

    // 添加事件监听
    marker.on('mouseover', () => infoWindow.open(map, marker.getPosition()));
    marker.on('mouseout', () => infoWindow.close());
    marker.on('click', () => showFangDetail(fangData));

    // 添加到地图和集合
    map.add(marker);
    fangMarkers.value.push(marker);
};

// 显示房产详情
const showFangDetail = (fangData) => {
    currentFangData.value = fangData;
    fangDetailVisible.value = true;
};

// 打开全景图
const openPanorama = () => {
    if (currentFangData.value && currentFangData.value.ajk_pano) {
        currentPanoUrl.value = currentFangData.value.ajk_pano;
        panoVisible.value = true;
    }
};

// 打开安居客链接
const openAjkUrl = () => {
    if (currentFangData.value && currentFangData.value.ajk_url) {
        window.open(currentFangData.value.ajk_url, '_blank');
    }
};

// 清除房产标记
const clearFangMarkers = () => {
    if (fangMarkers.value.length > 0) {
        fangMarkers.value.forEach((marker) => {
            map.remove(marker);
        });
        fangMarkers.value = [];
    }
};

// 导入房产数据
const importFangData = async () => {
    try {
        isLoading.value = true;
        await fangStore.importCsvData();
        toast.add({
            severity: 'success',
            summary: '导入成功',
            detail: '房产数据导入完成',
            life: 3000
        });
        // 重新加载标记
        await loadFangMarkers();
    } catch (error) {
        console.error('导入数据失败:', error);
        toast.add({
            severity: 'error',
            summary: '导入失败',
            detail: '导入房产数据失败，请稍后重试',
            life: 3000
        });
    } finally {
        isLoading.value = false;
    }
};

// 测试地理编码
const testGeocoding = async () => {
    try {
        isLoading.value = true;
        toast.add({ severity: 'info', summary: '测试中', detail: '正在测试地理编码功能...', life: 3000 });

        // 初始化地理编码服务
        await geocodingService.initialize(AMap);

        // 处理5条测试数据
        const result = await geocodingService.processTestData(5);

        toast.add({
            severity: 'success',
            summary: '测试完成',
            detail: `处理了 ${result.total} 条数据，成功 ${result.success} 条，失败 ${result.failed} 条`,
            life: 5000
        });

        // 重新加载标记
        await loadFangMarkers();
    } catch (error) {
        console.error('测试地理编码失败:', error);
        toast.add({ severity: 'error', summary: '测试失败', detail: error.message, life: 5000 });
    } finally {
        isLoading.value = false;
    }
};

// 批量地理编码
const batchGeocoding = async () => {
    try {
        isLoading.value = true;
        toast.add({ severity: 'info', summary: '批量处理中', detail: '正在批量处理地理编码，请耐心等待...', life: 3000 });

        // 初始化地理编码服务
        await geocodingService.initialize(AMap);

        // 批量处理所有数据
        const result = await geocodingService.processAllMissingCoordinates((progress) => {
            toast.add({
                severity: 'info',
                summary: '处理进度',
                detail: `第 ${progress.current}/${progress.total} 批，已处理 ${progress.processed} 条，成功 ${progress.success} 条`,
                life: 2000
            });
        });

        toast.add({
            severity: 'success',
            summary: '批量处理完成',
            detail: `总共处理 ${result.total} 条数据，成功 ${result.success} 条，失败 ${result.failed} 条`,
            life: 8000
        });

        // 重新加载标记
        await loadFangMarkers();
    } catch (error) {
        console.error('批量地理编码失败:', error);
        toast.add({ severity: 'error', summary: '批量处理失败', detail: error.message, life: 5000 });
    } finally {
        isLoading.value = false;
    }
};

// 组件挂载
onMounted(async () => {
    try {
        isLoading.value = true;
        AMap = await mapService.initMap();
        map = await mapService.createMap('container');
        const mapInstance = await mapService.getMap();
        mapStore.fetchMapOptions(mapInstance);

        // 初始化搜索功能
        initSearch();

        // 首次加载房产标记
        await loadFangMarkers();
    } catch (error) {
        console.error('地图初始化失败:', error);
        toast.add({
            severity: 'error',
            summary: '错误',
            detail: '地图加载失败，请刷新页面重试',
            life: 5000
        });
    } finally {
        isLoading.value = false;
    }
});

// 组件卸载
onUnmounted(() => {
    clearFangMarkers();
});
</script>

<style lang="scss" scoped>
.map {
    height: calc(100vh - 7vh);
    background-color: beige;
    position: relative;
    overflow: hidden;
}

#container {
    height: 100%;
    width: 100%;
    position: relative;
    overflow: hidden;

    :deep(.amap-layers) {
        z-index: 0 !important;
    }

    .map-overlay {
        position: relative;
        z-index: 1021;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.loading-content {
    text-align: center;

    p {
        margin-top: 1rem;
        font-size: 1.1rem;
        color: #666;
    }
}

.zoom-display {
    margin-right: 1rem;
    padding: 0.5rem;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    font-weight: bold;
}

.fang-detail {
    padding: 1rem;
}

.fang-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;

    h2 {
        margin: 0;
        color: #333;
    }

    .fang-price {
        font-size: 1.5rem;
        font-weight: bold;
        color: #e74c3c;
    }
}

.fang-info {
    margin-bottom: 1.5rem;

    p {
        margin: 0.5rem 0;
        line-height: 1.6;
    }
}

.fang-actions {
    display: flex;
    gap: 1rem;
}
</style>
