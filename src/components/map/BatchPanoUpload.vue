<template>
    <div class="card">
        <Toast />
        <!-- maxFileSize="3000000" 这里是 3MB 的文件大小显示 -->
        <FileUpload name="demo[]" @upload="onTemplatedUpload($event)" :multiple="true" accept="image/*" :maxFileSize="10000000" @select="onSelectedFiles">
            <template #header="{ chooseCallback, uploadCallback, clearCallback, files }">
                <div class="flex flex-wrap justify-between items-center flex-1 gap-4">
                    <div class="flex gap-2">
                        <Button @click="chooseCallback()" icon="pi pi-images" rounded outlined severity="secondary"></Button>
                        <Button @click="uploadEvent(uploadCallback)" icon="pi pi-cloud-upload" rounded outlined severity="success" :disabled="!files || files.length === 0"></Button>
                        <Button @click="clearCallback()" icon="pi pi-times" rounded outlined severity="danger" :disabled="!files || files.length === 0"></Button>
                    </div>
                    <ProgressBar :value="totalSizePercent" :showValue="false" class="md:w-20rem h-1 w-full md:ml-auto">
                        <span class="whitespace-nowrap">{{ totalSize }}B / 1Mb</span>
                    </ProgressBar>
                </div>
            </template>
            <template #content="{ files, uploadedFiles, removeUploadedFileCallback, removeFileCallback }">
                <div class="flex flex-col gap-8 pt-4">
                    <!-- <div v-if="files.length > 0">
                        <h5>Pending</h5>
                        <div class="flex flex-wrap gap-4">
                            <div v-for="(file, index) of files" :key="file.name + file.type + file.size" class="file-card p-8 rounded-border flex flex-col border border-surface items-center gap-4">
                                <div class="image-container">
                                    <img role="presentation" :alt="file.name" :src="file.objectURL" width="100" height="50" />
                                    <Badge value="Pending" severity="warn" class="status-badge" />
                                    <Button icon="pi pi-times" @click="onRemoveTemplatingFile(file, removeFileCallback, index)" outlined rounded severity="danger" class="delete-button" />
                                </div>
                                <span class="font-semibold text-ellipsis max-w-60 whitespace-nowrap overflow-hidden">{{ file.name }}</span>
                                <div>{{ formatSize(file.size.toFixed(2)) }}</div>
                            </div>
                        </div>
                    </div> -->
                    <div v-if="files.length > 0">
                        <h5>Pending</h5>
                        <div class="flex flex-wrap gap-4">
                            <div v-for="(file, index) of files" :key="file.name + file.type + file.size" class="p-8 rounded-border flex flex-col border border-surface items-center gap-4">
                                <div>
                                    <img role="presentation" :alt="file.name" :src="file.objectURL" width="100" height="50" />
                                </div>
                                <span class="font-semibold text-ellipsis max-w-60 whitespace-nowrap overflow-hidden">{{ file.name }}</span>
                                <div>{{ formatSize(file.size.toFixed(2)) }}</div>
                                <Badge value="Pending" severity="warn" />
                                <Button icon="pi pi-times" @click="onRemoveTemplatingFile(file, removeFileCallback, index)" outlined rounded severity="danger" />
                            </div>
                        </div>
                    </div>

                    <div v-if="uploadedFiles.length > 0">
                        <h5>Completed</h5>
                        <div class="flex flex-wrap gap-4">
                            <div v-for="(file, index) of uploadedFiles" :key="file.name + file.type + file.size" class="p-8 rounded-border flex flex-col border border-surface items-center gap-4">
                                <div>
                                    <img role="presentation" :alt="file.name" :src="file.objectURL" width="100" height="50" />
                                </div>
                                <span class="font-semibold text-ellipsis max-w-60 whitespace-nowrap overflow-hidden">{{ file.name }}</span>
                                <div>{{ formatSize(file.size) }}</div>
                                <Badge value="Completed" class="mt-4" severity="success" />
                                <Button icon="pi pi-times" @click="removeUploadedFileCallback(index)" outlined rounded severity="danger" />
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template #empty>
                <div class="flex items-center justify-center flex-col">
                    <i class="pi pi-cloud-upload !border-2 !rounded-full !p-8 !text-4xl !text-muted-color" />
                    <p class="mt-6 mb-0">Drag and drop files to here to upload.</p>
                </div>
            </template>
        </FileUpload>
    </div>
</template>

<script setup>
import { ref, toRaw } from 'vue';
import { usePrimeVue } from 'primevue/config';
import { useToast } from 'primevue/usetoast';
import { parse } from 'exifr';
import { wgs84togcj02 } from '@/utils/coordinate';
import { getGeoInfo, getGeoInfos } from '@/utils/mapTool';
import { usePhotoStore } from '@/stores/photoStore';

const $primevue = usePrimeVue();
const toast = useToast();

const totalSize = ref(0);
const totalSizePercent = ref(0);
const files = ref([]);
const exifResults = ref([]);
const emit = defineEmits(['saveBatch', 'upload', 'unlockSubmit']);
const pendingFormData = ref([]);
const readyFormData = ref([]);

const onRemoveTemplatingFile = (file, removeFileCallback, index) => {
    removeFileCallback(index);
    totalSize.value -= parseInt(formatSize(file.size));
    totalSizePercent.value = totalSize.value / 10;
};

const onClearTemplatingUpload = (clear) => {
    clear();
    totalSize.value = 0;
    totalSizePercent.value = 0;
};

// 文件选择后，解析exif信息
const onSelectedFiles = async (event) => {
    files.value = [...event.files];
    const exifQueue = files.value.map(async (file) => {
        // NO.1 解析文件的 exif 数据
        console.log('开始解析文件 file:', file, typeof file);
        const exif = await parse(file);
        console.log('解析后的EXIF数据 exif:', exif);
        // 检查EXIF数据中是否包含有效的经纬度信息
        if (!exif?.longitude || !exif?.latitude || isNaN(exif.longitude) || isNaN(exif.latitude) || exif.longitude < -180 || exif.longitude > 180 || exif.latitude < -90 || exif.latitude > 90) {
            console.error('图片未包含有效GPS信息:', file.name);
            // 使用Toast显示具体哪个文件没有GPS信息
            toast.add({ severity: 'error', summary: '上传失败', detail: `文件 ${file.name} 未包含有效GPS信息`, life: 5000 });
            console.error(`文件 ${file.name} 未包含有效GPS信息`);
            throw new Error('图片未包含有效GPS信息');
        }

        const [gcjLng, gcjLat] = wgs84togcj02(exif.longitude, exif.latitude);

        // 检查坐标转换结果是否有效
        if (isNaN(gcjLng) || isNaN(gcjLat)) {
            console.error('坐标转换失败:', { original: [exif.longitude, exif.latitude], converted: [gcjLng, gcjLat] });
            // 使用Toast显示坐标转换失败的文件
            toast.add({ severity: 'error', summary: '上传失败', detail: `文件 ${file.name} 坐标转换失败`, life: 5000 });
            throw new Error('坐标转换失败');
        }
        const exifData = {
            name: file.name,
            size: file.size,
            type: file.type,
            gps: { lng: exif.longitude, lat: exif.latitude },
            gcj: { lng: gcjLng, lat: gcjLat },
            timestamp: exif.DateTimeOriginal,
            altitude: exif.GPSAltitude
        };
        return exifData;
    });
    exifResults.value = await Promise.all(exifQueue);
    console.log('解析后的 exifResults :', exifResults.value);
    totalSize.value = files.value.reduce((sum, file) => sum + file.size, 0);
    console.log('totalSize.value:', totalSize.value);
    console.log('typeof totalSize.value:', typeof totalSize.value);
    toast.add({
        severity: 'info',
        summary: '照片解析成功',
        detail: exifResults.value,
        life: 5000
    });
};

// 这一步仅仅是上传图片到服务器，获得返回的文件存储地址和缩略图地址，同时把每个文件的 pendingFormData 数据构建好，统一交给解析地址函数处理后，存入 readyFormData 数组中
const uploadEvent = async (callback) => {
    try {
        const processingQueue = files.value.map(async (file) => {
            // NO.2 从Amap 获取其他信息如：city full_address
            const exif = await parse(file);
            const [gcjLng, gcjLat] = wgs84togcj02(exif.longitude, exif.latitude);
            const exifData = {
                name: file.name,
                size: file.size,
                type: file.type,
                gps: { lng: exif.longitude, lat: exif.latitude },
                gcj: { lng: gcjLng, lat: gcjLat },
                timestamp: exif.DateTimeOriginal,
                altitude: exif.GPSAltitude
            };

            // NO.3 上传照片，调用 panoProcessor.js来获得photo_url 和 thumbnial_url 信息
            const formDataObj = new FormData();
            formDataObj.append('file', file);
            const metadataBlob = new Blob([JSON.stringify(exifData)], { type: 'application/json' });
            formDataObj.append('metadata', metadataBlob);

            // 发送请求到后端API
            const response = await fetch('http://localhost:3001/api/pano-processor', {
                method: 'POST',
                body: formDataObj
            });

            if (!response.ok) {
                throw new Error(`上传失败: ${response.statusText}`);
            }

            const result = await response.json();
            if (!result.success) {
                throw new Error(result.error || '上传失败');
            }

            // const geoInfo = await getGeoInfo(gcjLng, gcjLat);
            // console.log('geoInfo:', geoInfo);

            // NO.4 构建每个文件上传数据需所需信息 formData
            const formData = {
                name: file.name,
                gps: { lng: exif.longitude, lat: exif.latitude },
                gcj: { lng: gcjLng, lat: gcjLat },
                gps_timestamp: exif.DateTimeOriginal,
                altitude: exif.GPSAltitude,
                pano_url: result.data.pano_url,
                thumbnail_url: result.data.thumbnail_url,
                city: 'Unknown',
                full_address: 'Unknown'
                // city: geoInfo.city,
                // full_address: geoInfo.full_address
            };

            console.log('formData from uploadEvent sent by BatchFileUpload:', formData);
            pendingFormData.value.push(formData);
        });
        toast.add({
            severity: 'info',
            summary: '全景图上传 sb 成功',
            detail: pendingFormData.value,
            life: 7000
        });

        await Promise.all(processingQueue);

        // 先触发PrimeVue上传，这会调用 onTemplatedUpload
        totalSizePercent.value = totalSize.value / 10;
        let queryLnglats = [];
        pendingFormData.value.forEach((item) => {
            queryLnglats.push([toRaw(item.gcj.lng), toRaw(item.gcj.lat)]);
        });
        console.log('pendingFormData.value.lenth:', pendingFormData.value.length);
        console.log('queryLnglats.length:', queryLnglats.length);
        console.log('queryLnglats:', queryLnglats);

        if (queryLnglats.length > 0) {
            const geoInfos = await getGeoInfos(queryLnglats);

            // 使用 forEach 直接修改原对象
            pendingFormData.value.forEach((item, index) => {
                if (geoInfos[index]) {
                    item.city = geoInfos[index].city || item.city;
                    item.full_address = geoInfos[index].full_address || item.full_address;
                }
            });

            // 复制到 readyFormData
            readyFormData.value = [...pendingFormData.value];
        }

        console.log('readyFormData.value:', readyFormData.value);
        emit('unlockSubmit', readyFormData.value);
        callback();

        // 这些代码应该移到 onTemplatedUpload 中
        // uploadedFiles.value = files.value.map((file) => ({...}));
        // files.value = [];
    } catch (error) {
        toast.add({
            severity: 'error',
            summary: 'Processing Error',
            detail: error,
            life: 5000
        });
    }
};

// 修改 onTemplatedUpload 方法
const onTemplatedUpload = () => {
    // 调用 photoStore 上传
    // const photoStore = usePhotoStore();
    // await photoStore.addPhoto(readyFormData.value);

    // 上传完成后触发自定义事件
    // emit('unlockSubmit');
    // emit('saveBatch');
    // 清空数据
    // pendingFormData.value = [];
    // totalSize.value = 0;

    toast.add({ severity: 'info', summary: '上传成功', detail: `已上传 ${readyFormData.value.length} 个文件`, life: 3000 });
};

const formatSize = (bytes) => {
    const k = 1024;
    const dm = 3;
    const sizes = $primevue.config.locale.fileSizeTypes;

    if (bytes === 0) {
        return `0 ${sizes[0]}`;
    }

    const i = Math.floor(Math.log(bytes) / Math.log(k));
    const formattedSize = parseFloat((bytes / Math.pow(k, i)).toFixed(dm));

    return `${formattedSize} ${sizes[i]}`;
};
</script>

<style scoped lang="scss">
.card {
    background: var(--card-bg);
    border: var(--card-border);
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 1rem;
}
.file-card {
    .image-container {
        position: relative;
        width: 100px;
        height: 70px;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .status-badge {
            position: absolute;
            top: -0.5rem;
            right: -0.5rem;
            z-index: 1;
        }

        .delete-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 0.2s ease;
            background: rgba(0, 0, 0, 0.5);

            &:hover {
                background: rgba(0, 0, 0, 0.7);
            }
        }

        &:hover {
            .delete-button {
                opacity: 1;
            }
        }
    }
}
</style>
