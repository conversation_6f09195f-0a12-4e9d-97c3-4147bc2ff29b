<template>
    <div class="test-map">
        <Toast />
        <div id="test-container" class="map-container">
            <!-- 控制面板 -->
            <div class="control-panel">
                <h3>地理编码精度对比测试</h3>
                <p>对比REST API、JavaScript API和PlaceSearch三种方式的地理编码精度</p>

                <div class="button-group">
                    <Button label="开始对比测试" icon="pi pi-play" @click="startComparison" :disabled="isLoading" :loading="isLoading" />
                    <Button label="清除标记" icon="pi pi-trash" @click="clearAllMarkers" severity="secondary" />
                    <Button label="适应视野" icon="pi pi-eye" @click="fitView" severity="secondary" />
                </div>

                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color green"></div>
                        <span>REST API (绿色)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color red"></div>
                        <span>JavaScript API (红色)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color blue"></div>
                        <span>PlaceSearch (蓝色)</span>
                    </div>
                </div>

                <div v-if="statusMessage" class="status-message" :class="statusType">
                    {{ statusMessage }}
                </div>
            </div>
        </div>

        <!-- 结果面板 -->
        <div v-if="comparisonResults.length > 0" class="results-panel">
            <h4>对比结果</h4>
            <div v-for="result in comparisonResults" :key="result.id" class="result-item">
                <div class="result-header">{{ result.name }} (ID: {{ result.id }})</div>

                <div class="coordinate-row rest-api">
                    <span><strong>REST API:</strong> {{ result.name }}</span>
                    <span v-if="result.restAPI.success"> {{ result.restAPI.lng.toFixed(6) }}, {{ result.restAPI.lat.toFixed(6) }} </span>
                    <span v-else class="error">失败</span>
                </div>

                <div class="coordinate-row js-api">
                    <span><strong>JS API:</strong> {{ result.name }}</span>
                    <span v-if="result.jsAPI.success"> {{ result.jsAPI.lng.toFixed(6) }}, {{ result.jsAPI.lat.toFixed(6) }} </span>
                    <span v-else class="error">失败</span>
                </div>

                <div class="coordinate-row place-search">
                    <span><strong>PlaceSearch:</strong> {{ result.name }}</span>
                    <span v-if="result.placeSearch.success"> {{ result.placeSearch.lng.toFixed(6) }}, {{ result.placeSearch.lat.toFixed(6) }} </span>
                    <span v-else class="error">失败</span>
                </div>

                <div v-if="Object.keys(result.distances).length > 0" class="distance-info">
                    <strong>距离对比:</strong><br />
                    <span v-if="result.distances.restToJs !== undefined"> REST API ↔ JS API: {{ result.distances.restToJs }}米<br /> </span>
                    <span v-if="result.distances.restToPlace !== undefined"> REST API ↔ PlaceSearch: {{ result.distances.restToPlace }}米<br /> </span>
                    <span v-if="result.distances.jsToPlace !== undefined"> JS API ↔ PlaceSearch: {{ result.distances.jsToPlace }}米 </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useToast } from 'primevue/usetoast';
import Button from 'primevue/button';
import Toast from 'primevue/toast';
import mapService from '@/services/mapService';
import { supabase } from '@/services/supabase';

// 配置
const AMAP_REST_KEY = '7a5917e97482a3cb8b2c1631b7cd9971';

// 响应式数据
const toast = useToast();
const isLoading = ref(false);
const statusMessage = ref('');
const statusType = ref('info');
const comparisonResults = ref([]);

let map = null;
let AMap = null;
let geocoder = null;
let placeSearch = null;
let markers = [];

// 地区映射
const districtMap = {
    江夏: '江夏区',
    洪山: '洪山区',
    武昌: '武昌区',
    沌口: '汉南区',
    经济开发区: '汉南区',
    江汉: '江汉区',
    江岸: '江岸区',
    硚口: '硚口区',
    汉阳: '汉阳区',
    青山: '青山区',
    武汉: '武汉市',
    汉南: '汉南区',
    蔡甸: '蔡甸区',
    新洲: '新洲区',
    黄陂: '黄陂区',
    东西湖: '东西湖区'
};

// 初始化
onMounted(async () => {
    try {
        updateStatus('正在初始化地图...', 'info');

        // 使用mapService初始化地图
        AMap = await mapService.initMap();
        map = await mapService.createMap('test-container');

        // 初始化地理编码器
        geocoder = new AMap.Geocoder({
            city: '武汉市',
            radius: 5000
        });

        // 初始化PlaceSearch
        placeSearch = new AMap.PlaceSearch({
            city: '027',
            pageSize: 10
        });

        updateStatus('地图初始化完成，准备开始测试', 'success');
    } catch (error) {
        console.error('地图初始化失败:', error);
        updateStatus(`地图初始化失败: ${error.message}`, 'error');
        toast.add({
            severity: 'error',
            summary: '错误',
            detail: '地图初始化失败，请刷新页面重试',
            life: 5000
        });
    }
});

onUnmounted(() => {
    clearAllMarkers();
    // 销毁地图实例
    if (map) {
        mapService.destroy();
        map = null;
        AMap = null;
    }
});

// 更新状态
const updateStatus = (message, type = 'info') => {
    statusMessage.value = message;
    statusType.value = type;
};

// 构建地址
const buildAddress = (fangData) => {
    const district = districtMap[fangData.district] || fangData.district;
    if (district && fangData.location) {
        return `武汉市${district}${fangData.location}`;
    }
    return `武汉市${district || ''}`;
};

// REST API地理编码
const geocodeWithRestAPI = async (address) => {
    try {
        const url = `https://restapi.amap.com/v3/geocode/geo?key=${AMAP_REST_KEY}&address=${encodeURIComponent(address)}`;
        const response = await fetch(url);
        const data = await response.json();

        if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
            const location = data.geocodes[0].location.split(',');
            return {
                success: true,
                lng: parseFloat(location[0]),
                lat: parseFloat(location[1]),
                formatted_address: data.geocodes[0].formatted_address
            };
        }
        return { success: false, error: 'No results' };
    } catch (error) {
        return { success: false, error: error.message };
    }
};

// JavaScript API地理编码
const geocodeWithJSAPI = (address) => {
    return new Promise((resolve) => {
        geocoder.getLocation(address, (status, result) => {
            if (status === 'complete' && result.geocodes && result.geocodes.length > 0) {
                const location = result.geocodes[0].location;
                resolve({
                    success: true,
                    lng: location.lng,
                    lat: location.lat,
                    formatted_address: result.geocodes[0].formatted_address
                });
            } else {
                resolve({ success: false, error: status });
            }
        });
    });
};

// PlaceSearch搜索
const searchWithPlaceSearch = (name) => {
    return new Promise((resolve) => {
        placeSearch.search(name, (status, result) => {
            if (status === 'complete' && result.poiList && result.poiList.pois.length > 0) {
                const poi = result.poiList.pois[0];
                resolve({
                    success: true,
                    lng: poi.location.lng,
                    lat: poi.location.lat,
                    formatted_address: poi.address,
                    name: poi.name
                });
            } else {
                resolve({ success: false, error: status });
            }
        });
    });
};

// 计算距离
const calculateDistance = (point1, point2) => {
    const distance = AMap.GeometryUtil.distance([point1.lng, point1.lat], [point2.lng, point2.lat]);
    return Math.round(distance);
};

// 添加标记
const addMarker = (position, color, title, info) => {
    const marker = new AMap.Marker({
        position: [position.lng, position.lat],
        title: title,
        content: `<div style="background: ${color}; color: white; padding: 5px 8px; border-radius: 50%; font-size: 12px; font-weight: bold;">${info}</div>`
    });

    marker.on('click', function () {
        const infoWindow = new AMap.InfoWindow({
            content: `
                <div style="padding: 10px;">
                    <h4 style="margin: 0 0 10px 0;">${title}</h4>
                    <p style="margin: 5px 0;"><strong>方式:</strong> ${info}</p>
                    <p style="margin: 5px 0;"><strong>经度:</strong> ${position.lng}</p>
                    <p style="margin: 5px 0;"><strong>纬度:</strong> ${position.lat}</p>
                    <p style="margin: 5px 0;"><strong>地址:</strong> ${position.formatted_address || 'N/A'}</p>
                </div>
            `
        });
        infoWindow.open(map, marker.getPosition());
    });

    markers.push(marker);
    map.add(marker);
    return marker;
};

// 开始对比测试
const startComparison = async () => {
    try {
        isLoading.value = true;
        updateStatus('正在获取随机房产数据...', 'info');

        // 获取随机5条数据
        const { data: fangData, error } = await supabase.from('fang_data').select('id, name, district, location').limit(5);

        if (error) throw error;
        if (!fangData || fangData.length === 0) {
            updateStatus('没有找到房产数据', 'error');
            return;
        }

        // 清除之前的结果
        clearAllMarkers();
        comparisonResults.value = [];

        // 对每条数据进行三种方式的地理编码
        for (let i = 0; i < fangData.length; i++) {
            const item = fangData[i];
            const address = buildAddress(item);

            updateStatus(`正在处理 ${i + 1}/${fangData.length}: ${item.name}`, 'info');

            // 三种方式并行处理 - 使用相同的搜索策略进行公平对比
            const [restResult, jsResult, placeResult] = await Promise.all([
                geocodeWithRestAPI(item.name), // 改为使用小区名称
                geocodeWithJSAPI(item.name), // 改为使用小区名称
                searchWithPlaceSearch(item.name)
            ]);

            const result = {
                id: item.id,
                name: item.name,
                address: address,
                restAPI: restResult,
                jsAPI: jsResult,
                placeSearch: placeResult,
                distances: {}
            };

            // 添加标记
            if (restResult.success) {
                addMarker(restResult, '#52c41a', item.name, 'REST');
            }
            if (jsResult.success) {
                addMarker(jsResult, '#ff4d4f', item.name, 'JS');
            }
            if (placeResult.success) {
                addMarker(placeResult, '#1890ff', item.name, 'POI');
            }

            // 计算距离
            if (restResult.success && jsResult.success) {
                result.distances.restToJs = calculateDistance(restResult, jsResult);
            }
            if (restResult.success && placeResult.success) {
                result.distances.restToPlace = calculateDistance(restResult, placeResult);
            }
            if (jsResult.success && placeResult.success) {
                result.distances.jsToPlace = calculateDistance(jsResult, placeResult);
            }

            comparisonResults.value.push(result);

            // 延迟避免请求过频
            await new Promise((resolve) => setTimeout(resolve, 500));
        }

        // 适应视野
        fitView();
        updateStatus('对比测试完成！', 'success');

        toast.add({
            severity: 'success',
            summary: '测试完成',
            detail: `成功测试了 ${fangData.length} 个地点`,
            life: 3000
        });
    } catch (error) {
        console.error('对比测试失败:', error);
        updateStatus(`测试失败: ${error.message}`, 'error');
        toast.add({
            severity: 'error',
            summary: '测试失败',
            detail: error.message,
            life: 5000
        });
    } finally {
        isLoading.value = false;
    }
};

// 清除所有标记
const clearAllMarkers = () => {
    if (markers.length > 0) {
        map.remove(markers);
        markers = [];
    }
    comparisonResults.value = [];
    updateStatus('标记已清除', 'info');
};

// 适应视野
const fitView = () => {
    if (markers.length > 0) {
        map.setFitView(markers);
        updateStatus('视野已调整', 'info');
    } else {
        updateStatus('没有标记可以适应', 'error');
    }
};
</script>

<style scoped>
.test-map {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.map-container {
    flex: 1;
    position: relative;
}

#test-container {
    width: 100%;
    height: 100%;
}

.control-panel {
    position: absolute;
    top: 40px;
    left: 20px;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    min-width: 300px;
    max-width: 400px;
}

.button-group {
    display: flex;
    gap: 10px;
    margin: 15px 0;
    flex-wrap: wrap;
}

.legend {
    display: flex;
    gap: 15px;
    margin: 15px 0;
    font-size: 12px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.legend-color.green {
    background: #52c41a;
}
.legend-color.red {
    background: #ff4d4f;
}
.legend-color.blue {
    background: #1890ff;
}

.status-message {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
    font-size: 14px;
}

.status-message.success {
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    color: #52c41a;
}

.status-message.error {
    background: #fff2f0;
    border: 1px solid #ffccc7;
    color: #ff4d4f;
}

.status-message.info {
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    color: #1890ff;
}

.results-panel {
    max-height: 40vh;
    overflow-y: auto;
    padding: 20px;
    background: #f5f5f5;
}

.result-item {
    background: white;
    margin: 10px 0;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.result-header {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 10px;
    color: #333;
}

.coordinate-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0;
    padding: 8px;
    border-radius: 4px;
}

.rest-api {
    background: #f6ffed;
    border-left: 4px solid #52c41a;
}
.js-api {
    background: #fff2f0;
    border-left: 4px solid #ff4d4f;
}
.place-search {
    background: #e6f7ff;
    border-left: 4px solid #1890ff;
}

.error {
    color: #ff4d4f;
    font-weight: bold;
}

.distance-info {
    margin-top: 10px;
    padding: 10px;
    background: #fafafa;
    border-radius: 4px;
    font-size: 12px;
}
</style>
