<template>
    <div class="pano-info-input">
        <Toast />
        <form @submit.prevent="handleSubmit">
            <Fuild>
                <div class="form-group">
                    <div class="grid grid-cols-8 gap-4">
                        <div class="col-span-3">
                            <label for="name">全景图名称 <span class="required">*</span></label>
                            <InputText id="name" v-model="formData.name" :class="{ 'p-invalid': v$.name.$invalid && v$.name.$dirty }" />
                            <small v-if="v$.name.$invalid && v$.name.$dirty" class="p-error">{{ v$.name.$errors[0].$message }}</small>
                        </div>
                        <div class="col-span-5">
                            <div class="label-group">
                                <label for="tag">标签</label>
                                <small class="helper-text">输入标签后按回车或空格键添加</small>
                            </div>
                            <Chips v-model="formData.tag" separator=" " />
                        </div>
                    </div>
                </div>
            </Fuild>

            <div class="form-group">
                <label>全景预览</label>
                <div class="photo-preview">
                    <img :src="previewPano" alt="全景预览" class="preview-image" />
                    <div class="preview-actions">
                        <Button icon="pi pi-eye" class="p-button-rounded p-button-info p-button-sm" @click="handleViewImage" />
                        <Button icon="pi pi-refresh" class="p-button-rounded p-button-warning p-button-sm" @click="showReplaceDialog = true" />
                        <Button icon="pi pi-map-marker" rounded raised :size="iconSize" @click="pointCustomGcj" />
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>全景信息</label>
                <div class="location-info">
                    <div class="coordinates">
                        <div class="coordinates-row">
                            <span>城市: {{ formData.city }} | </span>
                            <i class="pi pi-map-marker">_</i>
                            <span>{{ props.marker.value.full_address }}</span>
                            <!-- </div> -->
                        </div>
                        <div class="coordinates-row">
                            <span>GPS坐标: {{ formatCoordinates(formData.gps) }}</span>
                            <span>| GCJ坐标: {{ formatCoordinates(formData.gcj) }}</span>
                        </div>
                        <div class="coordinates-row">
                            <span>海拔高度: {{ formData.altitude }} 米 </span>
                            <span> | 创建时间: {{ formatDate(formData.create_time) }}</span>
                        </div>
                        <span>拍摄时间: <Calendar id="timestamp" v-model="formData.gps_timestamp" showTime hourFormat="24" /></span>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>新全景信息</label>
                <div class="meta-info">
                    <div class="meta-item">
                        <span class="meta-label">文件名:</span>
                        <span class="meta-value">{{ newPanoData.name }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">文件大小:</span>
                        <span class="meta-value">{{ formatFileSize(newPanoData.size) }} </span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">GPS:</span>
                        <span class="meta-value">{{ newPanoData.gps.lng.toFixed(6) }}, {{ newPanoData.gps.lat.toFixed(6) }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">GCJ:</span>
                        <span class="meta-value">{{ newPanoData.gcj.lng.toFixed(6) }}, {{ newPanoData.gcj.lat.toFixed(6) }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">新照片位置:</span>
                        <span class="meta-value">{{ newPanoData.full_address || '' }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">拍摄时间:</span>
                        <span class="meta-value">{{ newPanoData.timestamp }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">海拔:</span>
                        <span class="meta-value">{{ newPanoData.altitude }} 米</span>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <Button type="submit" label="保存" icon="pi pi-check" class="p-button-success" :loading="loading" />
                <Button type="button" label="取消" icon="pi pi-times" class="p-button-secondary" @click="handleCancel" :disabled="loading" />
            </div>
            <!-- 上传照片对话框 -->
            <Dialog v-model:visible="showReplaceDialog" header="上传全景图" :modal="true" :closable="false">
                <div class="replace-dialog-content">
                    <FileUpload name="photo-file" @upload="handleFileUpload" accept="image/*" :maxFileSize="10000000" :multiple="false" @select="onPanoSelect" :uploadButtonProps :fileLimit="1">
                        <template #empty>
                            <span>拖动文件到此处上传。</span>
                        </template>
                    </FileUpload>
                    <div v-if="newPanoFile" class="file-preview">
                        <img :src="newPanoFile.objectURL" alt="新全景图预览" class="preview-image" />
                    </div>
                </div>
                <template #footer>
                    <Button label="上传" icon="pi pi-check" class="p-button-success" @click="handleUpLoadPano" :disabled="!newPanoFile || replacingFile" :loading="replacingFile" />
                    <Button label="取消" icon="pi pi-times" class="p-button-secondary" @click="cancelUploadFile" :disabled="replacingFile" />
                </template>
            </Dialog>

            <!-- 删除确认对话框 -->
            <Dialog v-model:visible="showDeleteDialog" header="确认删除" :modal="true" :closable="true">
                <div class="delete-dialog-content">
                    <p>确定要删除此照片标记吗？此操作不可撤销。</p>
                </div>
                <template #footer>
                    <Button label="确认删除" icon="pi pi-trash" class="p-button-danger" @click="handleDelete" :loading="deleting" />
                    <Button label="取消" icon="pi pi-times" class="p-button-secondary" @click="showDeleteDialog = false" :disabled="deleting" />
                </template>
            </Dialog>
        </form>
    </div>
</template>

<script setup>
import InputText from 'primevue/inputtext';
import Calendar from 'primevue/calendar';
import FileUpload from 'primevue/fileupload';
import Chips from 'primevue/chips';
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';
import Badge from 'primevue/badge';
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useToast } from 'primevue/usetoast';
import { parse } from 'exifr';
import { useVuelidate } from '@vuelidate/core';
import { required, maxLength } from '@vuelidate/validators';
import { gcj02towgs84, wgs84togcj02 } from '@/utils/coordinate';
import { getGeoInfo, setMarker, removeMarker } from '@/utils/mapTool.js';
import { useMapStore } from '@/stores/mapStore';
const props = defineProps({
    marker: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['submit', 'cancel']);

const toast = useToast();
const mapStore = useMapStore();
const point_gcj = reactive({ lng: 0, lat: 0 });
const loading = ref(false);
const replacingFile = ref(false);
const showReplaceDialog = ref(false);
const newPanoFile = ref(null);
const defaultPano = 'empty-photo.webp';
const previewPano = ref(defaultPano);

// 初始化表单数据
const formData = reactive({
    name: props.marker.value.name,
    tag: [],
    collection_id: '',
    link: null,
    city: props.marker.value.city || null,
    full_address: props.marker.value.full_address || null,
    gps: null,
    gjc: null,
    custom_gcj: props.marker.value.custom_gcj || null,
    altitude: null,
    gps_timestamp: '',
    pano_url: defaultPano || null,
    thumbnail_url: props.marker.value.thumbnail_url || null,
    is_custom: false,
    config_json: null,
    config_file_url: null,
    type: 'equirectangular'
});
// 表单验证规则
const rules = {
    name: { required, maxLength: maxLength(20) },
    panoFile: { required }
};
const v$ = useVuelidate(rules, formData);
// 新照片数据
const newPanoData = reactive({
    name: '未上传图片',
    size: 0,
    gps: { lng: 0, lat: 0 },
    gcj: { lng: 0, lat: 0 },
    timestamp: 0,
    altitude: 0,
    full_address: '',
    city: ''
});
// 构建完整的GPS数据
let customGCJtogps = { lng: props.marker.value.custom_gcj.lng, lat: props.marker.value.custom_gcj.lat };
// 使用坐标转换工具进行转换（如果有）
if (gcj02towgs84) {
    try {
        const converted = gcj02towgs84(customGCJtogps.lng, customGCJtogps.lat);
        customGCJtogps = { lng: converted[0], lat: converted[1] };
    } catch (e) {
        console.warn('坐标转换失败:', e);
    }
}

onMounted(() => {
    if (props.marker) {
        return;
    }
});
//=======================================================
// 更新预览图
watch(
    () => newPanoFile.value,
    (newValue) => {
        if (newValue && newValue.objectURL) {
            previewPano.value = newValue.objectURL;
        }
    }
);
// 更新新全景信息
watch(
    () => formData.pano_url,
    (newValue) => {
        if (newValue) {
            formData.name = newPanoData.name || '';
            formData.gps = newPanoData.gps || '';
            formData.gcj = newPanoData.gcj || '';
            formData.altitude = newPanoData.altitude || '';
            formData.full_address = newPanoData.full_address || '';
            formData.city = newPanoData.city || '';
            formData.gps_timestamp = newPanoData.timestamp || '';
            console.log('formData.name : ', formData.name, formData);
        }
    },
    { deep: true }
);
// 添加 watch 监听 照片是否被解析。注意📢：此时还没有上传图片，故而新全景信息不能传递给 formData
watch(
    () => newPanoFile.value,
    (newPano) => {
        if (newPano) {
            formData.panoFile = newPano || '';
            formData.name = newPanoData.name || '';
            console.log('formData_newPano: ', formData.panoFile);
        }
    },
    { deep: true }
);
//=======================================================

function cancelUploadFile() {
    newPanoFile.value = null;
    showReplaceDialog.value = false;
}

//=======================================================
// 格式化坐标
const formatCoordinates = (gps) => {
    if (!gps) return '未知';
    if (typeof gps.lat === 'string') {
        return `${parseFloat(gps.lng).toFixed(6) || 0}, ${parseFloat(gps.lat).toFixed(6) || 0}`;
    }
    return `${gps.lng?.toFixed(6) || 0}, ${gps.lat?.toFixed(6) || 0}`;
};

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '未知时间';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// 格式化文件大小
const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 查看原图
const handleViewImage = () => {
    window.open(formData.pano_url, '_blank');
};

// 处理文件上传
const handleFileUpload = (event) => {
    console.log('文件上传事件:', event);
    const file = event.files[0];
    if (file) {
        newPanoFile.value = file;
    }
    console.log('handleFileUpload->:', newPanoFile.value);
};

async function onPanoSelect(event) {
    console.log('onPanoSelect:', event);
    newPanoFile.value = event.files[0];
    console.log('newPanoFile.value:', newPanoFile.value);
    const exif = await parse(newPanoFile.value);
    console.log('EXIF data:', exif); // 调试用，查看完整的 EXIF 数据结构
    newPanoData.name = newPanoFile.value.name;
    newPanoData.size = newPanoFile.value.size;
    newPanoData.gps = { lng: exif.longitude, lat: exif.latitude };
    newPanoData.gcj = { lng: wgs84togcj02(exif.longitude, exif.latitude)[0], lat: wgs84togcj02(exif.longitude, exif.latitude)[1] };
    newPanoData.timestamp = exif.DateTimeOriginal;
    newPanoData.altitude = exif.GPSAltitude;
    const geoInfoData = await getGeoInfo(exif.longitude, exif.latitude);
    newPanoData.full_address = geoInfoData.full_address;
    newPanoData.city = geoInfoData.city;
    console.log('newPanoData:', newPanoData);
    console.log('formData', formData);
}

// 确认上传照片，并得到返回的原图 url 和缩略图 url，存入 formData 中
const handleUpLoadPano = async () => {
    if (!newPanoFile.value) return;
    replacingFile.value = true;
    try {
        // 构建FormData对象 - 重命名为formDataObj避免变量遮蔽
        const formDataObj = new FormData();
        formDataObj.append('file', newPanoFile.value);

        // 构建元数据
        const metadata = {
            name: newPanoData.name,
            pano_url: '',
            thumbnail_url: '',
            gcj: newPanoData.gcj,
            custom_gcj: formData.custom_gcj || newPanoData.gcj
        };

        // 添加元数据到 formDataObj
        const metadataBlob = new Blob([JSON.stringify(metadata)], { type: 'application/json' });
        formDataObj.append('metadata', metadataBlob);

        // 发送请求到后端API
        const response = await fetch('http://localhost:3001/api/pano-processor', {
            method: 'POST',
            body: formDataObj
        });

        if (!response.ok) {
            throw new Error(`上传失败: ${response.statusText}`);
        }

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.error || '上传失败');
        }

        // 更新组件级别的formData对象
        formData.pano_url = result.data.pano_url;
        formData.thumbnail_url = result.data.thumbnail_url;

        // 手动触发数据更新
        formData.name = newPanoData.name || '';
        formData.gps = newPanoData.gps || '';
        formData.gcj = newPanoData.gcj || '';
        formData.altitude = newPanoData.altitude || '';
        formData.full_address = newPanoData.full_address || '';
        formData.city = newPanoData.city || '';
        formData.gps_timestamp = newPanoData.timestamp || '';

        // 关闭对话框并清理
        showReplaceDialog.value = false;
        newPanoFile.value = null;

        toast.add({ severity: 'success', summary: '成功', detail: '照片已成功替换', life: 3000 });
    } catch (error) {
        console.error('替换照片失败:', error);
        toast.add({ severity: 'error', summary: '错误', detail: '替换照片失败: ' + error.message, life: 3000 });
    } finally {
        replacingFile.value = false;
    }
};

// 提交表单数据
const handleSubmit = async () => {
    try {
        loading.value = true;
        // 将标签数组转换为逗号分隔的字符串
        const tagString = Array.isArray(formData.tag) ? formData.tag.join(',') : typeof formData.tag === 'string' ? formData.tag : '';

        // 构建提交数据
        const submitData = reactive({
            name: newPanoData.name,
            tag: tagString,
            collection_id: props.marker.value.collection_id ? props.marker.value.collection_id : '', // 确保 collection_id 存在
            link: formData.link,
            city: newPanoData.city,
            full_address: newPanoData.full_address,
            gps: newPanoData.gps ? newPanoData.gps : customGCJtogps,
            gcj: newPanoData.gcj ? newPanoData.gcj : { lng: 0, lat: 0 },
            custom_gcj: formData.custom_gcj ? formData.custom_gcj : { lng: 0, lat: 0 },
            altitude: newPanoData.altitude || 0,
            gps_timestamp: newPanoData.timestamp || 0,
            pano_url: formData.pano_url,
            thumbnail_url: formData.thumbnail_url,
            is_custom: false,
            type: 'equirectangular',
            config_json: null,
            config_file_url: null
        });

        console.log('submitData: ', submitData);
        emit('submit', submitData);
    } catch (error) {
        console.error('提交表单失败:', error);
        toast.add({
            severity: 'error',
            summary: '提交失败',
            detail: '请检查表单填写是否正确',
            life: 3000
        });
    } finally {
        loading.value = false;
    }
};

//=======================================================
// 取消
const handleCancel = () => {
    emit('cancel');
};
</script>

<style lang="scss" scoped>
.photo-info-input {
    padding: 10px;
    .label-group {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .form-group {
        margin-bottom: 15px;

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;

            .required {
                color: red;
            }
        }

        .helper-text {
            display: block;
            color: #666;
            font-size: 0.8rem;
            margin-top: 3px;
        }
    }

    .file-upload-container {
        .file-preview {
            margin-top: 10px;
            display: flex;
            align-items: center;

            .preview-image {
                width: 80px;
                height: 80px;
                object-fit: cover;
                border-radius: 4px;
            }

            .file-info {
                margin-left: 10px;
                display: flex;
                flex-direction: column;

                span {
                    &:first-child {
                        font-weight: 500;
                    }

                    &:last-child {
                        color: #666;
                        font-size: 0.8rem;
                    }
                }
            }
        }
    }

    .location-info {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;

        .coordinates {
            display: flex;
            gap: 15px;
            margin-bottom: 5px;

            span {
                font-family: monospace;
            }
        }

        .address {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #666;

            i {
                color: #ff4d4f;
            }
        }
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
    }

    .photo-preview {
        position: relative;
        margin-bottom: 10px;

        .preview-image {
            width: 40%;
            max-height: 200px;
            object-fit: cover;
            border-radius: 4px;
        }

        .preview-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
        }
    }

    :deep(.p-inputtext),
    :deep(.p-textarea),
    :deep(.p-calendar),
    :deep(.p-chips) {
        width: 100%;
    }
}

.photo-preview-container {
    margin-top: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    background-color: #f9fafb;

    .photo-preview-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;

        .photo-preview-image {
            max-width: 100%;
            height: auto;
            max-height: 300px;
            object-fit: contain;
            border-radius: 4px;
        }

        .photo-preview-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            text-align: center;

            span {
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}
</style>
