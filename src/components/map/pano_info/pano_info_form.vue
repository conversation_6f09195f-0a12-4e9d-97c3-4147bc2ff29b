<template>
    <div class="pano-info-form">
        <form @submit.prevent="handleSubmit">
            <div class="form-group">
                <label for="name">标记名称 <span class="required">*</span></label>
                <InputText id="name" v-model="formData.name" :class="{ 'p-invalid': v$.name.$invalid && v$.name.$dirty }" />
                <small v-if="v$.name.$invalid && v$.name.$dirty" class="p-error">{{ v$.name.$errors[0].$message }}</small>
            </div>

            <div class="form-group">
                <label for="description">描述</label>
                <Textarea id="description" v-model="formData.description" rows="3" autoResize />
            </div>

            <div class="form-group">
                <label>全景图</label>
                <div class="pano-preview">
                    <img :src="formData.thumbnail_url || formData.pano_url" alt="全景图预览" class="preview-image" />
                    <div class="preview-actions">
                        <Button icon="pi pi-eye" class="p-button-rounded p-button-info p-button-sm" @click="handleViewPano" />
                        <Button icon="pi pi-refresh" class="p-button-rounded p-button-warning p-button-sm" @click="showReplaceDialog = true" />
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>位置信息</label>
                <div class="location-info">
                    <div class="coordinates">
                        <span>经度: {{ formData.lng.toFixed(6) }}</span>
                        <span>纬度: {{ formData.lat.toFixed(6) }}</span>
                    </div>
                    <div v-if="formData.address" class="address">
                        <i class="pi pi-map-marker"></i>
                        <span>{{ formData.address }}</span>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="tags">标签</label>
                <Chips v-model="formData.tags" separator="," />
                <small class="helper-text">输入标签后按回车或逗号添加</small>
            </div>

            <div class="form-group">
                <label>创建信息</label>
                <div class="meta-info">
                    <div class="meta-item">
                        <span class="meta-label">创建时间:</span>
                        <span class="meta-value">{{ formatDate(formData.created_at) }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">最后更新:</span>
                        <span class="meta-value">{{ formatDate(formData.updated_at) }}</span>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <Button type="submit" label="更新" icon="pi pi-check" class="p-button-success" :loading="loading" />
                <Button type="button" label="删除" icon="pi pi-trash" class="p-button-danger" @click="confirmDelete" :disabled="loading" />
                <Button type="button" label="返回" icon="pi pi-arrow-left" class="p-button-secondary" @click="handleBackToList" :disabled="loading" />
            </div>
        </form>

        <!-- 替换全景图对话框 -->
        <Dialog v-model:visible="showReplaceDialog" header="替换全景图" :modal="true" :closable="true">
            <div class="replace-dialog-content">
                <FileUpload mode="basic" name="pano-file" :customUpload="true" @uploader="handleFileUpload" accept="image/*" :maxFileSize="20000000" />
                <div v-if="newPanoFile" class="file-preview">
                    <img :src="newPanoPreviewUrl" alt="新全景图预览" class="preview-image" />
                    <div class="file-info">
                        <span>{{ newPanoFile.name }}</span>
                        <span>{{ formatFileSize(newPanoFile.size) }}</span>
                    </div>
                </div>
            </div>
            <template #footer>
                <Button label="确认替换" icon="pi pi-check" class="p-button-success" @click="handleReplaceConfirm" :disabled="!newPanoFile || replacingFile" :loading="replacingFile" />
                <Button label="取消" icon="pi pi-times" class="p-button-secondary" @click="showReplaceDialog = false" :disabled="replacingFile" />
            </template>
        </Dialog>

        <!-- 删除确认对话框 -->
        <Dialog v-model:visible="showDeleteDialog" header="确认删除" :modal="true" :closable="true">
            <div class="delete-dialog-content">
                <p>确定要删除此全景标记吗？此操作不可撤销。</p>
            </div>
            <template #footer>
                <Button label="确认删除" icon="pi pi-trash" class="p-button-danger" @click="handleDelete" :loading="deleting" />
                <Button label="取消" icon="pi pi-times" class="p-button-secondary" @click="showDeleteDialog = false" :disabled="deleting" />
            </template>
        </Dialog>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useVuelidate } from '@vuelidate/core';
import { required, maxLength } from '@vuelidate/validators';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import FileUpload from 'primevue/fileupload';
import Chips from 'primevue/chips';
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';

const props = defineProps({
    marker: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['update', 'delete', 'cancel', 'back-to-list']);

const loading = ref(false);
const deleting = ref(false);
const replacingFile = ref(false);
const showReplaceDialog = ref(false);
const showDeleteDialog = ref(false);
const newPanoFile = ref(null);

// 表单数据
const formData = reactive({
    id: '',
    name: '',
    description: '',
    pano_url: '',
    thumbnail_url: '',
    tags: [],
    lng: 0,
    lat: 0,
    address: '',
    created_at: '',
    updated_at: ''
});

// 表单验证规则
const rules = {
    name: { required, maxLength: maxLength(50) }
};

const v$ = useVuelidate(rules, formData);

// 新全景图预览URL
const newPanoPreviewUrl = computed(() => {
    if (newPanoFile.value) {
        return URL.createObjectURL(newPanoFile.value);
    }
    return '';
});

// 初始化表单数据
onMounted(() => {
    if (props.marker) {
        formData.id = props.marker.id;
        formData.name = props.marker.name || '';
        formData.description = props.marker.description || '';
        formData.pano_url = props.marker.pano_url || '';
        formData.thumbnail_url = props.marker.thumbnail_url || '';
        formData.tags = props.marker.tags || [];
        formData.lng = props.marker.lng || 0;
        formData.lat = props.marker.lat || 0;
        formData.address = props.marker.address || '';
        formData.created_at = props.marker.created_at || '';
        formData.updated_at = props.marker.updated_at || '';
    }
});

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '未知时间';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// 格式化文件大小
const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 查看全景图
const handleViewPano = () => {
    // 这里可以添加查看全景图的逻辑
    // 例如打开一个新窗口或者显示全屏预览
    window.open(formData.img_url, '_blank');
    console.log(formData.img_url);
};

// 处理文件上传
const handleFileUpload = (event) => {
    const file = event.files[0];
    if (file) {
        newPanoFile.value = file;
    }
};

// 确认替换全景图
const handleReplaceConfirm = async () => {
    if (!newPanoFile.value) return;

    replacingFile.value = true;

    try {
        // 这里可以添加替换全景图的逻辑
        // 例如上传新文件到服务器，然后更新数据库记录

        // 模拟上传过程
        await new Promise((resolve) => setTimeout(resolve, 1500));

        // 更新表单数据
        formData.pano_url = URL.createObjectURL(newPanoFile.value);
        formData.thumbnail_url = URL.createObjectURL(newPanoFile.value);

        // 关闭对话框
        showReplaceDialog.value = false;
        newPanoFile.value = null;
    } catch (error) {
        console.error('替换全景图失败:', error);
    } finally {
        replacingFile.value = false;
    }
};

// 提交表单
const handleSubmit = async () => {
    const isValid = await v$.value.$validate();
    if (!isValid) return;

    loading.value = true;

    try {
        // 构建提交数据
        const submitData = {
            id: formData.id,
            name: formData.name,
            description: formData.description,
            tags: formData.tags
        };

        emit('update', submitData);
    } catch (error) {
        console.error('更新标记失败:', error);
    } finally {
        loading.value = false;
    }
};

// 确认删除
const confirmDelete = () => {
    showDeleteDialog.value = true;
};

// 删除标记
const handleDelete = async () => {
    deleting.value = true;

    try {
        emit('delete', formData.id);
    } catch (error) {
        console.error('删除标记失败:', error);
    } finally {
        deleting.value = false;
        showDeleteDialog.value = false;
    }
};

// 返回列表
const handleBackToList = () => {
    emit('back-to-list');
};
</script>

<style lang="scss" scoped>
.pano-info-form {
    padding: 10px;

    .form-group {
        margin-bottom: 15px;

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;

            .required {
                color: red;
            }
        }

        .helper-text {
            display: block;
            color: #666;
            font-size: 0.8rem;
            margin-top: 3px;
        }
    }

    .pano-preview {
        position: relative;
        margin-bottom: 10px;

        .preview-image {
            width: 100%;
            max-height: 200px;
            object-fit: cover;
            border-radius: 4px;
        }

        .preview-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
        }
    }

    .location-info {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;

        .coordinates {
            display: flex;
            gap: 15px;
            margin-bottom: 5px;

            span {
                font-family: monospace;
            }
        }

        .address {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #666;

            i {
                color: #ff4d4f;
            }
        }
    }

    .meta-info {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;

        .meta-item {
            display: flex;
            margin-bottom: 5px;

            &:last-child {
                margin-bottom: 0;
            }

            .meta-label {
                width: 80px;
                color: #666;
            }

            .meta-value {
                font-weight: 500;
            }
        }
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
    }

    .replace-dialog-content,
    .delete-dialog-content {
        padding: 10px 0;
    }

    .file-preview {
        margin-top: 10px;
        display: flex;
        align-items: center;

        .preview-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 4px;
        }

        .file-info {
            margin-left: 10px;
            display: flex;
            flex-direction: column;

            span {
                &:first-child {
                    font-weight: 500;
                }

                &:last-child {
                    color: #666;
                    font-size: 0.8rem;
                }
            }
        }
    }

    :deep(.p-inputtext),
    :deep(.p-textarea),
    :deep(.p-chips) {
        width: 100%;
    }
}
</style>
