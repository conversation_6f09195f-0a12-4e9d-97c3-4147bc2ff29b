<template>
    <div class="photo-info-list">
        <div class="list-header">
            <h3>照片标记列表</h3>
            <div class="list-actions">
                <Button icon="pi pi-trash" class="p-button-danger p-button-sm" :disabled="selectedItems.length === 0" @click="handleDeleteSelected" />
            </div>
        </div>

        <DataTable
            :value="items"
            v-model:selection="selectedItems"
            dataKey="id"
            :paginator="true"
            :rows="10"
            :rowsPerPageOptions="[5, 10, 20]"
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
            responsiveLayout="scroll"
        >
            <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>
            <Column field="name" header="名称" sortable>
                <template #body="slotProps">
                    <span class="marker-name">{{ slotProps.data.name }}</span>
                </template>
            </Column>
            <Column field="city" header="城市" sortable>
                <template #body="slotProps">
                    <span>{{ slotProps.data.city || '未知' }}</span>
                </template>
            </Column>
            <Column field="gps_timestamp" header="拍摄时间" sortable>
                <template #body="slotProps">
                    {{ formatDate(slotProps.data.gps_timestamp) }}
                </template>
            </Column>
            <Column header="缩略图">
                <template #body="slotProps">
                    <img v-if="slotProps.data.thumbnail_url" :src="slotProps.data.thumbnail_url" alt="缩略图" class="thumbnail-preview" />
                </template>
            </Column>
            <Column header="操作">
                <template #body="slotProps">
                    <Button icon="pi pi-compass" class="p-button-rounded p-button-info p-button-sm" @click="handleView(slotProps.data)" />
                    <Button icon="pi pi-pencil" class="p-button-rounded p-button-success p-button-sm" @click="handleEdit(slotProps.data)" />
                </template>
            </Column>
        </DataTable>

        <div v-if="items.length === 0" class="empty-list">
            <p>暂无照片标记</p>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';

const props = defineProps({
    items: {
        type: Array,
        default: () => []
    }
});

const emit = defineEmits(['select-item', 'edit-item', 'delete-items']);

const selectedItems = ref([]);

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '未知时间';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// 查看照片详情
const handleView = (item) => {
    emit('select-item', item);
};

// 编辑照片
const handleEdit = (item) => {
    emit('edit-item', item);
};

// 删除选中的照片
const handleDeleteSelected = () => {
    if (selectedItems.value.length > 0) {
        const ids = selectedItems.value.map((item) => item.id);
        emit('delete-items', ids);
        selectedItems.value = [];
    }
};
</script>

<style lang="scss" scoped>
.photo-info-list {
    padding: 10px;

    .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        h3 {
            margin: 0;
            font-size: 1.1rem;
        }

        .list-actions {
            display: flex;
            gap: 5px;
        }
    }

    .marker-name {
        font-weight: 500;
    }

    .thumbnail-preview {
        width: 60px;
        height: 40px;
        object-fit: cover;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .empty-list {
        text-align: center;
        padding: 20px;
        color: #666;
        font-style: italic;
    }
}
</style>
