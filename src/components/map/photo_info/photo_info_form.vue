<template>
    <div class="photo-info-form">
        <Toast />
        <form @submit.prevent="handleSubmit">
            <Fluid>
                <div class="form-group">
                    <div class="grid grid-cols-8 gap-4">
                        <div class="col-span-3">
                            <label for="name">照片名称 <span class="required">*</span></label>
                            <InputText id="name" v-model="formData.name" :class="{ 'p-invalid': v$.name.$invalid && v$.name.$dirty }" />
                            <small v-if="v$.name.$invalid && v$.name.$dirty" class="p-error">{{ v$.name.$errors[0].$message }}</small>
                        </div>
                        <div class="col-span-5">
                            <div class="label-group">
                                <label for="tag">标签</label>
                                <small class="helper-text">输入标签后按回车或空格键添加</small>
                            </div>
                            <Chips v-model="formData.tag" separator=" " />
                        </div>
                    </div>
                </div>
            </Fluid>

            <div class="form-group">
                <label>照片预览</label>
                <div class="photo-preview">
                    <img :src="previewPhoto" alt="照片预览" class="preview-image" />
                    <div class="preview-actions">
                        <Button icon="pi pi-compass" class="p-button-rounded p-button-info p-button-sm" @click="handleLocate" />
                        <Button icon="pi pi-eye" class="p-button-rounded p-button-info p-button-sm" @click="handleViewImage" />
                        <Button icon="pi pi-refresh" class="p-button-rounded p-button-warning p-button-sm" @click="showReplaceDialog = true" />
                        <Button icon="pi pi-map-marker" rounded raised :size="iconSize" @click="pointCustomGcj" />
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>照片信息</label>
                <div class="location-info">
                    <div class="coordinates">
                        <div class="coordinates-row">
                            <span>城市: {{ formData.city }} | </span>
                            <!-- <div v-if="formData.full_address" class="address"> -->
                            <i class="pi pi-map-marker">_</i>
                            <span>{{ props.marker.value.full_address }}</span>
                            <!-- </div> -->
                        </div>
                        <div class="coordinates-row">
                            <span>GPS坐标: {{ formatCoordinates(formData.gps) }}</span>
                            <span>| GCJ坐标: {{ formatCoordinates(formData.gcj) }}</span>
                        </div>
                        <div class="coordinates-row">
                            <span>海拔高度: {{ formData.altitude }} 米 </span>
                            <span> | 创建时间: {{ formatDate(formData.create_time) }}</span>
                        </div>
                        <span>拍摄时间: <Calendar id="timestamp" v-model="formData.gps_timestamp" showTime hourFormat="24" /></span>
                    </div>
                </div>
            </div>

            <Fluid>
                <div class="form-group" title="切换位置">
                    <div class="grid grid-cols-12 gap-4">
                        <div class="col-span-4">
                            <SelectButton v-model="is_custom_selector" :options="is_custom_options" />
                        </div>
                        <div class="col-span-3">
                            <span>{{ !formData.is_custom ? formatCoordinates(formData.gcj) : formatCoordinates(formData.custom_gcj) }}</span>
                        </div>
                        <div class="col-span-5">
                            <span>{{ formData.custom_address }}</span>
                        </div>
                    </div>
                </div>
            </Fluid>

            <div class="form-group">
                <label>新照片信息</label>
                <div class="meta-info">
                    <div class="meta-item">
                        <span class="meta-label">文件名:</span>
                        <span class="meta-value">{{ newPhotoData.name }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">文件大小:</span>
                        <span class="meta-value">{{ formatFileSize(newPhotoData.size) }} </span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">GPS:</span>
                        <span class="meta-value">{{ newPhotoData.gps.lng.toFixed(6) }}, {{ newPhotoData.gps.lat.toFixed(6) }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">GCJ:</span>
                        <span class="meta-value">{{ newPhotoData.gcj.lng.toFixed(6) }}, {{ newPhotoData.gcj.lat.toFixed(6) }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">新照片位置:</span>
                        <span class="meta-value">{{ newPhotoData.full_address || '' }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">拍摄时间:</span>
                        <span class="meta-value">{{ newPhotoData.timestamp }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">海拔:</span>
                        <span class="meta-value">{{ newPhotoData.altitude }} 米</span>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <Button type="submit" label="更新" icon="pi pi-check" class="p-button-success" :loading="loading" />
                <Button type="button" label="删除" icon="pi pi-trash" class="p-button-danger" @click="confirmDelete" :disabled="loading" />
                <Button type="button" label="返回" icon="pi pi-arrow-left" class="p-button-secondary" @click="handleBackToList" :disabled="loading" />
            </div>
        </form>

        <!-- 替换照片对话框 -->
        <Dialog v-model:visible="showReplaceDialog" header="替换照片" :modal="true" :closable="false">
            <div class="replace-dialog-content">
                <FileUpload name="photo-file" @upload="handleFileUpload" accept="image/*" :maxFileSize="10000000" :multiple="false" @select="onPhotoSelect" :uploadButtonProps :cancelButtonProps chooseLabel="选择照片" :fileLimit="1">
                    <template #empty>
                        <span>Drag and drop files to here to upload.</span>
                    </template>
                </FileUpload>
                <div v-if="newPhotoFile" class="file-preview">
                    <img :src="newPhotoFile.objectURL" alt="新照片预览" class="preview-image" />
                </div>
            </div>
            <template #footer>
                <Button label="确认替换" icon="pi pi-check" class="p-button-success" @click="handleReplaceConfirm" :disabled="!newPhotoFile || replacingFile" :loading="replacingFile" />
                <Button label="取消" icon="pi pi-times" class="p-button-secondary" @click="cancelUploadFile" :disabled="replacingFile" />
            </template>
        </Dialog>

        <!-- 删除确认对话框 -->
        <Dialog v-model:visible="showDeleteDialog" header="确认删除" :modal="true" :closable="true">
            <div class="delete-dialog-content">
                <p>确定要删除此照片标记吗？此操作不可撤销。</p>
            </div>
            <template #footer>
                <Button label="确认删除" icon="pi pi-trash" class="p-button-danger" @click="handleDelete" :loading="deleting" />
                <Button label="取消" icon="pi pi-times" class="p-button-secondary" @click="showDeleteDialog = false" :disabled="deleting" />
            </template>
        </Dialog>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useVuelidate } from '@vuelidate/core';
import { required, maxLength } from '@vuelidate/validators';
import InputText from 'primevue/inputtext';
import Calendar from 'primevue/calendar';
import FileUpload from 'primevue/fileupload';
import Chips from 'primevue/chips';
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';
import SelectButton from 'primevue/selectbutton';
import Fluid from 'primevue/fluid';
import { useToast } from 'primevue/usetoast';
import { wgs84togcj02 } from '@/utils/coordinate';
import { parse } from 'exifr';
import { getGeoInfo, setMarker, removeMarker } from '@/utils/mapTool.js';
import { useMapStore } from '@/stores/mapStore';
import { ICON_SVG } from '@/config/svg';

const props = defineProps({
    marker: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['update', 'delete', 'cancel', 'back-to-list', 'select-item']);

const toast = useToast();
const mapStore = useMapStore();
const iconSize = 'small';
const point_gcj = reactive({ lng: 0, lat: 0 });
const point_value = ref(`${point_gcj.lng}, ${point_gcj.lat}`); //这里的 point_value 是一个值为逗号分隔的字符串
const loading = ref(false);
const deleting = ref(false);
const replacingFile = ref(false);
const showReplaceDialog = ref(false);
const showDeleteDialog = ref(false);
const newPhotoFile = ref(null);
const defaultPhoto = 'empty-photo.webp';
const is_custom_selector = ref('图片位置');
const is_custom_options = ref(['图片位置', '指定位置']);
const previewPhoto = ref(null);

// const chooseButtonProps = {
//     disabled: newPhotoFile.value
// };

// watch(newPhotoFile, (newValue) => {
//     chooseButtonProps.disabled = newValue;
// });

const uploadButtonProps = {
    hidden: 'hidden',
    label: '上传',
    style: 'display: none' // 直接通过 style 隐藏
};

const cancelButtonProps = {
    style: 'display: none'
};

// 表单数据
const formData = reactive({
    id: '',
    create_time: '',
    name: '',
    tag: null,
    collection_id: '',
    link: '',
    city: '',
    full_address: '',
    gps: { lng: 0, lat: 0 },
    gcj: { lng: 0, lat: 0 },
    custom_gcj: { lng: 0, lat: 0 },
    altitude: null,
    gps_timestamp: '',
    photo_url: '',
    thumbnail_url: '',
    photoFile: '',
    is_custom: false,
    custom_address: ''
});
const newPhotoData = reactive({
    name: '未上传图片',
    size: 0,
    gps: { lng: 0, lat: 0 },
    gcj: { lng: 0, lat: 0 },
    timestamp: 0,
    altitude: 0,
    full_address: '',
    city: '',
    photo_url: '',
    thumbnail_url: ''
});
// 表单验证规则
const rules = {
    name: { required, maxLength: maxLength(100) }
};
const v$ = useVuelidate(rules, formData);
//========================================================
// 替换预览照片为新的照片
watch(
    () => newPhotoFile.value,
    (newValue) => {
        if (newValue && newValue.objectURL) {
            previewPhoto.value = newValue.objectURL;
        }
    }
);
// 监听  reactive 类型的 formData ，当它发生变化时 ？？
watch(
    () => formData,
    (newValue) => {
        formData.is_custom = newValue.is_custom;
        formData.custom_address = newValue.custom_address;
    }
);
// 监听 is_custom_selector 这个 ref：用来在表单界面中显示 现在使用的是哪个位置的坐标
watch(is_custom_selector, (newValue) => {
    if (newValue === '图片位置' || newValue === null) {
        formData.is_custom = false;
    } else if (newValue === '指定位置') {
        formData.is_custom = true;
    }
});
// 监听 point_value 这个 ref 坐标对象,分解对象并转化成数值，然后存入到自定义的gcj中
watch(point_value, (newValue) => {
    const [lng, lat] = newValue.split(', '); //拆分这个字符串，并把字符串类型改为数字类型
    formData.custom_gcj.lng = Number(parseFloat(lng).toFixed(6));
    formData.custom_gcj.lat = Number(parseFloat(lat).toFixed(6));
});
//==========================================================
// 新照片预览URL
const newPhotoPreviewUrl = computed(() => {
    if (newPhotoFile.value) {
        return URL.createObjectURL(newPhotoFile.value);
    }
    return '';
});

// 初始化表单数据
onMounted(() => {
    if (props.marker) {
        previewPhoto.value = props.marker.value.photo_url || defaultPhoto;
        formData.id = props.marker.value.id;
        formData.create_time = props.marker.value.create_time || '';
        formData.name = props.marker.value.name || '';
        formData.tag = props.marker.value.tag || '';
        formData.collection_id = props.marker.value.collection_id || '';
        formData.link = props.marker.value.link || '';
        formData.city = props.marker.value.city || '';
        formData.full_address = props.marker.value.full_address || '';
        formData.altitude = props.marker.value.altitude || null;
        formData.gps = props.marker.value.gps || { lng: 0, lat: 0 };
        formData.gcj = props.marker.value.gcj || { lng: 0, lat: 0 };
        formData.custom_gcj = props.marker.value.custom_gcj || { lng: 0, lat: 0 };
        formData.gps_timestamp = props.marker.value.gps_timestamp || new Date();
        formData.photo_url = props.marker.value.photo_url || '';
        formData.thumbnail_url = props.marker.value.thumbnail_url || '';
        formData.is_custom = props.marker.value.is_custom || false;
        formData.custom_address = props.marker.value.custom_address || '';

        // 处理时间戳
        if (props.marker.value.gps_timestamp) {
            formData.gps_timestamp = new Date(props.marker.value.gps_timestamp);
        }
        // 修改标签处理逻辑 - 从逗号分隔的字符串解析为数组
        if (props.marker.value.tag) {
            if (Array.isArray(props.marker.value.tag)) {
                formData.tag = props.marker.value.tag;
            } else if (typeof props.marker.value.tag === 'string') {
                // 直接按逗号分割字符串
                formData.tag = props.marker.value.tag
                    .split(',')
                    .map((tag) => tag.trim())
                    .filter((tag) => tag);
            } else {
                formData.tag = [];
            }
        } else {
            formData.tag = [];
        }
    }
    if (formData.is_custom) {
        is_custom_selector.value = '自选位置';
    } else {
        is_custom_selector.value = '图片位置';
    }
    console.log('is_custom_selector: ', is_custom_selector.value);
});

// 格式化坐标
const formatCoordinates = (gps) => {
    if (!gps) return '未知';
    if (typeof gps.lat === 'string') {
        return `${parseFloat(gps.lng).toFixed(6) || 0}, ${parseFloat(gps.lat).toFixed(6) || 0}`;
    }
    return `${gps.lng?.toFixed(6) || 0}, ${gps.lat?.toFixed(6) || 0}`;
};

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '未知时间';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// 格式化文件大小
const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 查看原图
const handleViewImage = () => {
    window.open(formData.photo_url, '_blank');
};

// 确认删除
const confirmDelete = () => {
    showDeleteDialog.value = true;
};

// 删除标记
const handleDelete = async () => {
    deleting.value = true;

    try {
        emit('delete', formData.id);
    } catch (error) {
        console.error('删除标记失败:', error);
    } finally {
        deleting.value = false;
        showDeleteDialog.value = false;
    }
};

// 返回列表
const handleBackToList = () => {
    emit('back-to-list');
};

// 取消上传图片
function cancelUploadFile() {
    previewPhoto.value = formData.photo_url;
    newPhotoFile.value = null;
    showReplaceDialog.value = false;
}

// 选择照片
async function onPhotoSelect(event) {
    console.log('onPhotoSelect:', event);
    newPhotoFile.value = event.files[0];
    console.log('newPhotoFile.value:', newPhotoFile.value);
    const exif = await parse(newPhotoFile.value);
    console.log('EXIF data:', exif); // 调试用，查看完整的 EXIF 数据结构
    newPhotoData.name = newPhotoFile.value.name;
    newPhotoData.size = newPhotoFile.value.size;
    newPhotoData.gps = { lng: exif.longitude, lat: exif.latitude };
    newPhotoData.gcj = { lng: wgs84togcj02(exif.longitude, exif.latitude)[0], lat: wgs84togcj02(exif.longitude, exif.latitude)[1] };
    newPhotoData.timestamp = exif.DateTimeOriginal;
    newPhotoData.altitude = exif.GPSAltitude;
    const newPhotoGeoInfo = await getGeoInfo(exif.longitude, exif.latitude);
    newPhotoData.full_address = newPhotoGeoInfo.full_address;
    newPhotoData.city = newPhotoGeoInfo.city;
    console.log('newPhotoData:', newPhotoData);
}

// 处理图片上传
const handleFileUpload = (event) => {
    console.log('文件上传事件:', event);
    const file = event.files[0];
    if (file) {
        newPhotoFile.value = file;
    }
};

// 定位到照片位置
const handleLocate = () => {
    emit('select-item', props.marker.value);
    console.log('handleLocate:', props.marker.value);
};

// 确认替换照片
// - 确认照片 exif 信息
// - 上传图片 & 后端生成缩略图
// - 替换原图片
// - 替换原缩略图
const handleReplaceConfirm = async () => {
    if (!newPhotoFile.value) return;
    replacingFile.value = true;
    try {
        // 构建FormData对象 - 重命名为formDataObj避免变量遮蔽
        const formDataObj = new FormData();
        formDataObj.append('file', newPhotoFile.value);

        // 构建元数据
        const metadata = {
            name: newPhotoData.name,
            photo_url: '',
            thumbnail_url: '',
            gcj: newPhotoData.gcj,
            custom_gcj: formData.custom_gcj || newPhotoData.gcj
        };

        // 添加元数据到 formDataObj
        const metadataBlob = new Blob([JSON.stringify(metadata)], { type: 'application/json' });
        formDataObj.append('metadata', metadataBlob);

        // 发送请求到后端API
        const response = await fetch('http://localhost:3001/api/image-processor', {
            method: 'POST',
            body: formDataObj
        });

        if (!response.ok) {
            throw new Error(`上传失败: ${response.statusText}`);
        }

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.error || '上传失败');
        }

        // 更新组件级别的formData对象
        newPhotoData.photo_url = result.data.photo_url;
        newPhotoData.thumbnail_url = result.data.thumbnail_url;
        formData.name = newPhotoData.name;
        // 关闭对话框
        showReplaceDialog.value = false;

        // 成功提示
        toast.add({ severity: 'success', summary: '成功', detail: `照片已成功替换为${newPhotoFile.value.objectURL}`, life: 3000 });
    } catch (error) {
        console.error('替换照片失败:', error);
        // toast.add({ severity: 'error', summary: '错误', detail: '替换照片失败: ' + error.message, life: 3000 });
    } finally {
        replacingFile.value = false;
    }
};

// 提交表单
const handleSubmit = async () => {
    const isValid = await v$.value.$validate();
    if (!isValid) return;

    loading.value = true;

    try {
        // 将标签数组转换为逗号分隔的字符串
        const tagString = Array.isArray(formData.tag) ? formData.tag.join(',') : typeof formData.tag === 'string' ? formData.tag : '';

        // 构建提交数据
        if (!newPhotoFile.value) {
            const submitData = reactive({
                id: formData.id,
                create_time: formData.create_time,
                name: formData.name,
                tag: tagString,
                collection_id: formData.collection_id,
                link: formData.link,
                city: formData.city,
                full_address: formData.full_address,
                custom_address: formData.custom_address,
                altitude: formData.altitude,
                gps: formData.gps,
                gcj: formData.gcj,
                custom_gcj: formData.custom_gcj,
                is_custom: formData.is_custom,
                gps_timestamp: formData.gps_timestamp,
                photo_url: formData.photo_url,
                thumbnail_url: formData.thumbnail_url
            });
            console.error('tag: ', tagString);
            console.log('非新照片更新 submitData: ', submitData);
            emit('update', submitData);
        } else {
            // 这里有问题
            const newPhotoGeoInfo = await getGeoInfo(newPhotoData.gcj.lng, newPhotoData.gcj.lat);
            const newPhotoFormData = reactive({
                id: formData.id,
                create_time: formData.create_time,
                name: newPhotoData.name,
                tag: tagString,
                collection_id: formData.collection_id,
                link: formData.link,
                city: newPhotoGeoInfo.city,
                full_address: newPhotoGeoInfo.full_address,
                custom_address: formData.custom_address,
                altitude: newPhotoData.altitude,
                gps: newPhotoData.gps,
                gcj: newPhotoData.gcj,
                custom_gcj: formData.custom_gcj,
                gps_timestamp: newPhotoData.timestamp,
                photo_url: newPhotoData.photo_url, //TODO 拼接字符后存入 sb 都在imageProcessor 中处理
                thumbnail_url: newPhotoData.thumbnail_url //TODO serverAPI 处理后，存入 sb
            });
            console.log('新照片更新 newPhotoData: ', newPhotoFormData);
            emit('update', newPhotoFormData);
        }
    } catch (error) {
        console.error('更新标记失败:', error);
    } finally {
        loading.value = false;
    }
};

// 点击地图，获取 custom gcj 函数
const pointCustomGcj = () => {
    // 使用局部样式而非全局样式
    const setCursor = () => {
        // 使用局部样式而非全局样式
        const existingStyle = document.getElementById('map-cursor-style');
        if (existingStyle) {
            document.head.removeChild(existingStyle);
        }
        // 只修改地图容器的样式，而不是全局样式
        const styleEl = document.createElement('style');
        styleEl.id = 'map-cursor-style';
        styleEl.innerHTML = `
            #container, #container * {
                cursor: url('${ICON_SVG.PIN}'), auto !important;
            }
        `;
        document.head.appendChild(styleEl);
    };
    // 修改重置鼠标样式的函数
    const resetCursor = () => {
        // 移除样式
        const styleEl = document.getElementById('map-cursor-style');
        if (styleEl) {
            document.head.removeChild(styleEl);
        }
        mapStore.locationPickerMode.active = false;
    };
    setCursor();
    // 添加安全机制：30 秒后自动重置，防止用户不点击地图导致样式一直存在
    const safetyTimeout = setTimeout(() => {
        resetCursor();
        toast.add({
            severity: 'info',
            summary: '位置选择已取消',
            detail: '超时未选择位置',
            life: 3000
        });
    }, 30000); // 30s超时
    // 设置地图为位置选择模式

    // 使用防抖处理地图点击回调
    const processMapClick = async (position) => {
        clearTimeout(safetyTimeout);

        // 先重置鼠标样式，减少渲染负担
        resetCursor();

        // 当地图被点击时，这个回调会被调用
        formData.custom_gcj = {
            lng: position.lng,
            lat: position.lat
        };

        // 更新显示值，同时 watch 会监听变化，并将它们数字化后，存入 custom_gcj的 lng 和 lat 中。
        point_value.value = `${position.lng.toFixed(6)}, ${position.lat.toFixed(6)}`;

        // 在地图上添加标记 - 使用修改后的 setMarker 函数
        const markerAdded = await setMarker(position.lng, position.lat, `photo-marker-${formData.id || Date.now()}`);

        if (!markerAdded) {
            toast.add({
                severity: 'error',
                summary: '标记添加失败',
                detail: '无法在地图上添加标记',
                life: 3000
            });
        }

        // 获取地址信息
        try {
            const geoInfo = await getGeoInfo(position.lng, position.lat);
            formData.custom_address = geoInfo.full_address;

            // 设置为自定义位置
            formData.is_custom = true;
            is_custom_selector.value = '指定位置';

            // 提示成功
            toast.add({
                severity: 'success',
                summary: '位置已更新',
                detail: `新位置: ${formData.full_address}`,
                life: 3000
            });
        } catch (error) {
            console.error('获取地址信息失败:', error);
            toast.add({
                severity: 'error',
                summary: '获取地址失败',
                detail: '无法获取选择位置的地址信息',
                life: 3000
            });
        }
    };

    // 设置地图为位置选择模式
    mapStore.setLocationPickerMode({
        active: true,
        message: '请点击地图选择新的位置',
        callback: processMapClick
    });
};
</script>

<style lang="scss" scoped>
.photo-info-form {
    padding: 10px;

    .form-group {
        margin-bottom: 15px;

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;

            .required {
                color: red;
            }
        }

        .helper-text {
            display: block;
            color: #666;
            font-size: 0.8rem;
            margin-top: 3px;
        }
    }

    .photo-preview {
        position: relative;
        margin-bottom: 10px;

        .preview-image {
            width: 100%;
            max-height: 200px;
            object-fit: cover;
            border-radius: 4px;
        }

        .preview-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
        }
    }

    .location-info {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;

        .coordinates {
            display: flex;
            flex-direction: column;
            gap: 5px;
            margin-bottom: 5px;

            span {
                font-family: monospace;
            }
        }

        .address {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #666;
            margin-top: 5px;

            i {
                color: #ff4d4f;
            }
        }
    }

    .meta-info {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;

        .meta-item {
            display: flex;
            margin-bottom: 5px;

            &:last-child {
                margin-bottom: 0;
            }

            .meta-label {
                width: 80px;
                color: #666;
            }

            .meta-value {
                font-weight: 500;
            }
        }
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
    }

    .replace-dialog-content,
    .delete-dialog-content {
        padding: 10px 0;
    }

    .label-group {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .file-preview {
        margin-top: 10px;
        display: flex;
        align-items: center;

        .preview-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 4px;
        }

        .file-info {
            margin-left: 10px;
            display: flex;
            flex-direction: column;

            span {
                &:first-child {
                    font-weight: 500;
                }

                &:last-child {
                    color: #666;
                    font-size: 0.8rem;
                }
            }
        }
    }

    :deep(.p-inputtext),
    :deep(.p-calendar),
    :deep(.p-textarea),
    :deep(.p-chips) {
        width: 100%;
    }
}
</style>
