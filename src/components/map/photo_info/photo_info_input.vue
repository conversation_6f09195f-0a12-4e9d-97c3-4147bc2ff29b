<template>
    <div class="photo-info-input">
        <Toast />
        <form @submit.prevent="handleSubmit">
            <Fuild>
                <div class="form-group">
                    <div class="grid grid-cols-8 gap-4">
                        <div class="col-span-3">
                            <label for="name">照片名称 <span class="required">*</span></label>
                            <InputText id="name" v-model="formData.name" :class="{ 'p-invalid': v$.name.$invalid && v$.name.$dirty }" />
                            <small v-if="v$.name.$invalid && v$.name.$dirty" class="p-error">{{ v$.name.$errors[0].$message }}</small>
                        </div>
                        <div class="col-span-5">
                            <div class="label-group">
                                <label for="tag">标签</label>
                                <small class="helper-text">输入标签后按回车或空格键添加</small>
                            </div>
                            <Chips v-model="formData.tag" separator=" " />
                        </div>
                    </div>
                </div>
            </Fuild>

            <!-- <div class="form-group">
                <label for="photo-file">照片文件 <span class="required">*</span></label>
                <div class="file-upload-container">
                    <FileUpload mode="basic" name="photo-file" :customUpload="true" @uploader="handleFileUpload" accept="image/*" :maxFileSize="10000000" :class="{ 'p-invalid': v$.photoFile.$invalid && v$.photoFile.$dirty }" />
                    <small v-if="v$.photoFile.$invalid && v$.photoFile.$dirty" class="p-error">{{ v$.photoFile.$errors[0].$message }}</small>
                    <div v-if="formData.photoFile" class="file-preview">
                        <img :src="previewUrl" alt="预览" class="preview-image" />
                        <div class="file-info">
                            <span>{{ formData.photoFile.name }}</span>
                            <span>{{ formatFileSize(formData.photoFile.size) }}</span>
                        </div>
                    </div>
                </div>

                < div name="添加照片预览区域" />
                <div v-if="formData.photoFile" class="photo-preview-container">
                    <div class="photo-preview-wrapper">
                        <img :src="previewUrl" role="presentation" :alt="formData.photoFile.name" class="photo-preview-image" />
                        <div class="photo-preview-info">
                            <span class="font-semibold">{{ formData.photoFile.name }}</span>
                            <div>{{ formatFileSize(formData.photoFile.size) }}</div>
                            <Badge value="待上传" severity="warn" />
                        </div>
                    </div>
                </div>
            </div> -->

            <div class="form-group">
                <label>照片预览</label>
                <div class="photo-preview">
                    <img :src="previewPhoto" alt="照片预览" class="preview-image" />
                    <div class="preview-actions">
                        <Button icon="pi pi-eye" class="p-button-rounded p-button-info p-button-sm" @click="handleViewImage" />
                        <Button icon="pi pi-refresh" class="p-button-rounded p-button-warning p-button-sm" @click="showReplaceDialog = true" />
                        <Button icon="pi pi-map-marker" rounded raised :size="iconSize" @click="pointCustomGcj" />
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>照片信息</label>
                <div class="location-info">
                    <div class="coordinates">
                        <div class="coordinates-row">
                            <span>城市: {{ formData.city }} | </span>
                            <!-- <div v-if="formData.full_address" class="address"> -->
                            <i class="pi pi-map-marker">_</i>
                            <span>{{ props.marker.value.full_address }}</span>
                            <!-- </div> -->
                        </div>
                        <div class="coordinates-row">
                            <span>GPS坐标: {{ formatCoordinates(formData.gps) }}</span>
                            <span>| GCJ坐标: {{ formatCoordinates(formData.gcj) }}</span>
                        </div>
                        <div class="coordinates-row">
                            <span>海拔高度: {{ formData.altitude }} 米 </span>
                            <span> | 创建时间: {{ formatDate(formData.create_time) }}</span>
                        </div>
                        <span>拍摄时间: <Calendar id="timestamp" v-model="formData.gps_timestamp" showTime hourFormat="24" /></span>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>新照片信息</label>
                <div class="meta-info">
                    <div class="meta-item">
                        <span class="meta-label">文件名:</span>
                        <span class="meta-value">{{ newPhotoData.name }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">文件大小:</span>
                        <span class="meta-value">{{ formatFileSize(newPhotoData.size) }} </span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">GPS:</span>
                        <span class="meta-value">{{ newPhotoData.gps.lng.toFixed(6) }}, {{ newPhotoData.gps.lat.toFixed(6) }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">GCJ:</span>
                        <span class="meta-value">{{ newPhotoData.gcj.lng.toFixed(6) }}, {{ newPhotoData.gcj.lat.toFixed(6) }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">新照片位置:</span>
                        <span class="meta-value">{{ newPhotoData.full_address || '' }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">拍摄时间:</span>
                        <span class="meta-value">{{ newPhotoData.timestamp }}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">海拔:</span>
                        <span class="meta-value">{{ newPhotoData.altitude }} 米</span>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <Button type="submit" label="保存" icon="pi pi-check" class="p-button-success" :loading="loading" />
                <Button type="button" label="取消" icon="pi pi-times" class="p-button-secondary" @click="handleCancel" :disabled="loading" />
            </div>
            <!-- 上传照片对话框 -->
            <Dialog v-model:visible="showReplaceDialog" header="上传照片" :modal="true" :closable="false">
                <div class="replace-dialog-content">
                    <FileUpload name="photo-file" @upload="handleFileUpload" accept="image/*" :maxFileSize="10000000" :multiple="false" @select="onPhotoSelect" :uploadButtonProps :fileLimit="1">
                        <template #empty>
                            <span>Drag and drop files to here to upload.</span>
                        </template>
                    </FileUpload>
                    <div v-if="newPhotoFile" class="file-preview">
                        <img :src="newPhotoFile.objectURL" alt="新照片预览" class="preview-image" />
                    </div>
                </div>
                <template #footer>
                    <Button label="上传图片" icon="pi pi-check" class="p-button-success" @click="handleUpLoadPhoto" :disabled="!newPhotoFile || replacingFile" :loading="replacingFile" />
                    <Button label="取消" icon="pi pi-times" class="p-button-secondary" @click="cancelUploadFile" :disabled="replacingFile" />
                </template>
            </Dialog>

            <!-- 删除确认对话框 -->
            <Dialog v-model:visible="showDeleteDialog" header="确认删除" :modal="true" :closable="true">
                <div class="delete-dialog-content">
                    <p>确定要删除此照片标记吗？此操作不可撤销。</p>
                </div>
                <template #footer>
                    <Button label="确认删除" icon="pi pi-trash" class="p-button-danger" @click="handleDelete" :loading="deleting" />
                    <Button label="取消" icon="pi pi-times" class="p-button-secondary" @click="showDeleteDialog = false" :disabled="deleting" />
                </template>
            </Dialog>
        </form>
    </div>
</template>

<script setup>
import InputText from 'primevue/inputtext';
import Calendar from 'primevue/calendar';
import FileUpload from 'primevue/fileupload';
import Chips from 'primevue/chips';
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';
import Badge from 'primevue/badge';
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useToast } from 'primevue/usetoast';
import { parse } from 'exifr';
import { useVuelidate } from '@vuelidate/core';
import { required, maxLength } from '@vuelidate/validators';
import { gcj02towgs84, wgs84togcj02 } from '@/utils/coordinate';
import { getGeoInfo, setMarker, removeMarker } from '@/utils/mapTool.js';
import { useMapStore } from '@/stores/mapStore';
const props = defineProps({
    marker: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['submit', 'cancel']);

const toast = useToast();
const mapStore = useMapStore();
const point_gcj = reactive({ lng: 0, lat: 0 });
const loading = ref(false);
const replacingFile = ref(false);
const showReplaceDialog = ref(false);
const newPhotoFile = ref(null);
const defaultPhoto = 'empty-photo.webp';
const previewPhoto = ref(defaultPhoto);

// 初始化表单数据
const formData = reactive({
    name: props.marker.value.name,
    tag: [],
    collection_id: '',
    link: null,
    city: props.marker.value.city || null,
    full_address: props.marker.value.full_address || null,
    gps: null,
    gjc: null,
    custom_gcj: props.marker.value.custom_gcj || null,
    altitude: null,
    gps_timestamp: '',
    photo_url: defaultPhoto || null,
    thumbnail_url: props.marker.value.thumbnail_url || null,
    photoFile: null,
    is_custom: false
});
// 表单验证规则
const rules = {
    name: { required, maxLength: maxLength(20) },
    photoFile: { required }
};
const v$ = useVuelidate(rules, formData);
// 新照片数据
const newPhotoData = reactive({
    name: '未上传图片',
    size: 0,
    gps: { lng: 0, lat: 0 },
    gcj: { lng: 0, lat: 0 },
    timestamp: 0,
    altitude: 0,
    full_address: '',
    city: ''
});
// 构建完整的GPS数据
let customGCJtogps = { lng: props.marker.value.custom_gcj.lng, lat: props.marker.value.custom_gcj.lat };
// 使用坐标转换工具进行转换（如果有）
if (gcj02towgs84) {
    try {
        const converted = gcj02towgs84(customGCJtogps.lng, customGCJtogps.lat);
        customGCJtogps = { lng: converted[0], lat: converted[1] };
    } catch (e) {
        console.warn('坐标转换失败:', e);
    }
}

onMounted(() => {
    if (props.marker) {
        // TODO：
        // 1. 解析照片的EXIF信息
        // 2. 根据EXIF信息设置表单数据
        // 3. 根据经纬度获取地址信息
        // 4. 根据地址信息设置表单数据
        // 5. 数据传给 infoWindow 处理保存
        // formData.original_lng = props.marker.lng;
        // formData.original_lat = props.marker.lat;
        // formData.gcj_lng = props.marker.lng;
        // formData.gcj_lat = props.marker.lat;
        return;
    }
});
//=======================================================
watch(
    () => newPhotoFile.value,
    (newValue) => {
        if (newValue && newValue.objectURL) {
            previewPhoto.value = newValue.objectURL;
        }
    }
);
// 添加 watch 监听 上传图片后，保存图片信息到 formData 各个字段中
watch(
    () => formData.photo_url,
    (newValue) => {
        if (newValue) {
            formData.name = newPhotoData.name || '';
            formData.gps = newPhotoData.gps || '';
            formData.gcj = newPhotoData.gcj || '';
            formData.altitude = newPhotoData.altitude || '';
            formData.full_address = newPhotoData.full_address || '';
            formData.city = newPhotoData.city || '';
            formData.gps_timestamp = newPhotoData.timestamp || '';
            console.log('formData.name : ', formData.name, formData);
        }
    },
    { deep: true }
);
// 添加 watch 监听 照片是否被解析。注意📢：此时还没有上传图片，故而新照片信息不能传递给 formData
watch(
    () => newPhotoFile.value,
    (newPhoto) => {
        if (newPhoto) {
            formData.photoFile = newPhoto || '';
            formData.name = newPhotoData.name || '';
            console.log('formData_newPhoto: ', formData.photoFile);
        }
    },
    { deep: true }
);
// 添加 watch 监听 照片 previewUrl 的变化
// watch(
//     () => (previewUrl.value ? previewUrl.value : ''),
//     (newPpreviewUrl) => {
//         if (newPpreviewUrl) {
//             console.log('newPpreviewUrl: ', newPpreviewUrl);
//         }
//     },
//     { deep: true }
// );
//=======================================================

function cancelUploadFile() {
    newPhotoFile.value = null;
    showReplaceDialog.value = false;
}

//=======================================================
// 格式化坐标
const formatCoordinates = (gps) => {
    if (!gps) return '未知';
    if (typeof gps.lat === 'string') {
        return `${parseFloat(gps.lng).toFixed(6) || 0}, ${parseFloat(gps.lat).toFixed(6) || 0}`;
    }
    return `${gps.lng?.toFixed(6) || 0}, ${gps.lat?.toFixed(6) || 0}`;
};

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '未知时间';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// 格式化文件大小
const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 查看原图
const handleViewImage = () => {
    window.open(formData.photo_url, '_blank');
};

// 处理文件上传
const handleFileUpload = (event) => {
    console.log('文件上传事件:', event);
    const file = event.files[0];
    if (file) {
        newPhotoFile.value = file;
    }
    console.log('handleFileUpload->:', newPhotoFile.value);
};

async function onPhotoSelect(event) {
    console.log('onPhotoSelect:', event);
    newPhotoFile.value = event.files[0];
    console.log('newPhotoFile.value:', newPhotoFile.value);
    const exif = await parse(newPhotoFile.value);
    console.log('EXIF data:', exif); // 调试用，查看完整的 EXIF 数据结构
    newPhotoData.name = newPhotoFile.value.name;
    newPhotoData.size = newPhotoFile.value.size;
    newPhotoData.gps = { lng: exif.longitude, lat: exif.latitude };
    newPhotoData.gcj = { lng: wgs84togcj02(exif.longitude, exif.latitude)[0], lat: wgs84togcj02(exif.longitude, exif.latitude)[1] };
    newPhotoData.timestamp = exif.DateTimeOriginal;
    newPhotoData.altitude = exif.GPSAltitude;
    const geoInfoData = await getGeoInfo(exif.longitude, exif.latitude);
    newPhotoData.full_address = geoInfoData.full_address;
    newPhotoData.city = geoInfoData.city;
    console.log('newPhotoData:', newPhotoData);
    console.log('formData', formData);
}

// 确认上传照片，并得到返回的原图 url 和缩略图 url，存入 formData 中
const handleUpLoadPhoto = async () => {
    if (!newPhotoFile.value) return;
    replacingFile.value = true;
    try {
        // 构建FormData对象 - 重命名为formDataObj避免变量遮蔽
        const formDataObj = new FormData();
        formDataObj.append('file', newPhotoFile.value);

        // 构建元数据
        const metadata = {
            name: newPhotoData.name,
            photo_url: '',
            thumbnail_url: '',
            gcj: newPhotoData.gcj,
            custom_gcj: formData.custom_gcj || newPhotoData.gcj
        };

        // 添加元数据到 formDataObj
        const metadataBlob = new Blob([JSON.stringify(metadata)], { type: 'application/json' });
        formDataObj.append('metadata', metadataBlob);

        // 发送请求到后端API
        const response = await fetch('http://localhost:3001/api/image-processor', {
            method: 'POST',
            body: formDataObj
        });

        if (!response.ok) {
            throw new Error(`上传失败: ${response.statusText}`);
        }

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.error || '上传失败');
        }

        // 更新组件级别的formData对象
        formData.photo_url = result.data.photo_url;
        formData.thumbnail_url = result.data.thumbnail_url;

        // 手动触发数据更新
        formData.name = newPhotoData.name || '';
        formData.gps = newPhotoData.gps || '';
        formData.gcj = newPhotoData.gcj || '';
        formData.altitude = newPhotoData.altitude || '';
        formData.full_address = newPhotoData.full_address || '';
        formData.city = newPhotoData.city || '';
        formData.gps_timestamp = newPhotoData.timestamp || '';

        // 关闭对话框并清理
        showReplaceDialog.value = false;
        newPhotoFile.value = null;

        toast.add({ severity: 'success', summary: '成功', detail: '照片已成功替换', life: 3000 });
    } catch (error) {
        console.error('替换照片失败:', error);
        toast.add({ severity: 'error', summary: '错误', detail: '替换照片失败: ' + error.message, life: 3000 });
    } finally {
        replacingFile.value = false;
    }
};

// 提交表单数据
const handleSubmit = async () => {
    try {
        // const isValid = await v$.value.$validate();
        // if (!isValid) {
        //     toast.add({
        //         severity: 'error',
        //         summary: '验证失败',
        //         detail: '请检查表单填写是否正确',
        //         life: 3000
        //     });
        //     return;
        // }
        loading.value = true;
        // 将标签数组转换为逗号分隔的字符串
        const tagString = Array.isArray(formData.tag) ? formData.tag.join(',') : typeof formData.tag === 'string' ? formData.tag : '';

        // 构建提交数据
        const submitData = reactive({
            name: newPhotoData.name,
            tag: tagString,
            collection_id: props.marker.value.collection_id ? props.marker.value.collection_id : '', // 确保 collection_id 存在
            link: formData.link,
            city: newPhotoData.city,
            full_address: newPhotoData.full_address,
            gps: newPhotoData.gps ? newPhotoData.gps : customGCJtogps,
            gcj: newPhotoData.gcj ? newPhotoData.gcj : { lng: 0, lat: 0 },
            custom_gcj: formData.custom_gcj ? formData.custom_gcj : { lng: 0, lat: 0 },
            altitude: newPhotoData.altitude || 0,
            gps_timestamp: newPhotoData.timestamp || 0,
            photo_url: formData.photo_url,
            thumbnail_url: formData.thumbnail_url,
            is_custom: false
        });

        console.log('submitData: ', submitData);
        emit('submit', submitData);
    } catch (error) {
        console.error('提交表单失败:', error);
        toast.add({
            severity: 'error',
            summary: '提交失败',
            detail: '请检查表单填写是否正确',
            life: 3000
        });
    } finally {
        loading.value = false;
    }
};

//=======================================================
// 取消
const handleCancel = () => {
    emit('cancel');
};
</script>

<style lang="scss" scoped>
.photo-info-input {
    padding: 10px;
    .label-group {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .form-group {
        margin-bottom: 15px;

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;

            .required {
                color: red;
            }
        }

        .helper-text {
            display: block;
            color: #666;
            font-size: 0.8rem;
            margin-top: 3px;
        }
    }

    .file-upload-container {
        .file-preview {
            margin-top: 10px;
            display: flex;
            align-items: center;

            .preview-image {
                width: 80px;
                height: 80px;
                object-fit: cover;
                border-radius: 4px;
            }

            .file-info {
                margin-left: 10px;
                display: flex;
                flex-direction: column;

                span {
                    &:first-child {
                        font-weight: 500;
                    }

                    &:last-child {
                        color: #666;
                        font-size: 0.8rem;
                    }
                }
            }
        }
    }

    .location-info {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;

        .coordinates {
            display: flex;
            gap: 15px;
            margin-bottom: 5px;

            span {
                font-family: monospace;
            }
        }

        .address {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #666;

            i {
                color: #ff4d4f;
            }
        }
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
    }

    .photo-preview {
        position: relative;
        margin-bottom: 10px;

        .preview-image {
            width: 40%;
            max-height: 200px;
            object-fit: cover;
            border-radius: 4px;
        }

        .preview-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
        }
    }

    :deep(.p-inputtext),
    :deep(.p-textarea),
    :deep(.p-calendar),
    :deep(.p-chips) {
        width: 100%;
    }
}

.photo-preview-container {
    margin-top: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    background-color: #f9fafb;

    .photo-preview-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;

        .photo-preview-image {
            max-width: 100%;
            height: auto;
            max-height: 300px;
            object-fit: contain;
            border-radius: 4px;
        }

        .photo-preview-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            text-align: center;

            span {
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}
</style>
