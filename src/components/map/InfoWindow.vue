<template>
    <Transition name="slide">
        <div v-if="visible" class="info-window">
            <div class="info-header">
                <h2>{{ title }}</h2>
                <button class="close-btn" @click="close">×</button>
            </div>
            <div class="info-content">
                <!-- All 组件 -->
                <template v-if="listType === 'all'">
                    <InfoListAll
                        v-if="mode === 'list'"
                        :pin-items="pinItems"
                        :photo-items="photoItems"
                        :pano-items="panoItems"
                        @select-item="handleSelectItem"
                        @edit-item="handleEditItem"
                        @delete-items="handleDeleteItems"
                        @locate-marker="$emit('locate-marker', $event)"
                    />
                </template>

                <!-- Pin 组件 -->
                <template v-if="listType === 'pin'">
                    <PinInfoList v-if="mode === 'list'" :items="items" @select-item="handleSelectItem" @edit-item="handleEditItem" @delete-items="handleDeleteItems" />
                    <PinInfoInput v-if="mode === 'input'" :marker="currentMarker" @submit="handleSubmit" @cancel="close" />
                    <PinInfoForm v-if="mode === 'form'" :marker="currentMarker" @update="handleUpdate" @delete="handleDelete" @cancel="close" @back-to-list="handleBackToList" />
                </template>

                <!-- Photo 组件 -->
                <template v-if="listType === 'photo'">
                    <PhotoInfoList v-if="mode === 'list'" :items="items" @select-item="handleSelectItem" @edit-item="handleEditItem" @delete-items="handleDeleteItems" />
                    <PhotoInfoInput v-if="mode === 'input'" :marker="currentMarker" @submit="handleSubmit" @cancel="close" />
                    <PhotoInfoForm v-if="mode === 'form'" :marker="currentMarker" @update="handleUpdate" @delete="handleDelete" @cancel="close" @back-to-list="handleBackToList" @select-item="handleSelectItem" />
                </template>

                <!-- Pano 组件 -->
                <template v-if="listType === 'pano'">
                    <PanoInfoList v-if="mode === 'list'" :items="items" @select-item="handleSelectItem" @edit-item="handleEditItem" @delete-items="handleDeleteItems" />
                    <PanoInfoInput v-if="mode === 'input'" :marker="currentMarker" @submit="handleSubmit" @cancel="close" />
                    <PanoInfoForm v-if="mode === 'form'" :marker="currentMarker" @update="handleUpdate" @delete="handleDelete" @cancel="close" @back-to-list="handleBackToList" />
                </template>

                <!-- Page 组件 - 暂不开发 -->
                <template v-if="listType === 'page'">
                    <div class="page-placeholder">页面标记功能开发中...</div>
                </template>
            </div>

            <!-- 底部操作区域 -->
            <div class="info-footer" v-if="showFooterButtons">
                <!-- 输入模式操作按钮 -->
                <template v-if="mode === 'input'">
                    <Button label="保存" icon="pi pi-check" class="p-button-success" @click="handleInputSave" />
                    <Button label="取消" icon="pi pi-times" class="p-button-secondary" @click="close" />
                </template>

                <!-- 编辑模式操作按钮 -->
                <template v-if="mode === 'form'">
                    <Button label="更新" icon="pi pi-check" class="p-button-success" @click="handleFormUpdate" />
                    <Button label="删除" icon="pi pi-trash" class="p-button-danger" @click="handleFormDelete" />
                    <Button label="取消" icon="pi pi-times" class="p-button-secondary" @click="close" />
                </template>
            </div>
        </div>
    </Transition>
</template>

<script setup>
import { defineProps, defineEmits, computed } from 'vue';
import Button from 'primevue/button';
// 导入各类型组件
import PinInfoList from './pin_info/pin_info_list.vue';
import PinInfoInput from './pin_info/pin_info_input.vue';
import PinInfoForm from './pin_info/pin_info_form.vue';

import PhotoInfoList from './photo_info/photo_info_list.vue';
import PhotoInfoInput from './photo_info/photo_info_input.vue';
import PhotoInfoForm from './photo_info/photo_info_form.vue';

import PanoInfoList from './pano_info/pano_info_list.vue';
import PanoInfoInput from './pano_info/pano_info_input.vue';
import PanoInfoForm from './pano_info/pano_info_form.vue';

// 导入全部标记列表组件
import InfoListAll from './InfoListAll.vue';

// 定义props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    mode: {
        type: String,
        default: 'list', // 'list', 'input', 'form'
        validator: (value) => ['list', 'input', 'form'].includes(value)
    },
    listType: {
        type: String,
        default: 'pin', // 'page', 'pin', 'pano', 'photo', 'all'
        validator: (value) => ['page', 'pin', 'pano', 'photo', 'all'].includes(value)
    },
    items: {
        type: Array,
        default: () => []
    },
    pinItems: {
        type: Array,
        default: () => []
    },
    photoItems: {
        type: Array,
        default: () => []
    },
    panoItems: {
        type: Array,
        default: () => []
    },
    currentMarker: {
        type: Object,
        default: () => ({})
    }
});

// 定义emit
const emit = defineEmits(['close', 'select-item', 'edit-item', 'submit', 'update', 'delete', 'delete-items', 'back-to-list', 'locate-marker']);

// 组件标题
const title = computed(() => {
    if (props.mode === 'list') {
        const typeTitles = {
            page: '页面标记列表',
            pin: '引脚标记列表',
            pano: '全景标记列表',
            photo: '照片标记列表',
            all: '所有标记列表'
        };
        return typeTitles[props.listType] || '标记列表';
    } else if (props.mode === 'input') {
        return '添加新标记';
    } else if (props.mode === 'form') {
        return '编辑标记信息';
    }
    return '信息窗口';
});

// 是否显示底部按钮
const showFooterButtons = computed(() => {
    return props.mode === 'input' || props.mode === 'form';
});

// 方法
const close = () => {
    emit('close');
};

const handleSelectItem = (item) => {
    emit('select-item', item);
};

const handleEditItem = (item) => {
    emit('edit-item', item);
};

const handleSubmit = (formData) => {
    emit('submit', formData);
};

const handleUpdate = (formData) => {
    emit('update', formData);
};

const handleDelete = (markerId) => {
    console.log('InfoWindow: 收到删除事件，标记ID:', markerId);
    emit('delete', markerId);
};

const handleDeleteItems = (markerIds) => {
    console.log('InfoWindow: 收到批量删除事件，标记IDs:', markerIds);
    emit('delete-items', markerIds);
};

const handleBackToList = () => {
    console.log('InfoWindow: 返回列表');
    emit('back-to-list');
};

// 底部操作区域方法
const handleInputSave = () => {
    // 获取当前模式下的表单组件并提交
    let selector = '';

    switch (props.listType) {
        case 'pin':
            selector = '.pin-info-input form';
            break;
        case 'photo':
            selector = '.photo-info-input form';
            break;
        case 'pano':
            selector = '.pano-info-input form';
            break;
        case 'all':
            console.warn('不支持对"所有标记"类型使用保存操作');
            return;
        case 'page':
            console.warn('页面标记功能尚未实现');
            return;
        default:
            console.warn(`未知的标记类型: ${props.listType}`);
            return;
    }

    console.log(`尝试触发表单提交，选择器: ${selector}`);
    const form = document.querySelector(selector);
    if (form) {
        const submitEvent = new Event('submit', { cancelable: true });
        form.dispatchEvent(submitEvent);
    } else {
        console.warn(`未找到表单元素: ${selector}`);
    }
};

const handleFormUpdate = () => {
    // 获取当前模式下的表单组件并提交
    let selector = '';

    switch (props.listType) {
        case 'pin':
            selector = '.pin-info-form form';
            break;
        case 'photo':
            selector = '.photo-info-form form';
            break;
        case 'pano':
            selector = '.pano-info-form form';
            break;
        case 'all':
            console.warn('不支持对"所有标记"类型使用更新操作');
            return;
        case 'page':
            console.warn('页面标记功能尚未实现');
            return;
        default:
            console.warn(`未知的标记类型: ${props.listType}`);
            return;
    }

    console.log(`尝试触发表单更新，选择器: ${selector}, 当前标记类型: ${props.listType}`);
    const form = document.querySelector(selector);
    if (form) {
        const submitEvent = new Event('submit', { cancelable: true });
        form.dispatchEvent(submitEvent);
    } else {
        console.warn(`未找到表单元素: ${selector}`);
    }
};

const handleFormDelete = () => {
    // 触发当前标记的删除操作
    emit('delete', props.currentMarker.id);
};
</script>

<style lang="scss" scoped>
.info-window {
    position: fixed;
    top: 0;
    right: 0;
    width: 40%;
    height: 100vh;
    background-color: #fff;
    box-shadow: -4px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1022;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.info-header {
    padding: 2px 10px;
    background-color: #f0f0f0;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
        margin: 0;
        font-size: 1rem;
        color: #333;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        line-height: 1;
        cursor: pointer;
        color: #666;

        &:hover {
            color: #ff0000;
        }
    }
}

.info-content {
    flex: 1;
    overflow-y: auto;
    padding: 0px 4px;
}

.info-footer {
    height: 5vh;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 0 16px;
    z-index: 10;
}

.page-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #666;
    font-style: italic;
}

.slide-enter-active,
.slide-leave-active {
    transition: transform 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
    transform: translateX(100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .info-window {
        width: 80%;
    }
}
</style>
