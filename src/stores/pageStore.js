import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { getAllPageDatas, getPageDataById, createPageData, updatePageData, deletePageData, deletePageDatas } from '../services/pageService.js';

export const usePageStore = defineStore('page', () => {
    // 状态
    // 在 Amap.vue 中，点击 marker 时，设置的 currentMarker
    const currentMarker = ref(null);
    const currentPage = ref(null);
    const pageMarker = ref(null);
    const markersInPage = ref([]);
    const markerList = ref([]);
    const editor = ref(null);
    // 当前文章中所有 marker 的 id
    const currentPageMarkersID = ref([]);
    // 当前文章中所有 marker
    const currentPageMarkers = computed(() => {
        return markerList.value.filter((marker) => currentPageMarkersID.value.includes(marker.id));
    });
    const pages = ref([]);
    const error = ref(null);
    //=================================================
    // 解析 editor 中的 content 内容，获得 pageMarker中相关 node 下的所有 node 对应的 marker
    //=================================================

    // Getters
    const allPages = computed(() => pages.value);
    const selectedPage = computed(() => currentPage.value);
    const getCurrentMarker = computed(() => currentMarker.value);
    const getPageMarker = computed(() => pageMarker.value);
    const getMarkersInPage = computed(() => markersInPage.value);
    const getMarkerList = computed(() => markerList.value);
    const getCurrentPageMarkers = computed(() => currentPageMarkers.value);
    const getFirstImageUrl = computed(() => {
        if (!editor.value) return '';
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = editor.value.options.element.innerHTML;

        // 查找第一个 img 标签
        const firstImg = tempDiv.querySelector('img');

        // 如果找到图片，则获取其 src 属性
        if (firstImg) {
            return firstImg.getAttribute('src');
        } else {
            return ''; // 如果没有图片，则设置为空字符串
        }
    });

    // Actions
    function setCurrentMarker(marker) {
        currentMarker.value = marker;
    }
    function setPageMarker(marker) {
        pageMarker.value = marker;
    }
    function fetchMarkersInPage(markersIds) {
        //TODO: 从数据库获取 PageMarker 中相关的 markers id 数组，然后将这些 references markers 添加到 markersInPage 中
        markersInPage.value = markersIds;
    }
    async function fetchPageMarkerById(id) {
        try {
            const result = await getPageDataById(id);
            if (result.success) {
                currentPage.value = result.data;
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            void 0;
        }
    }

    function addMarkerInPage(marker) {
        markersInPage.value.push(marker);
    }
    function removeMarkerInPage(markerId) {
        markersInPage.value = markersInPage.value.filter((m) => m.id !== markerId);
    }
    async function addPageMarker(pageData) {
        //TODO: 保存 PageMarker 到数据库
        try {
            const result = await createPageData(pageData);
            if (result.success) {
                pages.value.unshift(result.data);
                return result.data; // 返回新创建的页面标记数据
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            null;
        }
    }
    async function updatePageMarker(id, pageData) {
        //TODO: 更新 PageMarker 到数据库
        try {
            const result = await updatePageData(id, pageData);
            if (result.success) {
                const index = pages.value.findIndex((page) => page.id === id);
                if (index !== -1) {
                    pages.value.splice(index, 1, result.data);
                }
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            null;
        }
    }
    async function fetchAllPages() {
        //TODO: 从数据库获取所有 PageMarker
        try {
            const result = await getAllPageDatas();
            if (result.success) {
                pages.value = result.data;
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            null;
        }
    }
    function deletePageMarker(markerId) {
        //TODO: 删除 PageMarker 到数据库
    }
    function deletePageMarkers(markerIds) {
        //TODO: 删除多个 PageMarker 到数据库
    }

    function addMarker(marker) {
        markerList.value.push(marker);
    }

    function removeMarker(markerId) {
        markerList.value = markerList.value.filter((m) => m.id !== markerId);
    }

    function setEditor(editorInstance) {
        editor.value = editorInstance;
    }
    function setPageContent(content) {
        if (!editor.value) return;
        editor.value.commands.setContent(content);
    }
    // 在编辑器当前位置插入 node-image 节点
    function insertImageNode() {
        if (!editor.value || !currentMarker.value) return;

        const { state, commands } = editor.value;
        const { selection } = state;
        const node = selection.node || selection.$head.parent;

        if (node && node.type && node.type.name === 'nodeImage') {
            // 更新当前 nodeImage 节点的属性
            commands.updateAttributes('nodeImage', {
                marker: currentMarker.value,
                src: currentMarker.value.photo_url || null,
                alt: currentMarker.value.name || '照片预览',
                marker_id: currentMarker.value.id,
                marker_type: 'photo'
            });
        } else {
            // 插入新节点
            commands.insertContent({
                type: 'nodeImage',
                attrs: {
                    marker: currentMarker.value,
                    src: currentMarker.value.photo_url || null,
                    alt: currentMarker.value.name || '照片预览',
                    marker_id: currentMarker.value.id,
                    marker_type: 'photo'
                }
            });
        }
    }

    return {
        // 状态
        currentMarker,
        markerList,
        editor,
        currentPageMarkers,

        // Getters
        allPages,
        selectedPage,
        getCurrentMarker,
        getMarkerList,
        getFirstImageUrl,
        getPageMarker,
        getMarkersInPage,
        getCurrentPageMarkers,

        // Actions
        setCurrentMarker,
        addMarker,
        removeMarker,
        setEditor,
        insertImageNode,
        setPageMarker,
        fetchMarkersInPage,
        addMarkerInPage,
        removeMarkerInPage,
        addPageMarker,
        updatePageMarker,
        deletePageMarker,
        deletePageMarkers,
        setPageContent,
        fetchAllPages,
        fetchPageMarkerById
    };
});
