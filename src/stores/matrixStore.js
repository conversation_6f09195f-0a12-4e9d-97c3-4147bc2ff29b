import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import * as matrixService from '../services/matrixService.js';

/**
 * 全景矩阵状态管理Store
 * 用于管理1km网格全景标记的状态、显示切换和缓存
 */
export const useMatrixStore = defineStore('matrix', () => {
    // 状态
    const matrixMarkers = ref([]);
    const isVisible = ref(false);
    const isGenerating = ref(false);
    const error = ref(null);
    const boundaryData = ref(null); // 武汉三环边界数据缓存
    const gridConfig = ref({
        gridSize: 1000, // 默认1km网格
        bounds: null // 网格边界范围
    });

    // Getters
    const allMatrixMarkers = computed(() => matrixMarkers.value);
    const visibleMatrixMarkers = computed(() => (isVisible.value ? matrixMarkers.value : []));
    const isMatrixVisible = computed(() => isVisible.value);
    const isGeneratingMatrix = computed(() => isGenerating.value);
    const getError = computed(() => error.value);
    const getBoundaryData = computed(() => boundaryData.value);
    const getGridConfig = computed(() => gridConfig.value);

    // 按状态过滤的计算属性
    const plannedMarkers = computed(() => matrixMarkers.value.filter((marker) => marker.status === 'planned'));
    const capturedMarkers = computed(() => matrixMarkers.value.filter((marker) => marker.status === 'captured'));

    // Actions

    /**
     * 设置矩阵标记数据
     * @param {Array} markers - 矩阵标记数组
     */
    function setMatrixMarkers(markers) {
        matrixMarkers.value = markers;
    }

    /**
     * 添加单个矩阵标记
     * @param {Object} marker - 矩阵标记对象
     */
    function addMatrixMarker(marker) {
        matrixMarkers.value.push(marker);
    }

    /**
     * 更新矩阵标记
     * @param {string} id - 标记ID
     * @param {Object} updatedData - 更新的数据
     */
    function updateMatrixMarker(id, updatedData) {
        const index = matrixMarkers.value.findIndex((marker) => marker.id === id);
        if (index !== -1) {
            matrixMarkers.value[index] = { ...matrixMarkers.value[index], ...updatedData };
        }
    }

    /**
     * 删除矩阵标记
     * @param {string} id - 标记ID
     */
    function removeMatrixMarker(id) {
        matrixMarkers.value = matrixMarkers.value.filter((marker) => marker.id !== id);
    }

    /**
     * 清空所有矩阵标记
     */
    function clearMatrixMarkers() {
        matrixMarkers.value = [];
    }

    /**
     * 切换矩阵显示状态
     */
    function toggleVisibility() {
        isVisible.value = !isVisible.value;
    }

    /**
     * 设置矩阵显示状态
     * @param {boolean} visible - 是否显示
     */
    function setVisibility(visible) {
        isVisible.value = visible;
    }

    /**
     * 设置生成状态
     * @param {boolean} generating - 是否正在生成
     */
    function setGenerating(generating) {
        isGenerating.value = generating;
    }

    /**
     * 设置错误信息
     * @param {string} errorMessage - 错误信息
     */
    function setError(errorMessage) {
        error.value = errorMessage;
    }

    /**
     * 清除错误信息
     */
    function clearError() {
        error.value = null;
    }

    /**
     * 设置边界数据缓存
     * @param {Object} data - 边界数据
     */
    function setBoundaryData(data) {
        boundaryData.value = data;
    }

    /**
     * 设置网格配置
     * @param {Object} config - 网格配置
     */
    function setGridConfig(config) {
        gridConfig.value = { ...gridConfig.value, ...config };
    }

    /**
     * 按网格索引获取标记
     * @param {number} x - X轴网格索引
     * @param {number} y - Y轴网格索引
     * @returns {Object|null} 矩阵标记对象
     */
    function getMarkerByGridIndex(x, y) {
        return matrixMarkers.value.find((marker) => marker.gridIndex.x === x && marker.gridIndex.y === y);
    }

    /**
     * 获取指定区域内的标记
     * @param {Object} bounds - 边界对象 {north, south, east, west}
     * @returns {Array} 区域内的矩阵标记数组
     */
    function getMarkersInBounds(bounds) {
        return matrixMarkers.value.filter((marker) => {
            const { lng, lat } = marker.gcj; // 使用 gcj 坐标系进行地理查询
            return lng >= bounds.west && lng <= bounds.east && lat >= bounds.south && lat <= bounds.north;
        });
    }

    // 数据库集成方法

    /**
     * 从数据库加载矩阵标记
     * @returns {Promise<boolean>} 是否成功加载
     */
    async function loadMatrixMarkersFromDb() {
        try {
            setGenerating(true);
            clearError();

            const result = await matrixService.getAllMatrixMarkers();
            if (result.success) {
                const frontendMarkers = matrixService.convertDbMarkersToFrontendFormat(result.data);
                setMatrixMarkers(frontendMarkers);
                return true;
            } else {
                setError(result.error);
                return false;
            }
        } catch (error) {
            console.error('从数据库加载矩阵标记失败:', error);
            setError(error.message);
            return false;
        } finally {
            setGenerating(false);
        }
    }

    /**
     * 保存矩阵标记到数据库
     * @param {Array} gridPoints - 网格点数组
     * @returns {Promise<boolean>} 是否成功保存
     */
    async function saveMatrixMarkersToDb(gridPoints) {
        try {
            setGenerating(true);
            clearError();

            // 转换为数据库格式
            const dbMarkers = matrixService.convertGridPointsToDbFormat(gridPoints);

            // 批量保存到数据库
            const result = await matrixService.createMatrixMarkers(dbMarkers);
            if (result.success) {
                console.log(`成功保存 ${result.data.length} 个矩阵标记到数据库`);
                return true;
            } else {
                setError(result.error);
                return false;
            }
        } catch (error) {
            console.error('保存矩阵标记到数据库失败:', error);
            setError(error.message);
            return false;
        } finally {
            setGenerating(false);
        }
    }

    /**
     * 检查数据库中是否已有矩阵数据
     * @returns {Promise<boolean>} 是否存在数据
     */
    async function checkDbDataExists() {
        try {
            const result = await matrixService.checkMatrixDataExists();
            return result.success ? result.exists : false;
        } catch (error) {
            console.error('检查数据库数据失败:', error);
            return false;
        }
    }

    /**
     * 更新矩阵标记状态（同步到数据库）
     * @param {string} id - 标记ID
     * @param {string} status - 新状态
     * @returns {Promise<boolean>} 是否成功更新
     */
    async function updateMarkerStatusInDb(id, status) {
        try {
            const result = await matrixService.updateMatrixMarkerStatus(id, status);
            if (result.success) {
                // 同步更新本地状态
                updateMatrixMarker(id, { status });
                return true;
            } else {
                setError(result.error);
                return false;
            }
        } catch (error) {
            console.error('更新标记状态失败:', error);
            setError(error.message);
            return false;
        }
    }

    /**
     * 获取矩阵统计信息
     * @returns {Promise<Object|null>} 统计信息对象
     */
    async function getMatrixStatistics() {
        try {
            const result = await matrixService.getMatrixStatistics();
            return result.success ? result.data : null;
        } catch (error) {
            console.error('获取矩阵统计信息失败:', error);
            return null;
        }
    }

    /**
     * 清空数据库中的所有矩阵数据
     * @returns {Promise<boolean>} 是否成功清空
     */
    async function clearDbMatrixData() {
        try {
            const result = await matrixService.clearAllMatrixMarkers();
            if (result.success) {
                clearMatrixMarkers(); // 同时清空本地缓存
                return true;
            } else {
                setError(result.error);
                return false;
            }
        } catch (error) {
            console.error('清空数据库矩阵数据失败:', error);
            setError(error.message);
            return false;
        }
    }

    return {
        // 状态
        matrixMarkers,
        isVisible,
        isGenerating,
        error,
        boundaryData,
        gridConfig,

        // Getters
        allMatrixMarkers,
        visibleMatrixMarkers,
        isMatrixVisible,
        isGeneratingMatrix,
        getError,
        getBoundaryData,
        getGridConfig,
        plannedMarkers,
        capturedMarkers,

        // Actions
        setMatrixMarkers,
        addMatrixMarker,
        updateMatrixMarker,
        removeMatrixMarker,
        clearMatrixMarkers,
        toggleVisibility,
        setVisibility,
        setGenerating,
        setError,
        clearError,
        setBoundaryData,
        setGridConfig,
        getMarkerByGridIndex,
        getMarkersInBounds,

        // 数据库集成方法
        loadMatrixMarkersFromDb,
        saveMatrixMarkersToDb,
        checkDbDataExists,
        updateMarkerStatusInDb,
        getMatrixStatistics,
        clearDbMatrixData
    };
});
