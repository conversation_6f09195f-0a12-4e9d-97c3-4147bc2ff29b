import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { getAllPhotoMarkers, getPhotoMarkerById, createPhotoMarker, updatePhotoMarker, deletePhotoMarker, deletePhotoMarkers } from '../services/photoService.js';

export const usePhotoStore = defineStore('photo', () => {
    // 状态
    const photos = ref([]);
    const currentPhoto = ref(null);
    const loading = ref(false);
    const error = ref(null);

    // Getter
    const allPhotos = computed(() => photos.value);
    const selectedPhoto = computed(() => currentPhoto.value);
    const isLoading = computed(() => loading.value);
    const getError = computed(() => error.value);

    // Actions
    async function fetchAllPhotos() {
        loading.value = true;
        try {
            const result = await getAllPhotoMarkers();
            if (result.success) {
                photos.value = result.data;
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    async function fetchPhotoById(id) {
        loading.value = true;
        try {
            const result = await getPhotoMarkerById(id);
            if (result.success) {
                currentPhoto.value = result.data;
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    async function addPhoto(photoData) {
        loading.value = true;
        try {
            const result = await createPhotoMarker(photoData);
            if (result.success) {
                photos.value.unshift(result.data);
                return result.data; // 返回新创建的照片标记数据
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    async function updatePhoto({ id, photoData }) {
        loading.value = true;
        try {
            const result = await updatePhotoMarker(id, photoData);
            if (result.success) {
                const index = photos.value.findIndex((photo) => photo.id === id);
                if (index !== -1) {
                    photos.value.splice(index, 1, result.data);
                }
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    async function removePhoto(id) {
        loading.value = true;
        try {
            const result = await deletePhotoMarker(id);
            if (result.success) {
                photos.value = photos.value.filter((photo) => photo.id !== id);
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    async function removePhotos(ids) {
        loading.value = true;
        try {
            const result = await deletePhotoMarkers(ids);
            if (result.success) {
                photos.value = photos.value.filter((photo) => !ids.includes(photo.id));
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    function clearError() {
        error.value = null;
    }

    return {
        // 状态
        photos,
        currentPhoto,
        loading,
        error,

        // Getter
        allPhotos,
        selectedPhoto,
        isLoading,
        getError,

        // Actions
        fetchAllPhotos,
        fetchPhotoById,
        addPhoto,
        updatePhoto,
        removePhoto,
        removePhotos,
        clearError
    };
});
