import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { getAllPanoMarkers, getPanoMarkerById, createPanoMarker, updatePanoMarker, deletePanoMarker, deletePanoMarkers } from '../services/panoService.js';

export const usePanoStore = defineStore('pano', () => {
    // 状态
    const panos = ref([]);
    const currentPano = ref(null);
    const loading = ref(false);
    const error = ref(null);

    // Getter
    const allPanos = computed(() => panos.value);
    const selectedPano = computed(() => currentPano.value);
    const isLoading = computed(() => loading.value);
    const getError = computed(() => error.value);

    // Actions
    async function fetchAllPanos() {
        loading.value = true;
        try {
            const result = await getAllPanoMarkers();
            if (result.success) {
                panos.value = result.data;
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    async function fetchPanoById(id) {
        loading.value = true;
        try {
            const result = await getPanoMarkerById(id);
            if (result.success) {
                currentPano.value = result.data;
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    async function addPano(panoData) {
        loading.value = true;
        try {
            const result = await createPanoMarker(panoData);
            if (result.success) {
                panos.value.unshift(result.data);
                return result.data; // 返回新创建的照片标记数据
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    async function updatePano({ id, panoData }) {
        loading.value = true;
        try {
            const result = await updatePanoMarker(id, panoData);
            if (result.success) {
                const index = panos.value.findIndex((pano) => pano.id === id);
                if (index !== -1) {
                    panos.value.splice(index, 1, result.data);
                }
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    async function removePano(id) {
        loading.value = true;
        try {
            const result = await deletePanoMarker(id);
            if (result.success) {
                panos.value = panos.value.filter((pano) => pano.id !== id);
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    async function removePanos(ids) {
        loading.value = true;
        try {
            const result = await deletePanoMarkers(ids);
            if (result.success) {
                panos.value = panos.value.filter((pano) => !ids.includes(pano.id));
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    function clearError() {
        error.value = null;
    }

    return {
        // 状态
        panos,
        currentPano,
        loading,
        error,

        // Getter
        allPanos,
        selectedPano,
        isLoading,
        getError,

        // Actions
        fetchAllPanos,
        fetchPanoById,
        addPano,
        updatePano,
        removePano,
        removePanos,
        clearError
    };
});
