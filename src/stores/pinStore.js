import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { getAllPinMarkers, getPinMarkerById, createPinMarker, updatePinMarker, deletePinMarker, deletePinMarkers } from '../services/pinService.js';

export const usePinStore = defineStore('pin', () => {
    // 状态
    const pins = ref([]);
    const currentPin = ref(null);
    const loading = ref(false);
    const error = ref(null);

    // Getter
    const allPins = computed(() => pins.value);
    const selectedPin = computed(() => currentPin.value);
    const isLoading = computed(() => loading.value);
    const getError = computed(() => error.value);

    // Actions
    async function fetchAllPins() {
        loading.value = true;
        try {
            const result = await getAllPinMarkers();
            if (result.success) {
                pins.value = result.data;
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    async function fetchPinById(id) {
        loading.value = true;
        try {
            const result = await getPinMarkerById(id);
            if (result.success) {
                currentPin.value = result.data;
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    async function addPin(pinData) {
        loading.value = true;
        try {
            const result = await createPinMarker(pinData);
            if (result.success) {
                pins.value.unshift(result.data);
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    async function updatePin({ id, pinData }) {
        loading.value = true;
        try {
            const result = await updatePinMarker(id, pinData);
            if (result.success) {
                const index = pins.value.findIndex((pin) => pin.id === id);
                if (index !== -1) {
                    pins.value.splice(index, 1, result.data);
                }
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    async function removePin(id) {
        loading.value = true;
        try {
            const result = await deletePinMarker(id);
            if (result.success) {
                pins.value = pins.value.filter((pin) => pin.id !== id);
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    async function removePins(ids) {
        loading.value = true;
        try {
            const result = await deletePinMarkers(ids);
            if (result.success) {
                pins.value = pins.value.filter((pin) => !ids.includes(pin.id));
            } else {
                error.value = result.error;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    }

    function clearError() {
        error.value = null;
    }

    return {
        // 状态
        pins,
        currentPin,
        loading,
        error,

        // Getter
        allPins,
        selectedPin,
        isLoading,
        getError,

        // Actions
        fetchAllPins,
        fetchPinById,
        addPin,
        updatePin,
        removePin,
        removePins,
        clearError
    };
});
