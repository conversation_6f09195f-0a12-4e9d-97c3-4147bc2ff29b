import { defineStore } from 'pinia';
import { reactive } from 'vue';
import mapService from '@/services/mapService';

export const useMapStore = defineStore('map', () => {
    // 位置选择模式状态
    const locationPickerMode = reactive({
        active: false,
        message: '',
        callback: null
    });

    // 新增：响应式 map 对象
    const mapOptions = reactive({
        zoom: 11 // 默认缩放级别
        // 你可以加其他属性
    });

    // 防抖函数
    const debounce = (fn, delay = 300) => {
        let timer = null;
        return function (...args) {
            if (timer) clearTimeout(timer);
            timer = setTimeout(() => {
                fn.apply(this, args);
                timer = null;
            }, delay);
        };
    };

    // 设置位置选择模式
    const setLocationPickerMode = (options) => {
        locationPickerMode.active = options.active;
        locationPickerMode.message = options.message || '';

        // 使用防抖包装回调函数
        if (options.callback) {
            locationPickerMode.callback = debounce(options.callback, 100);
        } else {
            locationPickerMode.callback = null;
        }
    };

    // 获取地图实例 - 直接代理到 mapService
    const getMapInstance = () => {
        return mapService.getMap();
    };

    // 异步创建并获取地图实例 - 直接代理到 mapService
    const createMapInstance = async (containerId, options) => {
        return await mapService.createMap(containerId, options);
    };

    // 新增：设置 map 实例和 zoom
    const fetchMapOptions = (mapInstance) => {
        // 监听地图缩放事件
        mapInstance.on('zoomchange', () => {
            mapOptions.zoom = mapInstance.getZoom();
        });
    };

    return {
        locationPickerMode,
        getMapInstance,
        createMapInstance,
        setLocationPickerMode,
        mapOptions, // 暴露 map
        fetchMapOptions // 暴露设置方法
    };
});
