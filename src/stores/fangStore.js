import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import * as fangService from '@/services/fangService.js';

/**
 * 房产数据 Pinia Store
 * 管理房产数据的状态和操作
 */
export const useFangStore = defineStore('fang', () => {
    // 状态变量
    const allFangData = ref([]);
    const currentFangData = ref(null);
    const filteredFangData = ref([]);
    const districtList = ref([]);
    const locationList = ref([]);
    const statistics = ref({});
    const loading = ref(false);
    const error = ref(null);
    
    // 筛选条件
    const filters = ref({
        district: '',
        location: '',
        minPrice: null,
        maxPrice: null,
        keyword: ''
    });
    
    // 分页信息
    const pagination = ref({
        page: 1,
        pageSize: 50,
        total: 0
    });

    // 计算属性
    const hasData = computed(() => allFangData.value.length > 0);
    const totalCount = computed(() => allFangData.value.length);
    const isFiltered = computed(() => {
        return filters.value.district || 
               filters.value.location || 
               filters.value.minPrice !== null || 
               filters.value.maxPrice !== null || 
               filters.value.keyword;
    });

    // Actions

    /**
     * 获取所有房产数据
     */
    async function fetchAllFangData(options = {}) {
        try {
            loading.value = true;
            error.value = null;
            
            const result = await fangService.getAllFangData({
                ...filters.value,
                limit: options.limit || pagination.value.pageSize,
                offset: options.offset || (pagination.value.page - 1) * pagination.value.pageSize
            });
            
            if (result.success) {
                allFangData.value = result.data;
                pagination.value.total = result.count;
                return result.data;
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            error.value = err.message;
            console.error('获取房产数据失败:', err);
            throw err;
        } finally {
            loading.value = false;
        }
    }

    /**
     * 根据ID获取房产数据
     */
    async function fetchFangDataById(id) {
        try {
            loading.value = true;
            error.value = null;
            
            const result = await fangService.getFangDataById(id);
            
            if (result.success) {
                currentFangData.value = result.data;
                return result.data;
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            error.value = err.message;
            console.error('获取房产数据失败:', err);
            throw err;
        } finally {
            loading.value = false;
        }
    }

    /**
     * 搜索房产数据
     */
    async function searchFangData(keyword, options = {}) {
        try {
            loading.value = true;
            error.value = null;
            
            const result = await fangService.searchFangData(keyword, options);
            
            if (result.success) {
                filteredFangData.value = result.data;
                return result.data;
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            error.value = err.message;
            console.error('搜索房产数据失败:', err);
            throw err;
        } finally {
            loading.value = false;
        }
    }

    /**
     * 获取区域列表
     */
    async function fetchDistrictList() {
        try {
            const result = await fangService.getDistrictList();
            
            if (result.success) {
                districtList.value = result.data;
                return result.data;
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            error.value = err.message;
            console.error('获取区域列表失败:', err);
            throw err;
        }
    }

    /**
     * 获取指定区域的位置列表
     */
    async function fetchLocationsByDistrict(district) {
        try {
            const result = await fangService.getLocationsByDistrict(district);
            
            if (result.success) {
                locationList.value = result.data;
                return result.data;
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            error.value = err.message;
            console.error('获取位置列表失败:', err);
            throw err;
        }
    }

    /**
     * 获取统计信息
     */
    async function fetchStatistics() {
        try {
            const result = await fangService.getFangDataStatistics();
            
            if (result.success) {
                statistics.value = result.data;
                return result.data;
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            error.value = err.message;
            console.error('获取统计信息失败:', err);
            throw err;
        }
    }

    /**
     * 导入 CSV 数据
     */
    async function importCsvData(csvFilePath) {
        try {
            loading.value = true;
            error.value = null;
            
            const result = await fangService.importFangDataFromCsv(csvFilePath);
            
            if (result.success) {
                // 导入成功后重新获取数据
                await fetchAllFangData();
                await fetchStatistics();
                return result.data;
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            error.value = err.message;
            console.error('导入 CSV 数据失败:', err);
            throw err;
        } finally {
            loading.value = false;
        }
    }

    /**
     * 创建房产数据
     */
    async function createFangData(fangData) {
        try {
            loading.value = true;
            error.value = null;
            
            const result = await fangService.createFangData(fangData);
            
            if (result.success) {
                // 添加到本地数据
                allFangData.value.unshift(result.data);
                return result.data;
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            error.value = err.message;
            console.error('创建房产数据失败:', err);
            throw err;
        } finally {
            loading.value = false;
        }
    }

    /**
     * 更新房产数据
     */
    async function updateFangData(id, updateData) {
        try {
            loading.value = true;
            error.value = null;
            
            const result = await fangService.updateFangData(id, updateData);
            
            if (result.success) {
                // 更新本地数据
                const index = allFangData.value.findIndex(item => item.id === id);
                if (index !== -1) {
                    allFangData.value[index] = result.data;
                }
                
                if (currentFangData.value && currentFangData.value.id === id) {
                    currentFangData.value = result.data;
                }
                
                return result.data;
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            error.value = err.message;
            console.error('更新房产数据失败:', err);
            throw err;
        } finally {
            loading.value = false;
        }
    }

    /**
     * 删除房产数据
     */
    async function deleteFangData(id) {
        try {
            loading.value = true;
            error.value = null;
            
            const result = await fangService.deleteFangData(id);
            
            if (result.success) {
                // 从本地数据中移除
                const index = allFangData.value.findIndex(item => item.id === id);
                if (index !== -1) {
                    allFangData.value.splice(index, 1);
                }
                
                if (currentFangData.value && currentFangData.value.id === id) {
                    currentFangData.value = null;
                }
                
                return true;
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            error.value = err.message;
            console.error('删除房产数据失败:', err);
            throw err;
        } finally {
            loading.value = false;
        }
    }

    /**
     * 清空所有数据
     */
    async function clearAllData() {
        try {
            loading.value = true;
            error.value = null;
            
            const result = await fangService.clearAllFangData();
            
            if (result.success) {
                // 清空本地数据
                allFangData.value = [];
                filteredFangData.value = [];
                currentFangData.value = null;
                statistics.value = {};
                return true;
            } else {
                throw new Error(result.error);
            }
        } catch (err) {
            error.value = err.message;
            console.error('清空数据失败:', err);
            throw err;
        } finally {
            loading.value = false;
        }
    }

    /**
     * 设置筛选条件
     */
    function setFilters(newFilters) {
        filters.value = { ...filters.value, ...newFilters };
    }

    /**
     * 重置筛选条件
     */
    function resetFilters() {
        filters.value = {
            district: '',
            location: '',
            minPrice: null,
            maxPrice: null,
            keyword: ''
        };
    }

    /**
     * 设置分页信息
     */
    function setPagination(newPagination) {
        pagination.value = { ...pagination.value, ...newPagination };
    }

    /**
     * 设置当前房产数据
     */
    function setCurrentFangData(fangData) {
        currentFangData.value = fangData;
    }

    /**
     * 清除错误信息
     */
    function clearError() {
        error.value = null;
    }

    return {
        // 状态
        allFangData,
        currentFangData,
        filteredFangData,
        districtList,
        locationList,
        statistics,
        loading,
        error,
        filters,
        pagination,
        
        // 计算属性
        hasData,
        totalCount,
        isFiltered,
        
        // 方法
        fetchAllFangData,
        fetchFangDataById,
        searchFangData,
        fetchDistrictList,
        fetchLocationsByDistrict,
        fetchStatistics,
        importCsvData,
        createFangData,
        updateFangData,
        deleteFangData,
        clearAllData,
        setFilters,
        resetFilters,
        setPagination,
        setCurrentFangData,
        clearError
    };
});
