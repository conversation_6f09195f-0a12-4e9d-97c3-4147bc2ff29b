/**
 * @fileoverview MatrixMarker 数据结构定义
 * @description 用于武汉三环内全景图矩阵标记功能的数据结构
 */

/**
 * MatrixMarker 数据结构
 * @typedef {Object} MatrixMarker
 * @property {string} id - 矩阵标记的唯一标识符，格式为 'matrix_x_y'，其中 x 和 y 对应 gridIndex 的坐标值
 * @property {string} create_time - 创建时间（ISO字符串格式）
 * @property {string} update_time - 更新时间（ISO字符串格式）
 * @property {Position} custom_gcj - 自定义GCJ-02坐标系下的位置信息
 * @property {Position} gps - GPS原始坐标系下的位置信息
 * @property {Position} gcj - 标准GCJ-02坐标系下的位置信息
 * @property {GridIndex} grid_index - 网格索引信息
 * @property {'matrix'} type - 标记类型标识，固定值 'matrix'
 * @property {'planned'|'captured'} status - 拍摄状态，'planned' 表示计划拍摄，'captured' 表示已拍摄
 * @property {number} grid_size - 网格大小（单位：米），默认1000
 * @property {string} boundary_region - 所属区域，默认 'wuhan_3rd_ring'
 * @property {Object|null} metadata - 额外元数据
 * @property {string|null} captured_time - 实际拍摄时间（ISO字符串格式）
 * @property {string|null} pano_url - 关联的全景图片URL地址
 * @property {string|null} notes - 备注信息
 */

/**
 * 坐标位置信息
 * @typedef {Object} Position
 * @property {number} lng - 经度
 * @property {number} lat - 纬度
 */

/**
 * 网格索引信息
 * @typedef {Object} GridIndex
 * @property {number} x - X轴网格索引
 * @property {number} y - Y轴网格索引
 */

/**
 * 示例 MatrixMarker 对象
 * @example
 * // 计划拍摄的矩阵标记示例
 * const plannedMarker = {
 *   id: 'matrix_5_3',
 *   create_time: '2024-01-15T08:30:00.000Z',
 *   update_time: '2024-01-15T08:30:00.000Z',
 *   custom_gcj: {
 *     lng: 114.3054,
 *     lat: 30.5928
 *   },
 *   gps: {
 *     lng: 114.2954,
 *     lat: 30.5878
 *   },
 *   gcj: {
 *     lng: 114.3054,
 *     lat: 30.5928
 *   },
 *   grid_index: {
 *     x: 5,
 *     y: 3
 *   },
 *   type: 'matrix',
 *   status: 'planned',
 *   grid_size: 1000,
 *   boundary_region: 'wuhan_3rd_ring',
 *   metadata: null,
 *   captured_time: null,
 *   pano_url: null,
 *   notes: null
 * };
 *
 * // 已拍摄的矩阵标记示例
 * const capturedMarker = {
 *   id: 'matrix_10_7',
 *   create_time: '2024-01-15T08:30:00.000Z',
 *   update_time: '2024-01-15T14:25:00.000Z',
 *   custom_gcj: {
 *     lng: 114.2945,
 *     lat: 30.5847
 *   },
 *   gps: {
 *     lng: 114.2845,
 *     lat: 30.5797
 *   },
 *   gcj: {
 *     lng: 114.2945,
 *     lat: 30.5847
 *   },
 *   grid_index: {
 *     x: 10,
 *     y: 7
 *   },
 *   type: 'matrix',
 *   status: 'captured',
 *   grid_size: 1000,
 *   boundary_region: 'wuhan_3rd_ring',
 *   metadata: { photographer: 'user123', weather: 'sunny' },
 *   captured_time: '2024-01-15T14:25:00.000Z',
 *   pano_url: 'https://example.com/pano/matrix_10_7.jpg',
 *   notes: '高质量全景图，视野清晰'
 * };
 */

/**
 * 验证对象是否为有效的 MatrixMarker
 * @param {any} obj - 要验证的对象
 * @returns {boolean} 如果对象符合 MatrixMarker 结构返回 true，否则返回 false
 */
export function isValidMatrixMarker(obj) {
    if (!obj || typeof obj !== 'object') {
        return false;
    }

    // 验证必需字段存在
    if (!obj.id || !obj.custom_gcj || !obj.gps || !obj.gcj || !obj.grid_index || !obj.type || !obj.status) {
        return false;
    }

    // 验证 id 格式
    if (typeof obj.id !== 'string' || !obj.id.startsWith('matrix_')) {
        return false;
    }

    // 验证 type 字段
    if (obj.type !== 'matrix') {
        return false;
    }

    // 验证 status 字段
    if (obj.status !== 'planned' && obj.status !== 'captured') {
        return false;
    }

    // 验证坐标对象
    const validatePosition = (pos) => {
        return pos && typeof pos.lng === 'number' && typeof pos.lat === 'number';
    };

    if (!validatePosition(obj.custom_gcj) || !validatePosition(obj.gps) || !validatePosition(obj.gcj)) {
        return false;
    }

    // 验证 grid_index 对象
    if (typeof obj.grid_index.x !== 'number' || typeof obj.grid_index.y !== 'number') {
        return false;
    }

    // 验证 grid_size
    if (obj.grid_size && typeof obj.grid_size !== 'number') {
        return false;
    }

    return true;
}

/**
 * 从网格坐标生成标记ID
 * @param {number} x - X轴坐标
 * @param {number} y - Y轴坐标
 * @returns {string} 格式化的标记ID
 */
export function generateMatrixId(x, y) {
    return `matrix_${x}_${y}`;
}

/**
 * 创建新的 MatrixMarker 对象
 * @param {Object} options - 创建选项
 * @param {number} options.x - X轴网格索引
 * @param {number} options.y - Y轴网格索引
 * @param {Position} options.custom_gcj - 自定义GCJ-02坐标
 * @param {Position} options.gps - GPS坐标
 * @param {Position} options.gcj - 标准GCJ-02坐标
 * @param {string} [options.status='planned'] - 状态
 * @param {number} [options.grid_size=1000] - 网格大小
 * @param {string} [options.boundary_region='wuhan_3rd_ring'] - 所属区域
 * @param {Object} [options.metadata] - 元数据
 * @param {string} [options.notes] - 备注
 * @returns {MatrixMarker} 新的 MatrixMarker 对象
 */
export function createMatrixMarker({ x, y, custom_gcj, gps, gcj, status = 'planned', grid_size = 1000, boundary_region = 'wuhan_3rd_ring', metadata = null, notes = null }) {
    const now = new Date().toISOString();

    return {
        id: generateMatrixId(x, y),
        create_time: now,
        update_time: now,
        custom_gcj,
        gps,
        gcj,
        grid_index: { x, y },
        type: 'matrix',
        status,
        grid_size,
        boundary_region,
        metadata,
        captured_time: null,
        pano_url: null,
        notes
    };
}

// JSDoc typedef 定义不需要export，它们在整个项目中自动可用
