<template>
    <div class="grid grid-cols-12 gap-8">
        <div class="col-span-full lg:col-span-6">
            <FileUpload name="geo-images" mode="advanced" custom-upload multiple accept="image/*" :maxFileSize="5000000" @uploader="handleUploader">
                <template #empty>
                    <div class="flex items-center justify-center flex-col p-4">
                        <i class="pi pi-cloud-upload text-4xl mb-2" />
                        <p class="text-gray-500">拖放图片或点击上传</p>
                    </div>
                </template>
            </FileUpload>
            <Toast />
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import { useToast } from 'primevue/usetoast';
import { parse } from 'exifr';
import { wgs84togcj02 } from '@/utils/coordinate';
import axios from 'axios';
import AMapLoader from '@amap/amap-jsapi-loader';

const toast = useToast();
const emit = defineEmits(['upload-complete', 'upload']);

const handleUploader = async (event) => {
    try {
        const processingQueue = event.files.map(async (file) => {
            try {
                // 解析EXIF数据
                const exif = await parse(file);

                // 检查EXIF数据中是否包含有效的经纬度信息
                if (!exif?.longitude || !exif?.latitude || isNaN(exif.longitude) || isNaN(exif.latitude) || exif.longitude < -180 || exif.longitude > 180 || exif.latitude < -90 || exif.latitude > 90) {
                    console.error('图片未包含有效GPS信息:', file.name);
                    // 使用Toast显示具体哪个文件没有GPS信息
                    toast.add({ severity: 'error', summary: '上传失败', detail: `文件 ${file.name} 未包含有效GPS信息`, life: 5000 });
                    throw new Error('图片未包含有效GPS信息');
                }

                const [gcjLng, gcjLat] = wgs84togcj02(exif.longitude, exif.latitude);

                // 检查坐标转换结果是否有效
                if (isNaN(gcjLng) || isNaN(gcjLat)) {
                    console.error('坐标转换失败:', { original: [exif.longitude, exif.latitude], converted: [gcjLng, gcjLat] });
                    // 使用Toast显示坐标转换失败的文件
                    toast.add({ severity: 'error', summary: '上传失败', detail: `文件 ${file.name} 坐标转换失败`, life: 5000 });
                    throw new Error('坐标转换失败');
                }

                // 获取地理信息
                // 确保AMap已加载后再创建地理编码器
                window._AMapSecurityConfig = { securityJsCode: '05b99346f0c80233a92e6c1d6b56a9f3' };
                const AMap = await AMapLoader.load({
                    key: 'f30bebf0ca8200ba9080214e826ff768',
                    version: '2.0',
                    plugins: ['AMap.Geocoder']
                });

                const geocoder = new AMap.Geocoder();
                const address = await new Promise((resolve) => {
                    geocoder.getAddress([gcjLng, gcjLat], (status, result) => {
                        console.log('地理编码状态:', status, '坐标:', [gcjLng, gcjLat]);
                        if (status !== 'complete') {
                            console.error('地理编码失败:', status);
                            toast.add({ severity: 'warn', summary: '地理信息获取', detail: `文件 ${file.name} 地理信息获取不完整`, life: 3000 });
                        }
                        resolve(status === 'complete' ? result : null);
                    });
                });

                // 添加更详细的日志
                console.log('坐标信息:', {
                    original: [exif.longitude, exif.latitude],
                    converted: [gcjLng, gcjLat]
                });

                // 添加日志以便调试
                console.log('地理编码结果:', address);
                console.log('地理编码结果结构:', address ? Object.keys(address) : 'null', '地址组件:', address?.regeocode?.addressComponent ? Object.keys(address.regeocode.addressComponent) : 'null');

                // 构建元数据
                const metadata = {
                    original_lng: exif.longitude,
                    original_lat: exif.latitude,
                    gcj_lng: gcjLng,
                    gcj_lat: gcjLat,
                    timestamp: new Date(exif.DateTimeOriginal).toISOString(),
                    altitude: exif.GPSAltitude,
                    city: address?.regeocode?.addressComponent?.city || address?.regeocode?.addressComponent?.province,
                    full_address: address?.regeocode?.formattedAddress,
                    file_name: file.name
                };

                // 确保使用原始File对象
                const rawFile = file instanceof File ? file : file.file;

                const formData = new FormData();
                formData.append('file', rawFile);
                formData.append(
                    'metadata',
                    new Blob([JSON.stringify(metadata)], {
                        type: 'application/json'
                    })
                );

                // 在发送请求前添加验证
                if (!formData.has('file') || !formData.has('metadata')) {
                    console.error('FormData字段缺失:', {
                        hasFile: formData.has('file'),
                        hasMetadata: formData.has('metadata')
                    });
                    toast.add({ severity: 'error', summary: '上传失败', detail: `文件 ${file.name} 请求构造异常`, life: 5000 });
                    throw new Error('请求构造异常');
                }

                console.log('发送的FormData内容:');
                for (const [key, value] of formData.entries()) {
                    console.log(key, value instanceof Blob ? `Blob(${value.type})` : value);
                }

                const response = await axios.post('http://localhost:3001/api/image-processor', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });

                if (response.status === 409) {
                    console.log(`文件 ${file.name} 已存在于服务器`);
                    return null;
                } else if (response.status !== 200) {
                    toast.add({ severity: 'error', summary: '上传失败', detail: `文件 ${file.name} 后端处理失败`, life: 5000 });
                    throw new Error('后端处理失败');
                }
                return response.data;
            } catch (error) {
                // 记录错误信息
                console.error('文件处理错误:', error.message, '文件名:', file.name);
                // 不再抛出错误，而是返回null，这样其他文件仍然可以继续处理
                return null;
            }
        });

        // 过滤掉处理失败的文件（返回null的项）
        const results = (await Promise.all(processingQueue)).filter((result) => result !== null);

        if (results.length > 0) {
            emit('upload', results);
            toast.add({ severity: 'success', summary: '上传完成', detail: `成功上传 ${results.length} 个文件`, life: 3000 });
        } else {
            toast.add({ severity: 'warn', summary: '上传结果', detail: '没有文件成功上传', life: 3000 });
        }
    } catch (error) {
        console.error('文件处理错误:', error.message);
        toast.add({ severity: 'error', summary: '上传失败', detail: error.message, life: 5000 });
    }
};
</script>
