<template>
    <!-- 容器元素需要设置尺寸，否则全景画面无法显示 -->
    <div id="panorama-equirect" class="panorama-viewer"></div>
</template>

<script setup>
// 确保在应用中已全局引入 pannellum.css 和 pannellum.js。如果未全局引入，也可在此处引入
import { onMounted, onBeforeUnmount } from 'vue';
import { usePanoStore } from '@/stores/panoStore';

const panoStore = usePanoStore();
const currentPanoMarker = panoStore.currentPanoMarker;
let viewerInstance;
onMounted(() => {
    // 初始化 Pannellum Viewer，当容器已挂载到 DOM 后进行
    viewerInstance = window.pannellum.viewer('panorama-equirect', {
        type: 'equirectangular',
        panorama: currentPanoMarker.pano_url, // 全景图像路径，可以是静态文件路径或线上URL
        autoLoad: true, // 自动加载全景图像
        compass: true, // 显示指南针（假如已提供北偏角参数）
        // 可选：初始视角设置，例如 pitch (俯仰角), yaw (水平旋转角), hfov (水平视野角度)
        // pitch: 10,
        // yaw: 180,
        // hfov: 110,
        hotSpots: [
            // 示例：添加一些信息热点
            {
                pitch: 14.1,
                yaw: 1.5,
                type: 'info',
                text: '这里是天花板'
                // 可以加 URL: 'https://example.com' 来在点击时打开链接
            },
            {
                pitch: -9.4,
                yaw: 222.6,
                type: 'info',
                text: '这里是地面'
            }
        ]
    });
});

onBeforeUnmount(() => {
    // 组件卸载前销毁 viewer 实例，释放资源
    if (viewerInstance) {
        viewerInstance.destroy();
    }
});
</script>

<style scoped>
.panorama-viewer {
    width: 100%;
    height: 500px;
    position: relative;
    flex-grow: 1;
}
</style>
