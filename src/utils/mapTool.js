/**
 * 地图相关工具函数
 */
import mapService from '@/services/mapService';
import { ICON_SVG } from '@/config/svg';

let geocoder = null;
let info = {
    full_address: '',
    adcode: '',
    city: '',
    province: '',
    district: '',
    township: '',
    towncode: '',
    street: '',
    street_number: '',
    neighborhood: '',
    building: '',
    building_type: ''
};

/**
 * 根据经纬度获取地址信息
 * @param {number} lng - 经度
 * @param {number} lat - 纬度
 * @returns {Promise<object|string>} - 返回地址对象，或错误信息
 */
export const getGeoInfo = async (lng, lat) => {
    try {
        // 确保 AMap 已加载
        const AMap = await mapService.getAMap();
        if (!AMap) {
            console.error('高德地图 API 未加载');
            return '地址获取失败: 地图 API 未加载';
        }

        // 初始化 geocoder
        if (!geocoder) {
            geocoder = new AMap.Geocoder();
        }

        // 执行地理编码
        const result = await new Promise((resolve, reject) => {
            geocoder.getAddress([lng, lat], (status, result) => {
                status === 'complete' ? resolve(result) : reject(new Error('地理编码失败'));
            });
        });

        // 获得逆地理编码结果
        if (result.info === 'OK' && result.regeocode.formattedAddress) {
            info.full_address = result.regeocode.formattedAddress;
            info.adcode = result.regeocode.addressComponent.adcode;
            info.city = result.regeocode.addressComponent.city;
            info.province = result.regeocode.addressComponent.province;
            info.district = result.regeocode.addressComponent.district;
            info.township = result.regeocode.addressComponent.township;
            info.towncode = result.regeocode.addressComponent.towncode;
            info.street = result.regeocode.addressComponent.street;
            info.street_number = result.regeocode.addressComponent.streetNumber;
            info.neighborhood = result.regeocode.addressComponent.neighborhood;
            info.building = result.regeocode.addressComponent.building;
            info.building_type = result.regeocode.addressComponent.buildingType;
            return info;
        } else {
            return '地址获取失败';
        }
    } catch (error) {
        console.error('获取地址时出错:', error);
        return '地址获取失败';
    }
};

/**
 * 根据经纬度获取地址信息
 * @param {number} lng - 经度
 * @param {number} lat - 纬度
 * @returns {Promise<object|string>} - 返回地址对象，或错误信息
 */
export const getGeoInfos = async (lnglats) => {
    try {
        // 确保 AMap 已加载
        const AMap = await mapService.getAMap();
        if (!AMap) {
            console.error('高德地图 API 未加载');
            return '地址获取失败: 地图 API 未加载';
        }

        // 初始化 geocoder
        if (!geocoder) {
            geocoder = new AMap.Geocoder();
        }

        // 执行地理编码
        const result = await new Promise((resolve, reject) => {
            geocoder.getAddress(lnglats, (status, result) => {
                status === 'complete' ? resolve(result) : reject(new Error('地理编码失败'));
            });
        });

        // 获得逆地理编码结果
        if (result.info === 'OK' && result.regeocodes.length > 0) {
            const infos = [];
            result.regeocodes.forEach((regeocode) => {
                infos.push({
                    full_address: regeocode.formattedAddress,
                    adcode: regeocode.addressComponent.adcode,
                    city: regeocode.addressComponent.city,
                    province: regeocode.addressComponent.province,
                    district: regeocode.addressComponent.district,
                    township: regeocode.addressComponent.township,
                    towncode: regeocode.addressComponent.towncode,
                    street: regeocode.addressComponent.street,
                    street_number: regeocode.addressComponent.streetNumber,
                    neighborhood: regeocode.addressComponent.neighborhood,
                    building: regeocode.addressComponent.building,
                    building_type: regeocode.addressComponent.buildingType
                });
            });
            return infos;
        } else {
            return '地址获取失败';
        }
    } catch (error) {
        console.error('获取地址时出错:', error);
        return '地址获取失败';
    }
};

/**
 * 在地图上设置标记
 * @param {number} lng - 经度
 * @param {number} lat - 纬度
 * @param {string} [id='temp-marker'] - 标记ID，用于后续管理
 * @returns {Promise<boolean>} - 是否成功添加标记
 */
export const setMarker = async (lng, lat, id = 'temp-marker') => {
    try {
        // 直接从 mapService 获取 map 实例，避免使用 Promise
        const map = mapService.getMap();
        const AMap = mapService.getAMap();

        if (!map || !AMap) {
            console.error('地图实例不存在，无法添加标记');
            return false;
        }

        // 创建图标对象，设置大小为 24x24
        const icon = new AMap.Icon({
            image: ICON_SVG.LOCATIONDB,
            imageSize: new AMap.Size(24, 24)
        });

        // 使用 mapService 的 addMarker 方法添加标记
        const marker = mapService.addMarker(id, {
            position: [lng, lat],
            icon: icon,
            anchor: 'center',
            title: '照片指定位置',
            animation: 'AMAP_ANIMATION_DROP'
        });

        console.log('标记已添加:', marker);
        return true;
    } catch (error) {
        console.error('添加标记失败:', error);
        return false;
    }
};

/**
 * 移除地图上的标记
 * @param {string} [id='temp-marker'] - 要移除的标记ID
 * @returns {boolean} - 是否成功移除标记
 */
export const removeMarker = (id = 'temp-marker') => {
    try {
        return mapService.removeMarker(id);
    } catch (error) {
        console.error('移除标记失败:', error);
        return false;
    }
};
