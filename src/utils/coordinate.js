export function wgs84togcj02(lng, lat) {
    const a = 6378245.0;
    const ee = 0.00669342162296594323;

    if (lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271) {
        return [lng, lat];
    }

    function transformlat(lng, lat) {
        let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
        ret += ((20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0) / 3.0;
        ret += ((20.0 * Math.sin(lat * Math.PI) + 40.0 * Math.sin((lat / 3.0) * Math.PI)) * 2.0) / 3.0;
        ret += ((160.0 * Math.sin((lat / 12.0) * Math.PI) + 320 * Math.sin((lat * Math.PI) / 30.0)) * 2.0) / 3.0;
        return ret;
    }

    function transformlng(lng, lat) {
        let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
        ret += ((20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0) / 3.0;
        ret += ((20.0 * Math.sin(lng * Math.PI) + 40.0 * Math.sin((lng / 3.0) * Math.PI)) * 2.0) / 3.0;
        ret += ((150.0 * Math.sin((lng / 12.0) * Math.PI) + 300.0 * Math.sin((lng / 30.0) * Math.PI)) * 2.0) / 3.0;
        return ret;
    }

    let dlat = transformlat(lng - 105.0, lat - 35.0);
    let dlng = transformlng(lng - 105.0, lat - 35.0);
    const radlat = (lat / 180.0) * Math.PI;
    let magic = Math.sin(radlat);
    magic = 1 - ee * magic * magic;
    const sqrtmagic = Math.sqrt(magic);
    dlat = (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * Math.PI);
    dlng = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * Math.PI);
    const mglat = lat + dlat;
    const mglng = lng + dlng;
    return [mglng, mglat];
}

export function gcj02towgs84(lng, lat) {
    // 如果在中国境外，直接返回
    if (lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271) {
        return [lng, lat];
    }

    let initDelta = 0.01;
    let threshold = 0.000001;
    let dLat = initDelta,
        dLng = initDelta;
    let mLat = lat - dLat,
        mLng = lng - dLng;
    let pLat = lat + dLat,
        pLng = lng + dLng;
    let wgsLat, wgsLng;

    for (let i = 0; i < 30; i++) {
        wgsLat = (mLat + pLat) / 2;
        wgsLng = (mLng + pLng) / 2;
        let tmp = wgs84togcj02(wgsLng, wgsLat);
        dLat = tmp[1] - lat;
        dLng = tmp[0] - lng;
        if (Math.abs(dLat) < threshold && Math.abs(dLng) < threshold) {
            return [wgsLng, wgsLat];
        }
        if (dLat > 0) {
            pLat = wgsLat;
        } else {
            mLat = wgsLat;
        }
        if (dLng > 0) {
            pLng = wgsLng;
        } else {
            mLng = wgsLng;
        }
    }

    return [wgsLng, wgsLat];
}
