import { ref } from 'vue';
import { ICON_SVG } from '@/config/svg';
//import { pinStore, photoStore, panoStore } from '@/stores';

export const useMapMarkers = () => {
    const markers = ref([]);
    const currentMarker = ref(null);
    let map = null;
    // const mapState = {
    //     infoWindowVisible,
    //     infoWindowMode,
    //     currentMarker,
    //     infoWindowListType
    // };
    // 初始化地图实例
    const initMap = (mapInstance) => {
        map = mapInstance;
    };

    // 标记加载函数
    const loadPinMarkers = async (isInitialLoad = false) => {
        try {
            const pinStore = usePinStore();

            // 只有在初始加载时才清除现有的pin markers
            if (isInitialLoad) {
                clearPinMarkers();
                await pinStore.fetchAllPins();
            }

            // 使用Pinia store加载pin标记
            const pinData = pinStore.allPins;

            // 如果是初始加载，直接添加所有标记
            if (isInitialLoad) {
                pinData.forEach((item) => {
                    addPinMarkerToMap(item);
                });
            } else {
                // 增量更新：比较现有标记和新数据
                updatePinMarkers(pinData);
            }

            return pinData;
        } catch (error) {
            console.error('加载 pin marker 失败:', error);
            throw error;
        }
    };

    const loadPhotoMarkers = async (isInitialLoad = false) => {
        try {
            const photoStore = usePhotoStore();

            // 只有在初始加载时才清除现有的photo markers
            if (isInitialLoad) {
                clearPhotoMarkers();
                await photoStore.fetchAllPhotos();
            }

            // 使用Pinia store加载photo标记
            const data = photoStore.allPhotos;

            // 如果是初始加载，直接添加所有标记
            if (isInitialLoad) {
                // 同时在地图上创建标记
                if (data && data.length > 0) {
                    data.forEach((item) => {
                        addPhotoMarkerToMap(item);
                    });
                }
            } else {
                // 增量更新：比较现有标记和新数据
                updatePhotoMarkers(data);
            }

            return data;
        } catch (error) {
            console.error('加载照片标记失败:', error);
            throw error;
        }
    };

    const loadPanoMarkers = async (isInitialLoad = false) => {
        try {
            // 只有在初始加载时才清除
            if (isInitialLoad) {
                clearPanoMarkers();
            }

            // 使用Pinia store加载pano标记
            const panoStore = usePanoStore();
            await panoStore.fetchAllPanos();
            const data = panoStore.allPanos;

            // 将标记添加到地图
            if (data && data.length > 0) {
                data.forEach((item) => {
                    addPanoMarkerToMap(item);
                });
            }

            return data;
        } catch (error) {
            console.error('加载全景标记失败:', error);
            throw error;
        }
    };

    // 标记添加函数
    const addPinMarker = (lnglat, AMap, markerData) => {
        // 创建临时标记数据
        const tempMarkerData = markerData || {
            gcj: {
                lng: null,
                lat: null
            },
            gps: {
                lng: null,
                lat: null
            },
            custom_gcj: {
                lng: lnglat.getLng(),
                lat: lnglat.getLat()
            },
            name: 'pin marker',
            icon: 'PIN'
        };

        return tempMarkerData;
    };

    const addPhotoMarker = (lnglat, AMap) => {
        // 创建临时图标对象
        const icon = new AMap.Icon({
            image: 'ICON_SVG.CAMERA', // 这里需要在组件中传入ICON_SVG
            imageSize: new AMap.Size(24, 24)
        });

        const marker = new AMap.Marker({
            position: [lnglat.getLng(), lnglat.getLat()],
            icon: icon,
            // 将锚点设置为中心，使图标中心与坐标点对齐
            anchor: 'center',
            title: '新照片标记'
        });

        // 创建信息窗体
        const infoWindow = new AMap.InfoWindow({
            isCustom: false,
            content: `<div>
                <p>经度：${lnglat.getLng()}</p>
                <p>纬度：${lnglat.getLat()}</p>
            </div>`,
            offset: new AMap.Pixel(0, -30)
        });

        // 添加marker点击事件
        marker.on('mouseover', () => infoWindow.open(map, marker.getPosition()));
        marker.on('mouseout', () => infoWindow.close());

        marker.setMap(map);
        markers.value.push(marker);

        // 返回当前标记数据
        return {
            position: [lnglat.getLng(), lnglat.getLat()],
            originalGps: null
        };
    };

    const addPanoMarker = (lnglat, AMap) => {
        // 创建临时图标对象
        const icon = new AMap.Icon({
            image: 'ICON_SVG.LOCATIONDB', // 这里需要在组件中传入ICON_SVG
            imageSize: new AMap.Size(24, 24)
        });

        const marker = new AMap.Marker({
            position: [lnglat.getLng(), lnglat.getLat()],
            icon: icon,
            // 将锚点设置为中心，使图标中心与坐标点对齐
            anchor: 'center',
            title: '新全景标记',
            extData: {
                isPano: true,
                icon: 'LOCATIONDB'
            }
        });

        // 创建信息窗体
        const infoWindow = new AMap.InfoWindow({
            isCustom: false,
            content: `<div>
                <p>经度：${lnglat.getLng()}</p>
                <p>纬度：${lnglat.getLat()}</p>
            </div>`,
            offset: new AMap.Pixel(0, -30)
        });

        // 添加marker点击事件
        marker.on('mouseover', () => infoWindow.open(map, marker.getPosition()));
        marker.on('mouseout', () => infoWindow.close());

        marker.setMap(map);
        markers.value.push(marker);

        // 返回当前标记数据
        return {
            position: [lnglat.getLng(), lnglat.getLat()],
            custom_gcj: {
                lng: lnglat.getLng(),
                lat: lnglat.getLat()
            },
            icon: 'LOCATIONDB',
            isPano: true
        };
    };

    // 将标记添加到地图的具体实现
    function addPinMarkerToMap(item) {
        if (!map) {
            console.error('Map instance not initialized');
            return null;
        }
        // 根据 icon 类型选择不同的图标
        let iconUrl = ICON_SVG.PIN; // 默认图标

        // 如果 item.icon 存在且在 ICON_SVG 中有对应的值，使用该图标
        if (item.icon && ICON_SVG[item.icon]) {
            iconUrl = ICON_SVG[item.icon];
        }

        // 创建图标对象，设置大小为 24x24
        const icon = new window.AMap.Icon({
            image: iconUrl,
            imageSize: new window.AMap.Size(24, 24)
        });

        const marker = new window.AMap.Marker({
            position: [item.custom_gcj ? item.custom_gcj.lng : item.gcj.lng, item.custom_gcj ? item.custom_gcj.lat : item.gcj.lat],
            icon: icon,
            // 将锚点设置为中心，使图标中心与坐标点对齐
            anchor: 'center',
            title: item.name || '未命名标记',
            // 存储标记数据，便于后续引用
            extData: item
        });

        // 创建信息窗体
        const infoWindow = new window.AMap.InfoWindow({
            isCustom: false,
            content: `<div>
                <h3>${item.name || '未命名标记'}</h3>
                <p>${item.full_address || '无地址信息'}</p>
            </div>`,
            offset: new window.AMap.Pixel(0, -30)
        });

        // 添加marker事件
        marker.on('mouseover', () => infoWindow.open(map, marker.getPosition()));
        marker.on('mouseout', () => infoWindow.close());

        // 点击marker时打开编辑表单
        marker.on('click', () => {
            if (mapState.infoWindowVisible.value && mapState.infoWindowMode.value === 'form') {
                mapState.infoWindowVisible.value = false;
                setTimeout(() => {
                    mapState.currentMarker.value = marker.getExtData();
                    mapState.infoWindowMode.value = 'form';
                    mapState.infoWindowListType.value = 'pin';
                    mapState.infoWindowVisible.value = true;
                }, 10);
            } else {
                mapState.currentMarker.value = marker.getExtData();
                mapState.infoWindowMode.value = 'form';
                mapState.infoWindowListType.value = 'pin';
                mapState.infoWindowVisible.value = true;
            }
        });

        marker.setMap(map);
        markers.value.push(marker);

        console.log(item);
        return marker;
    }

    const addPhotoMarkerToMap = (item) => {
        if (!map) return null;

        // 创建图标对象，使用缩略图
        const icon = new window.AMap.Icon({
            image: item.thumbnail_url,
            imageSize: new window.AMap.Size(40, 40)
        });

        const marker = new window.AMap.Marker({
            position: [item.gcj.lng, item.gcj.lat],
            icon: icon,
            // 将锚点设置为中心，使图标中心与坐标点对齐
            anchor: 'center',
            title: item.name || '未命名照片',
            // 存储标记数据，便于后续引用
            extData: item
        });

        // 创建信息窗体
        const infoWindow = new window.AMap.InfoWindow({
            isCustom: false,
            content: `<div>
                <h3>${item.name || '未命名照片'}</h3>
                <p>${item.full_address || '无地址信息'}</p>
            </div>`,
            offset: new window.AMap.Pixel(0, -30)
        });

        // 添加marker事件
        marker.on('mouseover', () => infoWindow.open(map, marker.getPosition()));
        marker.on('mouseout', () => infoWindow.close());

        marker.setMap(map);
        markers.value.push(marker);

        return marker;
    };

    const addPanoMarkerToMap = (item) => {
        if (!map) return null;

        // 创建图标对象
        const icon = new window.AMap.Icon({
            image: 'ICON_SVG.LOCATIONDB', // 这里需要在组件中处理ICON_SVG
            size: new window.AMap.Size(24, 24),
            imageSize: new window.AMap.Size(24, 24)
        });

        const marker = new window.AMap.Marker({
            position: [item.gcj.lng, item.gcj.lat],
            icon: icon,
            anchor: 'center',
            title: item.name || '未命名全景',
            extData: item
        });

        // 创建信息窗体
        const infoWindow = new window.AMap.InfoWindow({
            isCustom: false,
            content: `<div>
                <h3>${item.name || '未命名全景'}</h3>
                <p>${item.full_address || '无地址信息'}</p>
            </div>`,
            offset: new window.AMap.Pixel(0, -30)
        });

        // 添加marker事件
        marker.on('mouseover', () => infoWindow.open(map, marker.getPosition()));
        marker.on('mouseout', () => infoWindow.close());

        marker.setMap(map);
        markers.value.push(marker);

        return marker;
    };

    // 标记更新函数
    const updatePinMarkers = (newData) => {
        return;
    };

    return {
        addPinMarkerToMap
    };
};
