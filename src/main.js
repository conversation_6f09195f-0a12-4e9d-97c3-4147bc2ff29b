import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import { createPinia } from 'pinia';
import Aura from '@primevue/themes/aura';
import PrimeVue from 'primevue/config';
import ConfirmationService from 'primevue/confirmationservice';
import ToastService from 'primevue/toastservice';
import DialogService from 'primevue/dialogservice';

import '@/assets/styles.scss';
import '@/assets/tailwind.css';
import '@/assets/cursors.css';
import 'pannellum/build/pannellum.css';
import 'pannellum/build/pannellum.js';

const app = createApp(App);
const pinia = createPinia();
app.use(router);
app.use(pinia);
app.use(PrimeVue, { theme: { preset: Aura, options: { darkModeSelector: '.app-dark' } } });
app.use(ToastService);
app.use(ConfirmationService);
app.use(DialogService);
// app.use(TlbsMap);

app.mount('#app');
