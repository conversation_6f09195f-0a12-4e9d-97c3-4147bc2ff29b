<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>地理编码调试测试</title>
    <style>
        html, body {
            height: 100%;
            width: 100%;
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .control-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #40a9ff;
        }
        
        .btn:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .status.success { background: #f6ffed; border: 1px solid #b7eb8f; color: #52c41a; }
        .status.error { background: #fff2f0; border: 1px solid #ffccc7; color: #ff4d4f; }
        .status.info { background: #e6f7ff; border: 1px solid #91d5ff; color: #1890ff; }
        
        .debug-log {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .test-section {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin: 15px 0;
        }
        
        #map-container {
            height: 300px;
            width: 100%;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="control-panel">
            <h2>地理编码API调试测试</h2>
            <p>逐步测试各个API的功能，定位问题所在</p>
            
            <div>
                <button class="btn" onclick="checkAPIStatus()">1. 检查API状态</button>
                <button class="btn" onclick="testRestAPI()">2. 测试REST API</button>
                <button class="btn" onclick="testJSGeocoder()">3. 测试JS Geocoder</button>
                <button class="btn" onclick="testPlaceSearch()">4. 测试PlaceSearch</button>
                <button class="btn" onclick="clearLog()">清除日志</button>
            </div>
            
            <div id="status" class="status info">
                准备就绪，请按顺序点击测试按钮
            </div>
        </div>
        
        <div class="test-section">
            <h3>调试日志</h3>
            <div id="debugLog" class="debug-log">等待测试开始...\n</div>
        </div>
        
        <div id="map-container"></div>
    </div>

    <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=f30bebf0ca8200ba9080214e826ff768&plugin=AMap.Geocoder,AMap.PlaceSearch"></script>
    <script type="text/javascript">
        const AMAP_WEB_KEY = 'f30bebf0ca8200ba9080214e826ff768';
        const AMAP_REST_KEY = '7a5917e97482a3cb8b2c1631b7cd9971';
        
        let map;
        let geocoder;
        let placeSearch;
        let logElement;
        
        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            
            if (!logElement) {
                logElement = document.getElementById('debugLog');
            }
            
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logMessage);
            
            // 更新状态
            updateStatus(message, type);
        }
        
        // 更新状态
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        // 清除日志
        function clearLog() {
            document.getElementById('debugLog').textContent = '日志已清除...\n';
        }
        
        // 1. 检查API状态
        function checkAPIStatus() {
            log('开始检查API状态...');
            
            // 检查AMap对象
            if (typeof AMap === 'undefined') {
                log('错误：AMap对象未定义，地图API未正确加载', 'error');
                return;
            }
            log('✓ AMap对象已加载');
            
            // 检查插件
            if (typeof AMap.Geocoder === 'undefined') {
                log('错误：AMap.Geocoder插件未加载', 'error');
            } else {
                log('✓ AMap.Geocoder插件已加载');
            }
            
            if (typeof AMap.PlaceSearch === 'undefined') {
                log('错误：AMap.PlaceSearch插件未加载', 'error');
            } else {
                log('✓ AMap.PlaceSearch插件已加载');
            }
            
            // 初始化地图
            try {
                if (!map) {
                    map = new AMap.Map('map-container', {
                        resizeEnable: true,
                        center: [114.298572, 30.584355],
                        zoom: 11
                    });
                    log('✓ 地图初始化成功');
                } else {
                    log('✓ 地图已存在');
                }
            } catch (error) {
                log(`地图初始化失败: ${error.message}`, 'error');
            }
            
            // 初始化Geocoder
            try {
                geocoder = new AMap.Geocoder({
                    city: '武汉市',
                    radius: 5000
                });
                log('✓ Geocoder初始化成功');
            } catch (error) {
                log(`Geocoder初始化失败: ${error.message}`, 'error');
            }
            
            // 初始化PlaceSearch
            try {
                placeSearch = new AMap.PlaceSearch({
                    city: '027',
                    pageSize: 10
                });
                log('✓ PlaceSearch初始化成功');
            } catch (error) {
                log(`PlaceSearch初始化失败: ${error.message}`, 'error');
            }
            
            log('API状态检查完成', 'success');
        }
        
        // 2. 测试REST API
        async function testRestAPI() {
            log('开始测试REST API...');
            
            const testAddress = '武汉市江夏区文化大道';
            
            try {
                const url = `https://restapi.amap.com/v3/geocode/geo?key=${AMAP_REST_KEY}&address=${encodeURIComponent(testAddress)}`;
                log(`请求URL: ${url}`);
                
                const response = await fetch(url);
                const data = await response.json();
                
                log(`REST API响应: ${JSON.stringify(data, null, 2)}`);
                
                if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
                    const location = data.geocodes[0].location.split(',');
                    const lng = parseFloat(location[0]);
                    const lat = parseFloat(location[1]);
                    
                    log(`✓ REST API成功: 坐标 ${lng}, ${lat}`, 'success');
                    
                    // 在地图上添加标记
                    if (map) {
                        const marker = new AMap.Marker({
                            position: [lng, lat],
                            title: 'REST API结果',
                            content: '<div style="background: #52c41a; color: white; padding: 5px 8px; border-radius: 50%; font-size: 12px;">REST</div>'
                        });
                        map.add(marker);
                        map.setCenter([lng, lat]);
                    }
                } else {
                    log(`REST API失败: ${data.info || '未知错误'}`, 'error');
                }
            } catch (error) {
                log(`REST API请求失败: ${error.message}`, 'error');
            }
        }
        
        // 3. 测试JavaScript Geocoder
        function testJSGeocoder() {
            log('开始测试JavaScript Geocoder...');
            
            if (!geocoder) {
                log('错误：Geocoder未初始化', 'error');
                return;
            }
            
            const testAddress = '武汉市江夏区文化大道';
            log(`测试地址: ${testAddress}`);
            
            geocoder.getLocation(testAddress, (status, result) => {
                log(`Geocoder回调 - 状态: ${status}`);
                log(`Geocoder回调 - 结果: ${JSON.stringify(result, null, 2)}`);
                
                if (status === 'complete' && result.geocodes && result.geocodes.length > 0) {
                    const location = result.geocodes[0].location;
                    log(`✓ JavaScript Geocoder成功: 坐标 ${location.lng}, ${location.lat}`, 'success');
                    
                    // 在地图上添加标记
                    if (map) {
                        const marker = new AMap.Marker({
                            position: [location.lng, location.lat],
                            title: 'JS Geocoder结果',
                            content: '<div style="background: #ff4d4f; color: white; padding: 5px 8px; border-radius: 50%; font-size: 12px;">JS</div>'
                        });
                        map.add(marker);
                    }
                } else {
                    log(`JavaScript Geocoder失败: 状态=${status}`, 'error');
                    if (result && result.info) {
                        log(`错误信息: ${result.info}`, 'error');
                    }
                }
            });
        }
        
        // 4. 测试PlaceSearch
        function testPlaceSearch() {
            log('开始测试PlaceSearch...');
            
            if (!placeSearch) {
                log('错误：PlaceSearch未初始化', 'error');
                return;
            }
            
            const testName = '中建汤逊湖壹号';
            log(`测试小区名: ${testName}`);
            
            placeSearch.search(testName, (status, result) => {
                log(`PlaceSearch回调 - 状态: ${status}`);
                log(`PlaceSearch回调 - 结果: ${JSON.stringify(result, null, 2)}`);
                
                if (status === 'complete' && result.poiList && result.poiList.pois.length > 0) {
                    const poi = result.poiList.pois[0];
                    log(`✓ PlaceSearch成功: ${poi.name} - 坐标 ${poi.location.lng}, ${poi.location.lat}`, 'success');
                    
                    // 在地图上添加标记
                    if (map) {
                        const marker = new AMap.Marker({
                            position: [poi.location.lng, poi.location.lat],
                            title: 'PlaceSearch结果',
                            content: '<div style="background: #1890ff; color: white; padding: 5px 8px; border-radius: 50%; font-size: 12px;">POI</div>'
                        });
                        map.add(marker);
                    }
                } else {
                    log(`PlaceSearch失败: 状态=${status}`, 'error');
                    if (result && result.info) {
                        log(`错误信息: ${result.info}`, 'error');
                    }
                }
            });
        }
        
        // 页面加载完成后的初始化
        window.onload = function() {
            log('页面加载完成，准备开始调试');
            
            // 检查地图API是否加载
            if (typeof AMap === 'undefined') {
                log('警告：AMap对象未定义，请检查API Key或网络连接', 'error');
            } else {
                log('AMap对象已加载，可以开始测试');
            }
        };
        
        // 监听地图API加载完成事件
        window.onAMapLoad = function() {
            log('AMap API加载完成事件触发');
        };
    </script>
</body>
</html>
