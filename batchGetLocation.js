/**
 * 批量地理编码测试脚本
 * 读取数据库中前10条房产数据，通过高德地图API获取坐标，并保存到CSV文件
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Supabase 配置
const supabaseUrl = 'https://iabpcsmeijvjfgqhgocp.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlhYnBjc21laWp2amZncWhnb2NwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNDMxNDU0NywiZXhwIjoyMDQ5ODkwNTQ3fQ.pXx1kS3V1EnoOM2rgNYzu7pII7yhNF1x9hcUm2HWHTM';

const supabase = createClient(supabaseUrl, supabaseKey);

// 高德地图API Key
const AMAP_KEY = '7a5917e97482a3cb8b2c1631b7cd9971';

/**
 * 从数据库获取前10条房产数据
 */
async function getFangData() {
    try {
        const { data, error } = await supabase.from('fang_data').select('id, name, district, location').limit(10);

        if (error) {
            throw error;
        }

        console.log(`获取到 ${data.length} 条房产数据`);
        return data;
    } catch (error) {
        console.error('获取数据失败:', error);
        throw error;
    }
}

/**
 * 构建搜索地址 - 改进版本
 */
function buildSearchAddress(fangData) {
    // 区域映射
    const districtMap = {
        江夏: '江夏区',
        洪山: '洪山区',
        武昌: '武昌区',
        沌口: '汉南区',
        经济开发区: '汉南区',
        江汉: '江汉区',
        江岸: '江岸区',
        硚口: '硚口区',
        汉阳: '汉阳区',
        青山: '青山区',
        武汉: '武汉市',
        汉南: '汉南区',
        蔡甸: '蔡甸区',
        新洲: '新洲区',
        黄陂: '黄陂区',
        东西湖: '东西湖区'
    };

    const district = districtMap[fangData.district] || fangData.district;

    // 优先使用：武汉市 + 标准区名 + 位置
    if (district && fangData.location) {
        return `武汉市${district}${fangData.location}`;
    }

    // 备选：武汉市 + 标准区名
    if (district) {
        return `武汉市${district}`;
    }

    // 最后：武汉市 + 位置
    if (fangData.location) {
        return `武汉市${fangData.location}`;
    }

    return '武汉市';
}

/**
 * 使用高德地图API进行地理编码
 */
async function geocodeAddress(address) {
    const url = `https://restapi.amap.com/v3/geocode/geo?key=${AMAP_KEY}&address=${encodeURIComponent(address)}`;

    try {
        const response = await fetch(url);
        const data = await response.json();

        if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
            const location = data.geocodes[0].location.split(',');
            return {
                lng: parseFloat(location[0]),
                lat: parseFloat(location[1])
            };
        } else {
            console.warn(`地理编码失败: ${address}`, data);
            return null;
        }
    } catch (error) {
        console.error(`请求失败: ${address}`, error);
        return null;
    }
}

/**
 * 批量处理地理编码
 */
async function batchGeocode(fangDataList) {
    const results = [];

    for (let i = 0; i < fangDataList.length; i++) {
        const fangData = fangDataList[i];
        const address = buildSearchAddress(fangData);

        console.log(`正在处理 ${i + 1}/${fangDataList.length}: ${fangData.name} - ${address}`);

        const coordinates = await geocodeAddress(address);

        results.push({
            id: fangData.id,
            name: fangData.name,
            address: address,
            gcj_lng: coordinates ? coordinates.lng : null,
            gcj_lat: coordinates ? coordinates.lat : null
        });

        // 延迟1秒避免请求过于频繁
        if (i < fangDataList.length - 1) {
            await new Promise((resolve) => setTimeout(resolve, 1000));
        }
    }

    return results;
}

/**
 * 保存结果到CSV文件
 */
function saveToCSV(results, filename = 'batchGCJ.csv') {
    const csvHeader = 'id,name,gcj_lat,gcj_lng\n';
    const csvRows = results
        .map((result) => {
            return `${result.id},"${result.name}",${result.gcj_lat || ''},${result.gcj_lng || ''}`;
        })
        .join('\n');

    const csvContent = csvHeader + csvRows;

    fs.writeFileSync(filename, csvContent, 'utf8');
    console.log(`结果已保存到 ${filename}`);
}

/**
 * 主函数
 */
async function main() {
    try {
        console.log('开始批量地理编码测试...');

        // 1. 获取房产数据
        console.log('1. 获取房产数据...');
        const fangDataList = await getFangData();

        if (fangDataList.length === 0) {
            console.log('没有找到房产数据');
            return;
        }

        // 2. 批量地理编码
        console.log('2. 开始批量地理编码...');
        const results = await batchGeocode(fangDataList);

        // 3. 保存结果
        console.log('3. 保存结果到CSV文件...');
        saveToCSV(results);

        // 4. 统计结果
        const successCount = results.filter((r) => r.gcj_lng && r.gcj_lat).length;
        const failCount = results.length - successCount;

        console.log('\n=== 处理完成 ===');
        console.log(`总数: ${results.length}`);
        console.log(`成功: ${successCount}`);
        console.log(`失败: ${failCount}`);
        console.log(`成功率: ${((successCount / results.length) * 100).toFixed(1)}%`);

        // 显示结果详情
        console.log('\n=== 结果详情 ===');
        results.forEach((result) => {
            const status = result.gcj_lng && result.gcj_lat ? '✓' : '✗';
            console.log(`${status} ${result.name}: ${result.gcj_lng || 'N/A'}, ${result.gcj_lat || 'N/A'}`);
        });
    } catch (error) {
        console.error('处理失败:', error);
    }
}

// 运行主函数
main();
