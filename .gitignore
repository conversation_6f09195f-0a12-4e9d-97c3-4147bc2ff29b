# 忽略 node_modules 目录
node_modules/

# 忽略其他常见的缓存和临时文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
*.DS_Store
.DS_Store
.DS_Store?*
.DS_Store*

# 忽略 IDE 和编辑器的配置文件
.idea/
.vscode/

# 忽略构建输出目录
dist/

# 忽略配置文件
CHANGELOG.md
LICENSE.md
README.md

# Added by Task Master AI
# Logs
logs
dev-debug.log
# Dependency directories
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 