<svg width="86" height="87" viewBox="0 0 86 87" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 32C0 20.799 0 15.1984 2.17987 10.9202C4.09734 7.15695 7.15695 4.09734 10.9202 2.17987C15.1984 0 20.799 0 32 0H54C65.201 0 70.8016 0 75.0798 2.17987C78.843 4.09734 81.9027 7.15695 83.8201 10.9202C86 15.1984 86 20.799 86 32V54C86 65.201 86 70.8016 83.8201 75.0798C81.9027 78.843 78.843 81.9027 75.0798 83.8201C70.8016 86 65.201 86 54 86H32C20.799 86 15.1984 86 10.9202 83.8201C7.15695 81.9027 4.09734 78.843 2.17987 75.0798C0 70.8016 0 65.201 0 54V32Z" fill="#FCFCF4"/>
<g opacity="0.2" filter="url(#filter0_f)">
<g style="mix-blend-mode:multiply">
<rect x="31" y="8" width="24" height="35" rx="12" fill="#FFA625"/>
</g>
<g style="mix-blend-mode:multiply">
<rect x="31" y="48" width="24" height="35" rx="12" fill="#69ABDF"/>
</g>
<g style="mix-blend-mode:multiply">
<rect x="60.7488" y="11" width="23" height="35" rx="11.5" transform="rotate(45 60.7488 11)" fill="#E7E00B"/>
</g>
<g style="mix-blend-mode:multiply">
<rect x="77.0122" y="63.7488" width="23" height="35" rx="11.5" transform="rotate(135 77.0122 63.7488)" fill="#63BE9D"/>
</g>
<g style="mix-blend-mode:multiply">
<rect x="8.20319" y="27.6628" width="23" height="35" rx="11.5" transform="rotate(-45 8.20319 27.6628)" fill="#FE6D51"/>
</g>
<g style="mix-blend-mode:multiply">
<rect x="24.4667" y="80.0122" width="23" height="35" rx="11.5" transform="rotate(-135 24.4667 80.0122)" fill="#A387C1"/>
</g>
<g style="mix-blend-mode:multiply">
<rect x="80" y="34" width="23" height="35" rx="11.5" transform="rotate(90 80 34)" fill="#9FCF45"/>
</g>
<g style="mix-blend-mode:multiply">
<rect x="41" y="34" width="23" height="35" rx="11.5" transform="rotate(90 41 34)" fill="#D47A9E"/>
</g>
</g>
<g style="mix-blend-mode:multiply" filter="url(#filter1_di)">
<rect x="31" y="5" width="24" height="35" rx="12" fill="#FFA625"/>
<rect x="31" y="5" width="24" height="35" rx="12" fill="url(#paint0_linear)" style="mix-blend-mode:overlay"/>
</g>
<g style="mix-blend-mode:multiply" filter="url(#filter2_di)">
<rect x="31" y="45" width="24" height="35" rx="12" fill="#69ABDF"/>
</g>
<g style="mix-blend-mode:multiply" filter="url(#filter3_di)">
<rect x="60.7488" y="8" width="23" height="35" rx="11.5" transform="rotate(45 60.7488 8)" fill="#E7E00B"/>
</g>
<g style="mix-blend-mode:multiply" filter="url(#filter4_di)">
<rect x="77.0122" y="60.7488" width="23" height="35" rx="11.5" transform="rotate(135 77.0122 60.7488)" fill="#63BE9D"/>
</g>
<g style="mix-blend-mode:multiply" filter="url(#filter5_di)">
<rect x="8.20319" y="24.6628" width="23" height="35" rx="11.5" transform="rotate(-45 8.20319 24.6628)" fill="#FE6D51"/>
<rect x="8.20319" y="24.6628" width="23" height="35" rx="11.5" transform="rotate(-45 8.20319 24.6628)" fill="url(#paint1_linear)" style="mix-blend-mode:overlay"/>
</g>
<g style="mix-blend-mode:multiply" filter="url(#filter6_di)">
<rect x="24.4667" y="77.0122" width="23" height="35" rx="11.5" transform="rotate(-135 24.4667 77.0122)" fill="#A387C1"/>
</g>
<g style="mix-blend-mode:multiply" filter="url(#filter7_di)">
<rect x="80" y="31" width="23" height="35" rx="11.5" transform="rotate(90 80 31)" fill="#9FCF45"/>
</g>
<g style="mix-blend-mode:multiply" filter="url(#filter8_di)">
<rect x="40" y="31" width="23" height="35" rx="11.5" transform="rotate(90 40 31)" fill="#D47A9E"/>
<rect x="40" y="31" width="23" height="35" rx="11.5" transform="rotate(90 40 31)" fill="url(#paint2_linear)" style="mix-blend-mode:overlay"/>
</g>
<defs>
<filter id="filter0_f" x="2" y="4" width="82" height="83" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur"/>
</filter>
<filter id="filter1_di" x="28" y="4" width="30" height="41" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
</filter>
<filter id="filter2_di" x="28" y="44" width="30" height="41" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
</filter>
<filter id="filter3_di" x="37.7635" y="11.7635" width="37.4853" height="37.4853" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
</filter>
<filter id="filter4_di" x="37.7635" y="39.7635" width="37.4853" height="37.4853" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
</filter>
<filter id="filter5_di" x="9.96661" y="12.1628" width="37.4853" height="37.4853" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
</filter>
<filter id="filter6_di" x="9.96667" y="39.7635" width="37.4853" height="37.4853" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
</filter>
<filter id="filter7_di" x="42" y="30" width="41" height="29" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
</filter>
<filter id="filter8_di" x="2" y="30" width="41" height="29" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow"/>
</filter>
<linearGradient id="paint0_linear" x1="40" y1="-9.5" x2="48" y2="12.5" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="12.0637" y1="20.5149" x2="21.2561" y2="30.4144" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="43" y1="75" x2="48" y2="52" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
