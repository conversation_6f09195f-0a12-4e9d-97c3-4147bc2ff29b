<svg width="86" height="86" viewBox="0 0 86 86" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 32C0 20.7989 0 15.1984 2.17987 10.9202C4.09734 7.15695 7.15695 4.09734 10.9202 2.17987C15.1984 0 20.7989 0 32 0H54C65.2011 0 70.8016 0 75.0798 2.17987C78.8431 4.09734 81.9027 7.15695 83.8201 10.9202C86 15.1984 86 20.7989 86 32V54C86 65.2011 86 70.8016 83.8201 75.0798C81.9027 78.8431 78.8431 81.9027 75.0798 83.8201C70.8016 86 65.2011 86 54 86H32C20.7989 86 15.1984 86 10.9202 83.8201C7.15695 81.9027 4.09734 78.8431 2.17987 75.0798C0 70.8016 0 65.2011 0 54V32Z" fill="url(#paint0_linear)"/>
<mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="86" height="86">
<path d="M0 32C0 20.7989 0 15.1984 2.17987 10.9202C4.09734 7.15695 7.15695 4.09734 10.9202 2.17987C15.1984 0 20.7989 0 32 0H54C65.2011 0 70.8016 0 75.0798 2.17987C78.8431 4.09734 81.9027 7.15695 83.8201 10.9202C86 15.1984 86 20.7989 86 32V54C86 65.2011 86 70.8016 83.8201 75.0798C81.9027 78.8431 78.8431 81.9027 75.0798 83.8201C70.8016 86 65.2011 86 54 86H32C20.7989 86 15.1984 86 10.9202 83.8201C7.15695 81.9027 4.09734 78.8431 2.17987 75.0798C0 70.8016 0 65.2011 0 54V32Z" fill="url(#paint1_linear)"/>
</mask>
<g mask="url(#mask0)">
<g filter="url(#filter0_d)">
<path d="M33.0051 47.4977C33.2283 25.5268 40.724 6.73119 44.5 0L91.5 -8.5V34L87.5 85C77.5 91.3333 56.2 100.5 51 86.5C45.8 72.5 45.5 55.3333 46 48.5H34.0016C33.4493 48.5 32.9995 48.0499 33.0051 47.4977Z" fill="url(#paint2_linear)"/>
</g>
</g>
<g filter="url(#filter1_d)">
<rect x="59" y="24" width="4" height="9" rx="2" fill="black"/>
</g>
<g filter="url(#filter2_d)">
<rect x="20" y="24" width="4" height="9" rx="2" fill="black"/>
</g>
<g filter="url(#filter3_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M67.5398 55.1559C67.9959 55.4582 68.1368 56.0992 67.8544 56.5875L67.0284 56.0401C67.8544 56.5875 67.8537 56.5888 67.8537 56.5888L67.8528 56.5904L67.8505 56.5942L67.8443 56.6048L67.8246 56.6376C67.8082 56.6646 67.7854 56.7017 67.7558 56.7481C67.6968 56.841 67.6109 56.9715 67.4966 57.1342C67.2682 57.4596 66.9261 57.914 66.4577 58.454C65.5209 59.5341 64.0786 60.9569 62.0307 62.3736C57.9259 65.2131 53 68 41.7714 68C25.5 68 19.6097 60.5458 17.1633 56.617C16.8657 56.1391 16.9863 55.4934 17.4327 55.1748C17.8791 54.8562 18.4822 54.9853 18.7798 55.4632C20.8666 58.8144 26.5 65.92 41.7714 65.92C53.5 65.92 57.1883 63.247 60.9763 60.6266C62.8747 59.3133 64.195 58.0061 65.034 57.0387C65.4536 56.5549 65.7528 56.1562 65.9436 55.8844C66.039 55.7486 66.1073 55.6445 66.15 55.5774C66.1713 55.5439 66.1862 55.5196 66.1949 55.5053L66.2025 55.4927L66.2031 55.4917C66.4857 55.0041 67.0839 54.8538 67.5398 55.1559Z" fill="black"/>
</g>
<defs>
<filter id="filter0_d" x="28.005" y="-11.5" width="68.495" height="112.352" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="59" y="24" width="4" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter2_d" x="20" y="24" width="4" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter3_d" x="17" y="55" width="51" height="14" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="31" y1="7" x2="39" y2="83.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#19CBF9"/>
<stop offset="1" stop-color="#2867D8"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="43" y1="0" x2="43" y2="86" gradientUnits="userSpaceOnUse">
<stop stop-color="#18CDF8"/>
<stop offset="1" stop-color="#186CF2"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="50.5" y1="8" x2="63.5" y2="81.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#EAECEB"/>
<stop offset="1" stop-color="#D2DCE5"/>
</linearGradient>
</defs>
</svg>
