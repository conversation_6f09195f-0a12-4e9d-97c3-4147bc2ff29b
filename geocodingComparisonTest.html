<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>地理编码对比测试</title>
    <style>
        html, body {
            height: 100%;
            width: 100%;
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        #container {
            height: 70%;
            width: 100%;
        }
        
        .control-panel {
            height: 30%;
            padding: 20px;
            background: #f5f5f5;
            overflow-y: auto;
        }
        
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #40a9ff;
        }
        
        .btn:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .status.success { background: #f6ffed; border: 1px solid #b7eb8f; color: #52c41a; }
        .status.error { background: #fff2f0; border: 1px solid #ffccc7; color: #ff4d4f; }
        .status.info { background: #e6f7ff; border: 1px solid #91d5ff; color: #1890ff; }
        
        .results {
            margin-top: 20px;
        }
        
        .result-item {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .result-header {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 10px;
            color: #333;
        }
        
        .coordinate-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            padding: 8px;
            border-radius: 4px;
        }
        
        .rest-api { background: #f6ffed; border-left: 4px solid #52c41a; }
        .js-api { background: #fff2f0; border-left: 4px solid #ff4d4f; }
        .place-search { background: #e6f7ff; border-left: 4px solid #1890ff; }
        
        .distance-info {
            margin-top: 10px;
            padding: 10px;
            background: #fafafa;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .legend {
            display: flex;
            gap: 20px;
            margin: 10px 0;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div id="container"></div>
    
    <div class="control-panel">
        <h3>地理编码精度对比测试</h3>
        <p>对比REST API、JavaScript API和PlaceSearch三种方式的地理编码精度</p>
        
        <div>
            <button class="btn" id="startBtn" onclick="startComparison()">开始对比测试</button>
            <button class="btn" onclick="clearAll()">清除所有标记</button>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background: #52c41a;"></div>
                <span>REST API (绿色)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #ff4d4f;"></div>
                <span>JavaScript API (红色)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #1890ff;"></div>
                <span>PlaceSearch (蓝色)</span>
            </div>
        </div>
        
        <div id="status" class="status info">
            准备就绪，点击"开始对比测试"开始分析
        </div>
        
        <div id="results" class="results"></div>
    </div>

    <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=f30bebf0ca8200ba9080214e826ff768&plugin=AMap.Geocoder,AMap.PlaceSearch"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script type="text/javascript">
        // 配置
        const supabaseUrl = 'https://iabpcsmeijvjfgqhgocp.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlhYnBjc21laWp2amZncWhnb2NwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNDMxNDU0NywiZXhwIjoyMDQ5ODkwNTQ3fQ.pXx1kS3V1EnoOM2rgNYzu7pII7yhNF1x9hcUm2HWHTM';
        const AMAP_REST_KEY = '7a5917e97482a3cb8b2c1631b7cd9971';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        let map;
        let geocoder;
        let placeSearch;
        let markers = [];
        let comparisonResults = [];
        
        // 初始化
        function init() {
            // 初始化地图
            map = new AMap.Map('container', {
                resizeEnable: true,
                center: [114.298572, 30.584355],
                zoom: 11
            });
            
            // 初始化地理编码器
            geocoder = new AMap.Geocoder({
                city: '武汉市',
                radius: 5000
            });
            
            // 初始化PlaceSearch
            placeSearch = new AMap.PlaceSearch({
                city: '027', // 武汉市区号
                pageSize: 10
            });
            
            updateStatus('初始化完成，准备开始测试', 'success');
        }
        
        // 更新状态
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        // 构建地址
        function buildAddress(fangData) {
            const districtMap = {
                江夏: '江夏区', 洪山: '洪山区', 武昌: '武昌区', 沌口: '汉南区',
                经济开发区: '汉南区', 江汉: '江汉区', 江岸: '江岸区', 硚口: '硚口区',
                汉阳: '汉阳区', 青山: '青山区', 武汉: '武汉市', 汉南: '汉南区',
                蔡甸: '蔡甸区', 新洲: '新洲区', 黄陂: '黄陂区', 东西湖: '东西湖区'
            };
            
            const district = districtMap[fangData.district] || fangData.district;
            
            if (district && fangData.location) {
                return `武汉市${district}${fangData.location}`;
            }
            return `武汉市${district || ''}`;
        }
        
        // REST API地理编码
        async function geocodeWithRestAPI(address) {
            try {
                const url = `https://restapi.amap.com/v3/geocode/geo?key=${AMAP_REST_KEY}&address=${encodeURIComponent(address)}`;
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
                    const location = data.geocodes[0].location.split(',');
                    return {
                        success: true,
                        lng: parseFloat(location[0]),
                        lat: parseFloat(location[1]),
                        formatted_address: data.geocodes[0].formatted_address
                    };
                }
                return { success: false, error: 'No results' };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        // JavaScript API地理编码
        function geocodeWithJSAPI(address) {
            return new Promise((resolve) => {
                geocoder.getLocation(address, (status, result) => {
                    if (status === 'complete' && result.geocodes && result.geocodes.length > 0) {
                        const location = result.geocodes[0].location;
                        resolve({
                            success: true,
                            lng: location.lng,
                            lat: location.lat,
                            formatted_address: result.geocodes[0].formatted_address
                        });
                    } else {
                        resolve({ success: false, error: status });
                    }
                });
            });
        }
        
        // PlaceSearch搜索
        function searchWithPlaceSearch(name) {
            return new Promise((resolve) => {
                placeSearch.search(name, (status, result) => {
                    if (status === 'complete' && result.poiList && result.poiList.pois.length > 0) {
                        const poi = result.poiList.pois[0]; // 取第一个结果
                        resolve({
                            success: true,
                            lng: poi.location.lng,
                            lat: poi.location.lat,
                            formatted_address: poi.address,
                            name: poi.name
                        });
                    } else {
                        resolve({ success: false, error: status });
                    }
                });
            });
        }
        
        // 计算距离
        function calculateDistance(point1, point2) {
            const distance = AMap.GeometryUtil.distance(
                [point1.lng, point1.lat],
                [point2.lng, point2.lat]
            );
            return Math.round(distance); // 返回米为单位的距离
        }
        
        // 添加标记
        function addMarker(position, color, title, info) {
            const marker = new AMap.Marker({
                position: [position.lng, position.lat],
                title: title,
                content: `<div style="background: ${color}; color: white; padding: 5px 8px; border-radius: 50%; font-size: 12px; font-weight: bold;">${info}</div>`
            });
            
            marker.on('click', function() {
                const infoWindow = new AMap.InfoWindow({
                    content: `
                        <div style="padding: 10px;">
                            <h4 style="margin: 0 0 10px 0;">${title}</h4>
                            <p style="margin: 5px 0;"><strong>方式:</strong> ${info}</p>
                            <p style="margin: 5px 0;"><strong>经度:</strong> ${position.lng}</p>
                            <p style="margin: 5px 0;"><strong>纬度:</strong> ${position.lat}</p>
                            <p style="margin: 5px 0;"><strong>地址:</strong> ${position.formatted_address || 'N/A'}</p>
                        </div>
                    `
                });
                infoWindow.open(map, marker.getPosition());
            });
            
            markers.push(marker);
            map.add(marker);
            return marker;
        }
        
        // 开始对比测试
        async function startComparison() {
            try {
                const startBtn = document.getElementById('startBtn');
                startBtn.disabled = true;
                startBtn.textContent = '测试中...';
                
                updateStatus('正在获取随机房产数据...', 'info');
                
                // 获取随机5条数据
                const { data: fangData, error } = await supabase
                    .from('fang_data')
                    .select('id, name, district, location')
                    .limit(5);
                
                if (error) throw error;
                if (!fangData || fangData.length === 0) {
                    updateStatus('没有找到房产数据', 'error');
                    return;
                }
                
                // 清除之前的结果
                clearAll();
                comparisonResults = [];
                
                // 对每条数据进行三种方式的地理编码
                for (let i = 0; i < fangData.length; i++) {
                    const item = fangData[i];
                    const address = buildAddress(item);
                    
                    updateStatus(`正在处理 ${i + 1}/${fangData.length}: ${item.name}`, 'info');
                    
                    // 三种方式并行处理
                    const [restResult, jsResult, placeResult] = await Promise.all([
                        geocodeWithRestAPI(address),
                        geocodeWithJSAPI(address),
                        searchWithPlaceSearch(item.name)
                    ]);
                    
                    const result = {
                        id: item.id,
                        name: item.name,
                        address: address,
                        restAPI: restResult,
                        jsAPI: jsResult,
                        placeSearch: placeResult,
                        distances: {}
                    };
                    
                    // 添加标记
                    if (restResult.success) {
                        addMarker(restResult, '#52c41a', item.name, 'REST');
                    }
                    if (jsResult.success) {
                        addMarker(jsResult, '#ff4d4f', item.name, 'JS');
                    }
                    if (placeResult.success) {
                        addMarker(placeResult, '#1890ff', item.name, 'POI');
                    }
                    
                    // 计算距离
                    if (restResult.success && jsResult.success) {
                        result.distances.restToJs = calculateDistance(restResult, jsResult);
                    }
                    if (restResult.success && placeResult.success) {
                        result.distances.restToPlace = calculateDistance(restResult, placeResult);
                    }
                    if (jsResult.success && placeResult.success) {
                        result.distances.jsToPlace = calculateDistance(jsResult, placeResult);
                    }
                    
                    comparisonResults.push(result);
                    
                    // 延迟避免请求过频
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
                // 适应视野
                if (markers.length > 0) {
                    map.setFitView(markers);
                }
                
                // 显示结果
                displayResults();
                updateStatus('对比测试完成！', 'success');
                
            } catch (error) {
                console.error('对比测试失败:', error);
                updateStatus(`测试失败: ${error.message}`, 'error');
            } finally {
                const startBtn = document.getElementById('startBtn');
                startBtn.disabled = false;
                startBtn.textContent = '开始对比测试';
            }
        }
        
        // 显示结果
        function displayResults() {
            const resultsEl = document.getElementById('results');
            resultsEl.innerHTML = '';
            
            comparisonResults.forEach(result => {
                const div = document.createElement('div');
                div.className = 'result-item';
                
                let distanceInfo = '';
                if (Object.keys(result.distances).length > 0) {
                    distanceInfo = '<div class="distance-info"><strong>距离对比:</strong><br>';
                    if (result.distances.restToJs !== undefined) {
                        distanceInfo += `REST API ↔ JS API: ${result.distances.restToJs}米<br>`;
                    }
                    if (result.distances.restToPlace !== undefined) {
                        distanceInfo += `REST API ↔ PlaceSearch: ${result.distances.restToPlace}米<br>`;
                    }
                    if (result.distances.jsToPlace !== undefined) {
                        distanceInfo += `JS API ↔ PlaceSearch: ${result.distances.jsToPlace}米`;
                    }
                    distanceInfo += '</div>';
                }
                
                div.innerHTML = `
                    <div class="result-header">${result.name} (ID: ${result.id})</div>
                    <div class="coordinate-row rest-api">
                        <span><strong>REST API:</strong> ${result.address}</span>
                        <span>${result.restAPI.success ? `${result.restAPI.lng}, ${result.restAPI.lat}` : '失败'}</span>
                    </div>
                    <div class="coordinate-row js-api">
                        <span><strong>JS API:</strong> ${result.address}</span>
                        <span>${result.jsAPI.success ? `${result.jsAPI.lng}, ${result.jsAPI.lat}` : '失败'}</span>
                    </div>
                    <div class="coordinate-row place-search">
                        <span><strong>PlaceSearch:</strong> ${result.name}</span>
                        <span>${result.placeSearch.success ? `${result.placeSearch.lng}, ${result.placeSearch.lat}` : '失败'}</span>
                    </div>
                    ${distanceInfo}
                `;
                
                resultsEl.appendChild(div);
            });
        }
        
        // 清除所有标记
        function clearAll() {
            if (markers.length > 0) {
                map.remove(markers);
                markers = [];
            }
            document.getElementById('results').innerHTML = '';
        }
        
        // 页面加载完成后初始化
        window.onload = function() {
            init();
        };
    </script>
</body>
</html>
