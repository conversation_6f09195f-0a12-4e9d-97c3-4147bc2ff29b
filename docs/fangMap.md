现在我们通过了测试，接下来将改造 fang_data 表,并实现 FangMap.vue 中的功能。
FangMap.vue 中的功能是：在 @/Users/<USER>/GitHub/livemapTipTap page/src/components/map/FangMap.vue 中，展示所有的 fang_data 中的数据。

1. marker 的颜色或图标根据数据中的 sort 来区分。sort 为 resale 则标识这是一个交房并投入使用的小区，在地图上可以用红色的标识来显示(public/fang-red-32.png)。sort 为 new 则表示这还是处于未交房且为在售状态的楼盘，在地图上可以用绿色的标识来显示(public/fang-green-32.png)。
2. 每一条数据都是一个地图上的一个 marker 标记(我们可以称之为 fang_marker)，每次打开 @/Users/<USER>/GitHub/livemapTipTap page/src/components/map/FangMap.vue 都会加载 fangMarker 的数据并显示在地图上。鼠标移动到每一个 fangMarker 上(悬停)，都会弹出信息窗口info-panel，显示相关信息。鼠标从 fangMakrer 上已开 1 秒后，或从 info-panel 窗口区域移开 1 秒后，info-panel 自动关闭。当鼠标移动到下一个 fangMarker 之上时，立即关闭之前fangMarker的 info-panel。

- 小区或楼盘名称 name
- 地址信息 district + location
- 房价信息 price
- 标签信息 (sort 为 new 独有)
- 建筑面积信息 (sort 为 new 独有)
- 交房时间 establish (sort 为 resale 独有)
- 二手房在售量 on_sold (sort 为 resale 独有)
- 安居客全景图 ajk-pano( 图标地址为：public/ajk-pano_icon.png)
- 安居客链接 ajk-url (图标地址为：public/ajk-icon.png)
  其中 ajk-pano 和 ajk-url 是可以点击的图标
  为了防止跨域访问的问题出现，点击这 2 个图标 ，都会弹出一个新的窗口用来跳转链接。

3. 未来为了补全或增加更多的房屋信息，可能还会从其他网站补充信息来源，如房天下或者其他平台。所以未来在 info-panel 中还会显示房天下的 ftx-url 或 ftx-pano ，同时fang_data 中也会增加相关字段。
4. 合并 fang_data 表中 gcj_lat 和 gcj_lng 字段为 gcj ，数据类型为 jsonb。 你可以参考 supabase 项目中，amap_pano 表中的 gcj 字段。
5. 假如你完成了 步骤 4的内容，在 fang_data 中创建了新的 gcj 字段, gcj 中的数据是通过获得 高德地图的批量查询，一次性遍历该表中所有没有 gcj 字段的数据，并通过向高德地图提交批量小区名称name查询请求(restapi或 javascript api 或 placeSearch )，从返回的数据中一一对应的存在相应数据的gcj列中。
   你可以先完成步骤 4 和步骤 5，然后完成步骤 1,2,3
   每次步骤完成都需要进行测试。无误后再执行后续任务步骤。
