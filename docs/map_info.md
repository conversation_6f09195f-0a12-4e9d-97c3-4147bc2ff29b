# 地图服务信息

## 高德地图 Key

- 开发用 Key: 'f30bebf0ca8200ba9080214e826ff768'
- 申请方式: [高德开放平台](https://lbs.amap.com/dev/key/app)

## GCJ 坐标转换

- 使用库/接口: `如 coordtransform、@amap/amap-jsapi-loader` 或高德API
- 示例代码:

```js
import coordtransform from 'coordtransform';
const [lng, lat] = coordtransform.wgs84togcj02(lng, lat);
```

## mapService.js 主要接口

- initMap()
- createMap(containerId, options)
- addMarker(id, options)
- removeMarker(id)
- getMap()
- getAMap()
