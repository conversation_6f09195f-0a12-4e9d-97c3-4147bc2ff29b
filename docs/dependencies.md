# 依赖与环境说明

## Web 端主要依赖

- @supabase/supabase-js: x.x.x
- @amap/amap-jsapi-loader: x.x.x
- vue3: x.x.x

## React Native 端推荐依赖

- react-native: x.x.x
- expo 或 react-native-cli
- ant-design-mobile-rn: x.x.x
- @react-native-camera/vision-camera 或 expo-camera
- react-native-image-crop-picker 或 expo-image-manipulator
- react-native-exif
- @supabase/supabase-js
- react-native-amap3d 或 WebView + amap-jsapi
- @react-native-async-storage/async-storage

## 其它

- node: >=16
- .env 配置见 env.example
