# 数据结构与接口约定

## amap_photo 表结构

```sql
CREATE TABLE public.amap_photo (
  id uuid NOT NULL DEFAULT extensions.uuid_generate_v4(),
  create_time timestamp with time zone NULL DEFAULT now(),
  gps jsonb NULL,
  gcj jsonb NULL,
  name text NULL,
  gps_timestamp timestamp without time zone NULL,
  altitude numeric NOT NULL,
  city text NULL,
  full_address text NULL,
  photo_url text NULL,
  thumbnail_url text NULL,
  custom_gcj jsonb NULL,
  tag text NULL,
  collection_id text NULL,
  link text NULL,
  is_custom boolean NULL DEFAULT false,
  custom_address text NULL,
  CONSTRAINT amap_marker_images_pkey PRIMARY KEY (id)
);
```

## 字段说明

- id: 唯一标识
- gcj: 高德地图坐标（json: {lng, lat}）
- gps: 原始 GPS 坐标
- name: 照片名称
- photo_url: 图片存储 URL
- thumbnail_url: 缩略图 URL
- tag: 标签
- ...（其它字段说明参考下方sql）

```sql
CREATE TABLE public.amap_photo (
  id uuid NOT NULL DEFAULT extensions.uuid_generate_v4(), -- 唯一标识符
  create_time timestamp with time zone NULL DEFAULT now(), -- 创建时间
  gps jsonb NULL, -- GPS 数据
  gcj jsonb NULL, -- GCJ 数据
  name text NULL, -- 照片名称
  gps_timestamp timestamp without time zone NULL, -- GPS 时间戳
  altitude numeric NOT NULL, -- 海拔高度
  city text NULL, -- 城市名称
  full_address text NULL, -- 完整地址
  photo_url text NULL, -- 照片 URL
  thumbnail_url text NULL, -- 缩略图 URL
  custom_gcj jsonb NULL, -- 自定义 GCJ 数据
  tag text NULL, -- 标签
  collection_id text NULL, -- 集合 ID
  link text NULL, -- 相关链接
  is_custom boolean NULL DEFAULT false, -- 是否为自定义照片
  custom_address text NULL, -- 自定义地址
  CONSTRAINT amap_marker_images_pkey PRIMARY KEY (id) -- 主键约束
) TABLESPACE pg_default;
```

## marker 数据样例

```json
{
    "id": "62c3de39-04e3-42c3-9b0e-d45bf3ced0a3",
    "gcj": { "lat": 30.613534001813793, "lng": 114.2993852764492 },
    "gps": { "lat": 30.61595277777778, "lng": 114.29395277777778 },
    "tag": null,
    "city": "武汉市",
    "link": null,
    "name": "IMG_0408.jpeg",
    "altitude": 28.927297905487276,
    "is_custom": false,
    "photo_url": "https://iabpcsmeijvjfgqhgocp.supabase.co/storage/v1/object/public/amap_images/original/IMG_0408-1743998502297-ey00dp.jpeg",
    "custom_gcj": null,
    "create_time": "2025-04-07T04:01:54.964642+00:00",
    "full_address": "湖北省武汉市江岸区劳动街道永清庭苑",
    "collection_id": null,
    "gps_timestamp": "2025-03-28T08:44:27",
    "thumbnail_url": "https://iabpcsmeijvjfgqhgocp.supabase.co/storage/v1/object/public/amap_images/thumbnail/IMG_0408-1743998502297-ey00dp.jpeg",
    "custom_address": null
}
```
