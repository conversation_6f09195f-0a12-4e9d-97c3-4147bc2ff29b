# Supabase 信息

## Supabase 项目地址

- 项目 URL: `https://xxxx.supabase.co`
- 控制台: `https://app.supabase.com/project/xxxx`

## Storage

- Bucket 名称: `snapshot`
- 访问规则: `公开`

## 数据库表

- 主要表：`amap_photo`
- 建表 SQL 见 data_contract.md

## 相关代码

```js
// src/services/supabase.js
import { createClient } from '@supabase/supabase-js';
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseKey = import.meta.env.VITE_SUPABASE_KEY;
export const supabase = createClient(supabaseUrl, supabaseKey);
```
