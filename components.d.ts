/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Accordion: typeof import('primevue/accordion')['default']
    AccordionContent: typeof import('primevue/accordioncontent')['default']
    AccordionHeader: typeof import('primevue/accordionheader')['default']
    AccordionPanel: typeof import('primevue/accordionpanel')['default']
    AdminMap: typeof import('./src/components/map/AdminMap.vue')['default']
    Amap: typeof import('./src/components/map/Amap.vue')['default']
    Amap_backup: typeof import('./src/components/map/Amap_backup.vue')['default']
    AutoComplete: typeof import('primevue/autocomplete')['default']
    Badge: typeof import('primevue/badge')['default']
    BatchFileUpload: typeof import('./src/components/map/BatchPhotoUpload.vue')['default']
    BatchPanoUpload: typeof import('./src/components/map/BatchPanoUpload.vue')['default']
    BatchPhotoUpload: typeof import('./src/components/map/BatchPhotoUpload.vue')['default']
    BestSellingWidget: typeof import('./src/components/dashboard/BestSellingWidget.vue')['default']
    Button: typeof import('primevue/button')['default']
    ButtonGroup: typeof import('primevue/buttongroup')['default']
    Card: typeof import('primevue/card')['default']
    Chart: typeof import('primevue/chart')['default']
    Checkbox: typeof import('primevue/checkbox')['default']
    ColorPicker: typeof import('primevue/colorpicker')['default']
    Column: typeof import('primevue/column')['default']
    copy: typeof import('./src/components/map/doc/DocImage.vue')['default']
    DataTable: typeof import('primevue/datatable')['default']
    DataView: typeof import('primevue/dataview')['default']
    DatePicker: typeof import('primevue/datepicker')['default']
    Dialog: typeof import('primevue/dialog')['default']
    Divider: typeof import('primevue/divider')['default']
    DocCount: typeof import('./src/components/map/doc/DocCount.vue')['default']
    DocImage: typeof import('./src/components/map/doc/DocImage.vue')['default']
    DocImageNode: typeof import('./src/components/map/doc/DocImageNode.vue')['default']
    Dock: typeof import('primevue/dock')['default']
    FangMap: typeof import('./src/components/map/FangMap.vue')['default']
    FeaturesWidget: typeof import('./src/components/landing/FeaturesWidget.vue')['default']
    Fieldset: typeof import('primevue/fieldset')['default']
    FileUpload: typeof import('primevue/fileupload')['default']
    FloatingConfigurator: typeof import('./src/components/FloatingConfigurator.vue')['default']
    FloatLabel: typeof import('primevue/floatlabel')['default']
    Fluid: typeof import('primevue/fluid')['default']
    FooterWidget: typeof import('./src/components/landing/FooterWidget.vue')['default']
    HeroWidget: typeof import('./src/components/landing/HeroWidget.vue')['default']
    HighlightsWidget: typeof import('./src/components/landing/HighlightsWidget.vue')['default']
    IconField: typeof import('primevue/iconfield')['default']
    InfoListAll: typeof import('./src/components/map/InfoListAll.vue')['default']
    InfoWindow: typeof import('./src/components/map/InfoWindow.vue')['default']
    InputGroup: typeof import('primevue/inputgroup')['default']
    InputGroupAddon: typeof import('primevue/inputgroupaddon')['default']
    InputIcon: typeof import('primevue/inputicon')['default']
    InputNumber: typeof import('primevue/inputnumber')['default']
    InputText: typeof import('primevue/inputtext')['default']
    Knob: typeof import('primevue/knob')['default']
    Listbox: typeof import('primevue/listbox')['default']
    LogViewer: typeof import('./src/components/LogViewer.vue')['default']
    Menu: typeof import('primevue/menu')['default']
    Menubar: typeof import('primevue/menubar')['default']
    MultiSelect: typeof import('primevue/multiselect')['default']
    MyMap: typeof import('./src/components/map/MyMap.vue')['default']
    NodeCount: typeof import('./src/components/map/page_info/NodeCount.vue')['default']
    NodeImage: typeof import('./src/components/map/page_info/NodeImage.vue')['default']
    NotificationsWidget: typeof import('./src/components/dashboard/NotificationsWidget.vue')['default']
    OrderList: typeof import('primevue/orderlist')['default']
    Page_info_list: typeof import('./src/components/map/page_info/page_info_list.vue')['default']
    PageEditor: typeof import('./src/components/map/page_info/PageEditor.vue')['default']
    Panel: typeof import('primevue/panel')['default']
    Pano_info_form: typeof import('./src/components/map/pano_info/pano_info_form.vue')['default']
    Pano_info_input: typeof import('./src/components/map/pano_info/pano_info_input.vue')['default']
    Pano_info_list: typeof import('./src/components/map/pano_info/pano_info_list.vue')['default']
    Pano1_info_input: typeof import('./src/components/map/pano_info/pano_info_input.vue')['default']
    Photo_info_form: typeof import('./src/components/map/photo_info/photo_info_form.vue')['default']
    Photo_info_input: typeof import('./src/components/map/photo_info/photo_info_input.vue')['default']
    Photo_info_list: typeof import('./src/components/map/photo_info/photo_info_list.vue')['default']
    PickList: typeof import('primevue/picklist')['default']
    Pin_info_form: typeof import('./src/components/map/pin_info/pin_info_form.vue')['default']
    Pin_info_input: typeof import('./src/components/map/pin_info/pin_info_input.vue')['default']
    Pin_info_list: typeof import('./src/components/map/pin_info/pin_info_list.vue')['default']
    Popover: typeof import('primevue/popover')['default']
    PricingWidget: typeof import('./src/components/landing/PricingWidget.vue')['default']
    ProgressBar: typeof import('primevue/progressbar')['default']
    RadioButton: typeof import('primevue/radiobutton')['default']
    Rating: typeof import('primevue/rating')['default']
    RecentSalesWidget: typeof import('./src/components/dashboard/RecentSalesWidget.vue')['default']
    RevenueStreamWidget: typeof import('./src/components/dashboard/RevenueStreamWidget.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Select: typeof import('primevue/select')['default']
    SelectButton: typeof import('primevue/selectbutton')['default']
    Slider: typeof import('primevue/slider')['default']
    SplitButton: typeof import('primevue/splitbutton')['default']
    Splitter: typeof import('primevue/splitter')['default']
    SplitterPanel: typeof import('primevue/splitterpanel')['default']
    StatsWidget: typeof import('./src/components/dashboard/StatsWidget.vue')['default']
    Tab: typeof import('primevue/tab')['default']
    TabList: typeof import('primevue/tablist')['default']
    TabPanel: typeof import('primevue/tabpanel')['default']
    TabPanels: typeof import('primevue/tabpanels')['default']
    Tabs: typeof import('primevue/tabs')['default']
    Tag: typeof import('primevue/tag')['default']
    TestMap: typeof import('./src/components/map/TestMap.vue')['default']
    Textarea: typeof import('primevue/textarea')['default']
    Toast: typeof import('primevue/toast')['default']
    ToggleButton: typeof import('primevue/togglebutton')['default']
    ToggleSwitch: typeof import('primevue/toggleswitch')['default']
    Toolbar: typeof import('primevue/toolbar')['default']
    TopbarWidget: typeof import('./src/components/landing/TopbarWidget.vue')['default']
    TreeSelect: typeof import('primevue/treeselect')['default']
  }
  export interface ComponentCustomProperties {
    StyleClass: typeof import('primevue/styleclass')['default']
    Tooltip: typeof import('primevue/tooltip')['default']
  }
}
