# 构建贴纸stickers和涂鸦doodles功能的随想

## stickers 矢量图 svg 和 位图 的考虑

### stickers 功能构想简述

我的 vue 项目是基于高德地图 JS API2.0(https://lbs.amap.com/api/javascript-api-v2/documentation) 基础上构建的。我想在我的 vue 项目中添加一个功能叫 sticker 贴纸。作用是将我创建的 icon ，通过底部的 dock 栏来展示各式各样的 stickers 贴纸。拖动贴纸到地图上，可以起到以地图问底板背景，类似于 canvas 的效果，方便做各式各样的标记。其应用场景比如旅游攻略；基于地理位置的汇报讲解；地理信息分析等；

### stickers 问题

1. 如果所用的 stickers 是 webp 格式的，那么相比较于 svg 格式，有什么优劣；
2. 如果我想在把地图上的 stickers 做放大缩小旋转，那么 webp 或者说 png 一类的位图，能方便操作吗？
3. 为什么我会考虑不直接使用 svg ，转而使用 png 或 webp，jpg 之类的位图呢，是因为我希望这个 stickers 未来会让用户上传，而用户各自的需求和目的是不一样的，他需要有适合自己的 stickers， 如果让用户自行上传，那么位图格式的资源是最丰富的，这是我为了提升用户体验的一种考虑。

## doodles 在地图中使用 画板或画笔涂鸦 的方式进行标记

### doodles 功能构想简述

我看到 tiptap 中有画板涂鸦的功能(https://tiptap.dev/docs/editor/extensions/custom-extensions/node-views/examples#drawing-in-the-editor)。我希望在地图上也能进行绘画涂鸦，从而方便用户来讲解或描述地图中的地理信息或其他信息。

### doodles 问题

1. 我疑惑之处在于，如果按实例代码所示，将 Paper.js 作为 extension 添加到 editor 中，那么可不可以 将 paper 绘制所在的 div 图层，添加到 Amap 所在的地图 div 中，从而让 paper 所绘制的 svg 显示在地图中，随着地图的缩放而缩放；
2. 这个绘制的 svg 如何添加到数据库中作持久化？如何定位到地图上？以 marker 的形式吗？
3. 高德地图JS API2.0 中可以直接绘制 svg 吗？如何定位，持久化保存，以及方便编辑，如调整画笔颜色，粗细，样式(长实线，波浪线，点划线，箭头)等。
