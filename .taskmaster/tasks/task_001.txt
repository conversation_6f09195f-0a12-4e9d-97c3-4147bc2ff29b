# Task ID: 1
# Title: Define MatrixMarker Data Structure
# Status: done
# Dependencies: None
# Priority: high
# Description: Define the data structure for matrix markers as specified in the PRD, including id, position (GCJ-02), gridIndex, type, and status, using JavaScript with JSDoc documentation and examples.
# Details:
Create a JavaScript definition for `MatrixMarker` using JSDoc comments to specify the structure and types. Include fields: `id` (string, format 'matrix_x_y'), `position` ({ lng: number, lat: number }), `gridIndex` ({ x: number, y: number }), `type` ('matrix'), and `status` ('planned' | 'captured'). Provide example objects demonstrating the structure. Consider adding a basic validation function.

# Test Strategy:
Verify the JSDoc documentation accurately describes the structure and types. Check that example objects conform to the documented structure. If a validation function is created, test its ability to correctly identify valid and invalid `MatrixMarker` objects.

# Subtasks:
## 1. Define Base MatrixMarker Structure using JSDoc [done]
### Dependencies: None
### Description: Create the initial JavaScript definition for `MatrixMarker` using JSDoc comments, including all required field names: `id`, `position`, `gridIndex`, `type`, and `status`. Use basic JSDoc types initially.
### Details:
Create a file (e.g., `matrixMarker.js` or `types.js`) and add JSDoc comments to define the structure for a `MatrixMarker` object. Include `@property` tags for all five fields with initial types like `{string}`, `{object}`, or `{any}`.

## 2. Refine Position and GridIndex JSDoc Types [done]
### Dependencies: 1.1
### Description: Update the JSDoc documentation for the `position` and `gridIndex` fields to specify their object structure.
### Details:
Modify the JSDoc comments for `MatrixMarker` created in step 1. Change the type documentation for `position` to `{object}` with nested properties like `{number} lng` and `{number} lat`. Change the type documentation for `gridIndex` to `{object}` with nested properties like `{number} x` and `{number} y`.

## 3. Refine Status and Type JSDoc Types [done]
### Dependencies: 1.2
### Description: Update the JSDoc documentation for the `status` and `type` fields to specify the allowed literal or union values.
### Details:
Modify the JSDoc comments for `MatrixMarker`. Change the type documentation for `status` to specify the union type `{'planned'|'captured'}` and the type documentation for `type` to specify the literal type `{'matrix'}`.

## 4. Document ID Format Requirement and Add Examples [done]
### Dependencies: 1.3
### Description: Add documentation (JSDoc comments) explaining the required format for the `id` field ('matrix_x_y') and its intended derivation. Include example `MatrixMarker` objects.
### Details:
Add JSDoc comments explaining that the `id` should be a string following the format 'matrix_x_y', typically derived from the `gridIndex`. Add one or more example JavaScript objects that conform to the documented `MatrixMarker` structure.

## 5. Consider Adding a Basic Validation Function [done]
### Dependencies: 1.4
### Description: Optionally, create a simple JavaScript function to validate if an object conforms to the `MatrixMarker` structure.
### Details:
Implement a function (e.g., `isValidMatrixMarker(obj)`) that checks if the input object `obj` has the correct fields with expected types and values (e.g., `type` is 'matrix', `status` is 'planned' or 'captured', `position` and `gridIndex` have correct nested number properties).

