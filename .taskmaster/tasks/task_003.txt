# Task ID: 3
# Title: Add PanoMatrix Option to View Menu
# Status: done
# Dependencies: None
# Priority: high
# Description: Add the 'PanoMatrix' option to the existing View menu in the application's UI, using PrimeVue MenuItem component and an appropriate grid/matrix icon.
# Details:
Locate the existing View menu data structure (likely an array of MenuItem objects). Add a new MenuItem object with label '全景矩阵', an icon (e.g., 'pi pi-th' or 'pi pi-table' from PrimeIcons), and a command/action that will trigger the PanoMatrix functionality toggle.

# Test Strategy:
Manually verify the '全景矩阵' option appears correctly in the View menu with the specified icon. Check if clicking the menu item triggers a placeholder action (e.g., a console log or a simple state change).
