# Task ID: 5
# Title: Implement Grid Point Generation Algorithm
# Status: done
# Dependencies: 4
# Priority: high
# Description: Develop the algorithm to generate grid points within the calculated rectangular area, spaced 1 kilometer apart, and store their GCJ-02 coordinates and grid indices.
# Details:
Based on the bounding box from Task 4, iterate through the latitude and longitude range with 1km intervals. For each point, calculate its GCJ-02 coordinates (which Amap uses natively, but conversion might be needed if starting from WGS84 or other systems - PRD specifies GCJ-02 output, so ensure input is compatible or convert). Store each point as a `MatrixMarker` object (defined in Task 1) including its grid index (x, y) and position.

# Test Strategy:
Generate grid points for a smaller test area and verify the spacing is approximately 1km. Check that coordinates are in GCJ-02 format. Validate the total number of points generated is reasonable for the area size.
