# Task ID: 9
# Title: Implement Matrix Marker Interactions (Ho<PERSON>, Click)
# Status: done
# Dependencies: 7
# Priority: medium
# Description: Add interactive features to the matrix markers, including displaying coordinate information on hover and showing detailed information on click.
# Details:
When creating Amap Marker instances (Task 7), add event listeners for 'mouseover' and 'click'. On 'mouseover', display a tooltip or info window near the marker showing its coordinates (lng, lat) and potentially grid index. On 'click', display a more detailed info window or a side panel with the marker's full details (id, position, gridIndex, type, status).

# Test Strategy:
Hover the mouse over several matrix markers and verify that the coordinate tooltip appears correctly. Click on markers and verify that the detailed information display is triggered and shows the correct data for the clicked marker.
