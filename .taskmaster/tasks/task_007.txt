# Task ID: 7
# Title: Implement Matrix Marker Display on Map
# Status: done
# Dependencies: 6
# Priority: high
# Description: Implement the functionality to display the generated matrix marker points on the Amap instance when the PanoMatrix feature is active.
# Details:
Within the `Amap.vue` component or a dedicated map service (`PanoMatrixService`), listen for changes in the PanoMatrix display state from the Pinia store (Task 2). When active, iterate through the array of `MatrixMarker` objects. For each marker, create an Amap Marker instance using its `position` (GCJ-02) and add it to the map. When inactive, remove all matrix markers from the map.

# Test Strategy:
Toggle the PanoMatrix button. Verify that markers appear on the map when active and disappear when inactive. Check that markers are placed at the correct approximate locations within the 3rd Ring.
