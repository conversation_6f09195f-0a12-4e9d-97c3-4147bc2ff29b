# Task ID: 2
# Title: Setup Pinia Store for PanoMatrix State
# Status: done
# Dependencies: 1
# Priority: medium
# Description: Set up the necessary state management in Pinia store to hold the generated matrix marker data, the display toggle state, and potentially caching.
# Details:
Create a new module or add state/getters/actions to an existing Pinia store to manage the array of `MatrixMarker` objects, a boolean flag for display visibility, and potentially a state for caching the generated grid data.

# Test Strategy:
Write unit tests for the Pinia store to ensure state changes (like toggling visibility, adding markers) work correctly and data is stored as expected.
