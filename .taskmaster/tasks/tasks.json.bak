{"tasks": [{"id": 1, "title": "Define MatrixMarker Data Structure", "description": "Define the data structure for matrix markers as specified in the PRD, including id, position (GCJ-02), gridIndex, type, and status.", "details": "Create a TypeScript interface or JavaScript object structure for `MatrixMarker` with fields: `id` (string, format 'matrix_x_y'), `position` ({ lng: number, lat: number }), `gridIndex` ({ x: number, y: number }), `type` ('matrix'), and `status` ('planned' | 'captured'). This structure will be used throughout the feature.", "testStrategy": "Verify the defined data structure matches the PRD specification and is correctly typed if using TypeScript.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 2, "title": "Setup Pinia Store for PanoMatrix State", "description": "Set up the necessary state management in Pinia store to hold the generated matrix marker data, the display toggle state, and potentially caching.", "details": "Create a new module or add state/getters/actions to an existing Pinia store to manage the array of `MatrixMarker` objects, a boolean flag for display visibility, and potentially a state for caching the generated grid data.", "testStrategy": "Write unit tests for the Pinia store to ensure state changes (like toggling visibility, adding markers) work correctly and data is stored as expected.", "priority": "medium", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 3, "title": "Add PanoMatrix Option to View Menu", "description": "Add the 'PanoMatrix' option to the existing View menu in the application's UI, using PrimeVue MenuItem component and an appropriate grid/matrix icon.", "details": "Locate the existing View menu data structure (likely an array of MenuItem objects). Add a new MenuItem object with label '全景矩阵', an icon (e.g., 'pi pi-th' or 'pi pi-table' from PrimeIcons), and a command/action that will trigger the PanoMatrix functionality toggle.", "testStrategy": "Manually verify the '全景矩阵' option appears correctly in the View menu with the specified icon. Check if clicking the menu item triggers a placeholder action (e.g., a console log or a simple state change).", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 4, "title": "Implement 3rd Ring Boundary Acquisition and Rectangle Calculation", "description": "Implement the logic to acquire the boundary coordinates for Wuhan's 3rd Ring using the Amap API and calculate the maximum rectangular area that covers the effective region within this boundary.", "details": "Use the Amap API (@amap/amap-jsapi-loader) to query or define the boundary of Wuhan's 3rd Ring. Process these boundary coordinates to find the minimum and maximum latitude and longitude values, defining the bounding box (rectangle) for the grid generation. Ensure the rectangle calculation maximizes coverage within the actual ring boundary.", "testStrategy": "Verify the calculated bounding box coordinates are correct and reasonably cover the Wuhan 3rd Ring area based on visual inspection or known reference points. Test with edge cases if possible.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 5, "title": "Implement Grid Point Generation Algorithm", "description": "Develop the algorithm to generate grid points within the calculated rectangular area, spaced 1 kilometer apart, and store their GCJ-02 coordinates and grid indices.", "details": "Based on the bounding box from Task 4, iterate through the latitude and longitude range with 1km intervals. For each point, calculate its GCJ-02 coordinates (which Amap uses natively, but conversion might be needed if starting from WGS84 or other systems - PRD specifies GCJ-02 output, so ensure input is compatible or convert). Store each point as a `MatrixMarker` object (defined in Task 1) including its grid index (x, y) and position.", "testStrategy": "Generate grid points for a smaller test area and verify the spacing is approximately 1km. Check that coordinates are in GCJ-02 format. Validate the total number of points generated is reasonable for the area size.", "priority": "high", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 6, "title": "Integrate PanoMatrix Button Logic (Toggle Display)", "description": "Connect the PanoMatrix menu button click event to the logic that triggers the generation (if not cached) and display/hiding of the matrix markers.", "details": "Modify the command/action handler for the PanoMatrix MenuItem (from Task 3). This handler should toggle the display state managed in the Pinia store (Task 2). If the markers haven't been generated yet (or cache is invalid), trigger the grid generation logic (Task 5). Update the button's visual state (e.g., active/inactive) based on the display state.", "testStrategy": "Click the PanoMatrix button multiple times. Verify that it correctly toggles an internal state. Check that the grid generation function is called only when necessary (e.g., on the first click or cache miss).", "priority": "high", "dependencies": [3, 5], "status": "pending", "subtasks": []}, {"id": 7, "title": "Implement Matrix Marker Display on Map", "description": "Implement the functionality to display the generated matrix marker points on the Amap instance when the PanoMatrix feature is active.", "details": "Within the `Amap.vue` component or a dedicated map service (`PanoMatrixService`), listen for changes in the PanoMatrix display state from the Pinia store (Task 2). When active, iterate through the array of `MatrixMarker` objects. For each marker, create an Amap Marker instance using its `position` (GCJ-02) and add it to the map. When inactive, remove all matrix markers from the map.", "testStrategy": "Toggle the PanoMatrix button. Verify that markers appear on the map when active and disappear when inactive. Check that markers are placed at the correct approximate locations within the 3rd Ring.", "priority": "high", "dependencies": [6], "status": "pending", "subtasks": []}, {"id": 8, "title": "Design and Apply Matrix Marker Styles", "description": "Design and apply distinct visual styles for the matrix markers, including a unique icon, color scheme, size, and transparency.", "details": "Create a custom marker icon (e.g., using SVG or an image) that represents a grid or crosshair, distinct from existing pin, photo, or pano markers. Apply a blue or green color scheme consistent with the UI. Set an appropriate size and potentially a semi-transparent effect to the marker icons when creating Amap Marker instances in Task 7.", "testStrategy": "Visually inspect the displayed matrix markers on the map. Ensure they use the correct custom icon, color, size, and transparency as specified. Verify they are clearly distinguishable from other marker types.", "priority": "medium", "dependencies": [7], "status": "pending", "subtasks": []}, {"id": 9, "title": "Implement Matrix Marker Interactions (Hover, Click)", "description": "Add interactive features to the matrix markers, including displaying coordinate information on hover and showing detailed information on click.", "details": "When creating Amap Marker instances (Task 7), add event listeners for 'mouseover' and 'click'. On 'mouseover', display a tooltip or info window near the marker showing its coordinates (lng, lat) and potentially grid index. On 'click', display a more detailed info window or a side panel with the marker's full details (id, position, gridIndex, type, status).", "testStrategy": "Hover the mouse over several matrix markers and verify that the coordinate tooltip appears correctly. Click on markers and verify that the detailed information display is triggered and shows the correct data for the clicked marker.", "priority": "medium", "dependencies": [7], "status": "pending", "subtasks": []}, {"id": 10, "title": "Implement Performance Optimizations (Lazy Loading, Caching)", "description": "Implement performance optimizations such as lazy loading markers based on map view/zoom level and caching the generated grid data.", "details": "Modify the marker display logic (Task 7) to only add markers that are currently within the map's visible bounds and potentially within a certain zoom level range (lazy loading). Add logic to the Pinia store (Task 2) or `PanoMatrixService` to cache the generated array of `MatrixMarker` objects after the initial generation (Task 5), so subsequent toggles don't require recalculation.", "testStrategy": "Test map performance with the PanoMatrix feature active, especially when zooming and panning rapidly. Verify that only markers in the visible area are rendered. Clear browser cache and toggle the feature; verify subsequent toggles are faster due to data caching. Monitor memory usage.", "priority": "medium", "dependencies": [7], "status": "pending", "subtasks": []}, {"id": 11, "title": "Add UI Feedback (Loading State, Toast Notifications)", "description": "Add UI feedback mechanisms, including a loading indicator during grid generation/display and Toast notifications for operation results.", "details": "Implement a loading state variable in the Pinia store (Task 2) that is set to true when grid generation/display starts and false when it completes. Display a visual loading indicator (e.g., a spinner or progress bar) while this state is true. Use the PrimeVue Toast component to display messages for successful operations (e.g., '全景矩阵标记已显示') or errors.", "testStrategy": "Toggle the PanoMatrix feature (especially the first time or after clearing cache) and verify that a loading indicator is shown. Check that appropriate Toast messages appear upon successful display/hiding or potential errors.", "priority": "medium", "dependencies": [6, 7], "status": "pending", "subtasks": []}, {"id": 12, "title": "Ensure Compatibility and Refine Integration", "description": "Perform final integration checks to ensure the PanoMatrix feature works seamlessly with existing pin, photo, and pano marker functionalities and adheres to existing code standards.", "details": "Test the PanoMatrix feature alongside existing marker types. Ensure displaying/hiding matrix markers does not affect other markers. Verify map interactions (panning, zooming) work correctly with all marker types present. Review the code for adherence to Vue 3 Composition API, Pinia, and general project code standards. Refactor or clean up code as needed.", "testStrategy": "Activate all marker types (pin, photo, pano, matrix) simultaneously. Perform various map interactions (zoom, pan, click, hover). Verify that all markers behave correctly and no conflicts or performance issues arise beyond expected levels. Conduct a code review.", "priority": "medium", "dependencies": [7, 8, 9, 10, 11], "status": "pending", "subtasks": []}]}