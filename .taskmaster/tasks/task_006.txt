# Task ID: 6
# Title: Integrate PanoMatrix Button Logic (Toggle Display)
# Status: done
# Dependencies: 3, 5
# Priority: high
# Description: Connect the PanoMatrix menu button click event to the logic that triggers the generation (if not cached) and display/hiding of the matrix markers.
# Details:
Modify the command/action handler for the PanoMatrix MenuItem (from Task 3). This handler should toggle the display state managed in the Pinia store (Task 2). If the markers haven't been generated yet (or cache is invalid), trigger the grid generation logic (Task 5). Update the button's visual state (e.g., active/inactive) based on the display state.

# Test Strategy:
Click the PanoMatrix button multiple times. Verify that it correctly toggles an internal state. Check that the grid generation function is called only when necessary (e.g., on the first click or cache miss).
