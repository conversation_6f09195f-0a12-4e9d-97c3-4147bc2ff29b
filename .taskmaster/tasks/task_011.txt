# Task ID: 11
# Title: Add UI Feedback (Loading State, Toast Notifications)
# Status: done
# Dependencies: 6, 7
# Priority: medium
# Description: Add UI feedback mechanisms, including a loading indicator during grid generation/display and Toast notifications for operation results.
# Details:
Implement a loading state variable in the Pinia store (Task 2) that is set to true when grid generation/display starts and false when it completes. Display a visual loading indicator (e.g., a spinner or progress bar) while this state is true. Use the PrimeVue Toast component to display messages for successful operations (e.g., '全景矩阵标记已显示') or errors.

# Test Strategy:
Toggle the PanoMatrix feature (especially the first time or after clearing cache) and verify that a loading indicator is shown. Check that appropriate Toast messages appear upon successful display/hiding or potential errors.
