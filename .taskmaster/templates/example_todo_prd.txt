<context>
# Overview
本项目旨在开发一个自动化的 PDF 转 Markdown 系统。该系统能够将用户上传的 PDF 文档转换为一系列图像，然后利用先进的 Gemini 2.5 Flash API 对这些图像进行光学字符识别 (OCR)，提取文本内容，并最终将提取的文本转换为结构化的 Markdown 格式。这个工具主要面向需要从 PDF 中快速提取和重用文本内容的用户，例如学生、研究人员、内容创作者等，提供一种高效、准确的解决方案。

# Core Features
1.  **PDF 到图像转换**:
    *   **功能**: 系统能够接收用户上传的 PDF 文件，并将其每一页转换为独立的图像文件（例如 PNG 或 JPEG 格式）。
    *   **重要性**: 这是进行 OCR 处理的前提步骤，确保图像质量直接影响后续 OCR 的准确性。
    *   **工作原理**: 利用 Python 的 PDF 处理库（如 PyMuPDF/Fitz, pdf2image）实现 PDF 页面到图像的转换。需要处理多页 PDF，并确保转换后的图像清晰可辨。

2.  **图像 OCR 处理 (使用 Gemini 2.5 Flash API)**:
    *   **功能**: 对转换后的图像进行 OCR 处理，提取图像中的文本信息。
    *   **重要性**: 这是系统的核心功能，准确提取文本是生成高质量 Markdown 的关键。
    *   **工作原理**: 将图像文件发送到 Gemini 2.5 Flash API。API 将返回识别出的文本内容。需要处理 API 的请求和响应，包括错误处理和重试机制。

3.  **文本到 Markdown 转换**:
    *   **功能**: 将 OCR 提取的纯文本内容转换为结构化的 Markdown 格式。
    *   **重要性**: 提供易于阅读、编辑和发布的输出格式。
    *   **工作原理**: 分析提取的文本，尝试识别标题、段落、列表等基本结构，并应用相应的 Markdown 语法。可能需要一些启发式方法或简单的自然语言处理技术来改进格式塔建。

4.  **用户界面 (命令行界面 - CLI)**:
    *   **功能**: 提供一个简单的命令行界面，允许用户指定输入的 PDF 文件路径和输出 Markdown 文件的路径。
    *   **重要性**: 方便用户与系统交互。
    *   **工作原理**: 使用 Python 的 `argparse` 或类似库构建 CLI，接收输入参数并显示处理进度和结果。

# User Experience
*   **用户画像**:
    *   需要从 PDF 讲义中提取笔记的学生。
    *   需要从扫描版论文中获取文本的研究人员。
    *   需要将 PDF 内容发布到博客或网站的内容创作者。
*   **关键用户流程**:
    1.  用户通过命令行指定输入的 PDF 文件。
    2.  用户通过命令行指定输出 Markdown 文件的保存位置（可选，可默认为同目录下同文件名）。
    3.  系统开始处理：PDF 转图像 -> 图像 OCR -> 文本转 Markdown。
    4.  系统在命令行输出处理进度和最终结果（成功或失败信息）。
    5.  用户在指定位置找到生成的 Markdown 文件。
*   **UI/UX 考虑**:
    *   CLI 应提供清晰的指令和反馈。
    *   处理过程中应有进度提示，尤其对于大文件。
    *   错误信息应明确且具有指导性。
</context>
<PRD>
# Technical Architecture
*   **系统组件**:
    1.  **输入模块**: 接收 PDF 文件路径。
    2.  **PDF 处理模块**: 使用 Python 库（如 `PyMuPDF` 或 `pdf2image` 配合 `poppler`）将 PDF 页面转换为图像。
    3.  **OCR 模块**: 与 Gemini 2.5 Flash API 交互，发送图像并接收文本。需要管理 API 密钥和处理 API 调用限制。
    4.  **Markdown 生成模块**: 将 OCR 结果转换为 Markdown 文本。
    5.  **输出模块**: 将 Markdown 文本保存到文件。
    6.  **CLI 模块**: 使用 `argparse` 实现命令行交互。
*   **数据模型**:
    *   输入: PDF 文件。
    *   中间数据: 图像文件列表 (每个文件对应 PDF 的一页)。
    *   OCR 输出: 结构化的文本数据（可能包含每页文本）。
    *   最终输出: Markdown 文件。
*   **APIs 和集成**:
    *   **Gemini 2.5 Flash API**: 核心 OCR 功能。需要处理 API 认证、请求构建、响应解析和错误处理。
    *   Python PDF 处理库 (如 `PyMuPDF`, `pdf2image`)。
    *   Python HTTP 请求库 (如 `requests` 或 `httpx`) 与 Gemini API 交互。
*   **基础设施需求**:
    *   Python 运行环境。
    *   依赖库的安装 (通过 `requirements.txt` 或 `pyproject.toml`)。
    *   有效的 Gemini API 密钥。
    *   对于 `pdf2image`，可能需要 `poppler` 工具集。

# Development Roadmap
*   **MVP 需求**:
    1.  实现核心的 PDF 到图像转换功能。
    2.  集成 Gemini 2.5 Flash API 进行单张图像的 OCR。
    3.  实现基础的 OCR 文本到 Markdown 的转换（段落级别）。
    4.  构建基本的 CLI，允许指定输入 PDF 和输出目录。
    5.  处理单页和多页 PDF。
    6.  基本的错误处理和日志记录。
*   **未来增强**:
    1.  改进 Markdown 格式化逻辑，识别更多元素（如列表、表格、粗体、斜体）。
    2.  支持批量处理多个 PDF 文件。
    3.  提供配置选项（如图像格式、DPI）。
    4.  优化性能，例如并行处理图像 OCR。
    5.  考虑添加一个简单的 GUI (如图形用户界面) 封装 CLI。
    6.  支持处理受密码保护的 PDF（如果用户提供密码）。
    7.  更好的错误恢复和重试机制。

# Logical Dependency Chain
1.  **环境搭建与库选型**: 确定并安装所有必要的 Python 库 (PDF处理、图像处理、API交互)。
2.  **PDF 转图像模块**: 实现稳定可靠的 PDF 页面到图像文件的转换。这是后续步骤的基础。
3.  **Gemini API 集成**: 成功调用 Gemini 2.5 Flash API 对单张图像进行 OCR，并获取文本结果。确保 API 密钥管理和基本错误处理。
4.  **核心 OCR 流程**: 将 PDF 转图像模块和 Gemini API 集成起来，实现对整个 PDF 文档所有页面的 OCR 处理。
5.  **基础 Markdown 生成**: 将 OCR 提取的纯文本转换为简单的 Markdown 结构（主要是段落）。
6.  **CLI 实现**: 构建命令行界面，使得用户可以指定输入输出，并能触发整个处理流程。
7.  **端到端测试与完善**: 测试整个流程，修复 bug，完善错误处理和用户反馈。

# Risks and Mitigations
*   **技术挑战**:
    *   **OCR 准确性**: Gemini API 的准确性可能受图像质量、字体、布局复杂性的影响。
        *   **缓解**: 确保 PDF 转图像时生成高质量、高分辨率的图像。提供清晰的错误信息，告知用户可能的原因。
    *   **Markdown 结构识别**: 从纯文本中准确识别复杂的 Markdown 结构（如嵌套列表、表格）可能非常困难。
        *   **缓解**: MVP阶段专注于基础结构（段落、简单标题）。后续版本可以引入更复杂的解析逻辑或第三方库。
    *   **API 限制**: Gemini API 可能有调用频率或并发限制。
        *   **缓解**: 实现合理的请求速率控制和重试机制。在文档中说明 API 使用的潜在成本。
*   **MVP 范围**:
    *   **过度工程**: 试图在 MVP 中实现过多高级功能。
        *   **缓解**: 严格遵循 MVP 需求，优先完成核心功能。
*   **资源约束**:
    *   **API 成本**: Gemini API 的使用可能会产生费用。
        *   **缓解**: 在文档中明确指出，并建议用户了解 API 定价。开发过程中使用免费额度（如果可用）。
    *   **依赖库的兼容性**: 不同库之间可能存在版本冲突。
        *   **缓解**: 使用虚拟环境 (如 venv) 管理依赖。仔细选择库版本并在 `requirements.txt` 中固定。

# Appendix
*   **研究发现**:
    *   `PyMuPDF (Fitz)` 通常被认为是高效的 PDF 处理库。
    *   `pdf2image` 是一个流行的 `poppler` 封装库，用于 PDF 到图像的转换。
    *   Gemini API 文档是关键参考。
*   **技术规格**:
    *   Python 版本: 3.8+
    *   输出 Markdown 兼容通用 Markdown 解析器。
</PRD> 
