# 武汉三环内全景图矩阵标记功能 PRD

## 项目概述

在现有的LiveMap高德地图应用中，开发一个全景图矩阵标记功能，用于在武汉市三环内按网格模式自动生成全景图拍摄位置标记。通过在地图上显示这些矩阵式分布的标记点，为用户提供系统性的全景图拍摄指导。

## 技术栈

### 前端框架
- Vue 3 (Composition API)
- PrimeVue 4 组件库
- TipTap 富文本编辑器
- Pinia 状态管理
- Vue Router 4

### 地图服务
- 高德地图 (@amap/amap-jsapi-loader)
- coordtransform 坐标转换
- Pannellum 全景图查看器

### 构建工具
- Vite
- Tailwind CSS
- PostCSS

## 功能需求

### 核心功能：全景图矩阵标记系统

#### 1. 武汉三环范围定义
- 基于高德地图API获取武汉市三环的具体边界坐标
- 计算三环内的最大矩形区域覆盖范围
- 优化矩形边界以最大化覆盖三环内的有效区域

#### 2. 网格标记生成算法
- 在确定的矩形区域内，按照1公里间隔生成网格点
- 每个网格点作为一个全景图拍摄标记位置
- 生成的标记点使用GCJ-02坐标系（高德地图标准）
- 计算并存储每个标记点的经纬度坐标

#### 3. PanoMatrix按钮集成
- 在现有View菜单中添加"PanoMatrix"选项
- 按钮位置：View -> PanoMatrix (与PageList、PinList、PanoList、PhotoList并列)
- 按钮图标：使用grid或matrix相关的PrimeIcon图标
- 按钮功能：切换矩阵标记的显示/隐藏状态

#### 4. 标记显示与交互
- 点击PanoMatrix按钮后，在地图上显示所有计算出的网格标记点
- 使用特殊的图标区分矩阵标记和普通标记（建议使用网格或十字图标）
- 再次点击按钮，隐藏所有矩阵标记
- 标记点支持hover显示坐标信息
- 标记点点击后显示详细信息（坐标、编号等）

#### 5. 标记样式设计
- 标记图标：使用区别于现有pin、photo、pano标记的专用图标
- 颜色方案：采用与现有UI一致的配色，建议使用蓝色或绿色系
- 尺寸：适中大小，确保在不同缩放级别下都清晰可见
- 透明度：支持半透明效果，避免遮挡地图细节

### UI/UX设计要求

#### 1. 按钮设计
- 遵循现有View菜单的设计规范
- 使用PrimeVue的MenuItem组件
- 图标选择：pi pi-th 或 pi pi-table 或类似的网格图标
- 文字标签：中文"全景矩阵"

#### 2. 状态反馈
- 按钮状态：显示/隐藏状态的视觉反馈
- 加载状态：标记生成和显示过程中的loading指示
- 提示信息：使用Toast组件显示操作结果

#### 3. 性能优化
- 标记点懒加载：根据地图缩放级别和可视区域动态显示标记
- 内存管理：及时清理隐藏的标记对象
- 缓存机制：生成的网格数据进行本地缓存

### 技术实现要求

#### 1. 数据结构
```javascript
// 矩阵标记数据结构
interface MatrixMarker {
  id: string;           // 唯一标识符 (格式: "matrix_x_y")
  position: {           // GCJ-02坐标
    lng: number;
    lat: number;
  };
  gridIndex: {          // 网格索引
    x: number;
    y: number;
  };
  type: 'matrix';       // 标记类型标识
  status: 'planned' | 'captured'; // 拍摄状态
}
```

#### 2. 组件架构
- 在`src/components/map/Amap.vue`中集成功能
- 新增`PanoMatrixService`服务类处理矩阵计算
- 在现有菜单数据结构中添加PanoMatrix选项
- 使用现有的地图标记管理系统

#### 3. 状态管理
- 在Pinia Store中添加矩阵标记相关状态
- 管理标记显示/隐藏状态
- 缓存生成的网格数据

#### 4. 坐标计算逻辑
- 武汉三环边界点查询
- 最大矩形区域计算算法
- 1公里间隔网格点生成
- GCJ-02坐标系转换和验证

### 集成要求

#### 1. 与现有功能的兼容性
- 不影响现有的pin、photo、pano标记功能
- 与现有地图交互保持一致
- 遵循现有的代码规范和架构模式

#### 2. 扩展性考虑
- 支持未来调整网格间距的需求
- 支持不同城市三环范围的配置
- 为future全景图拍摄状态管理预留接口

## 验收标准

### 功能验收
1. 正确计算武汉三环内的矩形覆盖区域
2. 按1公里间隔准确生成网格标记点
3. View菜单中PanoMatrix按钮正常工作
4. 标记显示/隐藏功能正常
5. 标记点交互功能完整

### 性能验收
1. 标记生成时间不超过2秒
2. 标记显示/隐藏响应时间不超过500ms
3. 地图缩放和平移时性能流畅
4. 内存使用合理，无明显泄漏

### UI验收
1. 按钮样式与现有设计一致
2. 标记图标清晰可辨，不与现有标记混淆
3. 交互反馈及时准确
4. 移动端适配良好

## 实施优先级

### 高优先级
1. 武汉三环边界数据获取和矩形区域计算
2. 网格标记生成算法实现
3. View菜单集成和基础显示功能

### 中优先级
1. 标记交互功能（hover、click）
2. 性能优化（懒加载、缓存）
3. 状态管理集成

### 低优先级
1. 高级UI效果和动画
2. 扩展功能预留接口
3. 详细的标记信息展示

## 风险和注意事项

1. **坐标精度**：确保网格计算的坐标精度满足实际使用需求
2. **性能影响**：大量标记点可能影响地图性能，需要优化策略
3. **数据准确性**：武汉三环边界数据的准确性和时效性
4. **用户体验**：避免标记过于密集影响地图查看体验
5. **兼容性**：确保与现有功能和第三方库的兼容性

## 🗄️ 数据持久化要求 (重要！容易遗漏)

### 数据库设计
- **表结构**: amap_matrix表，包含三种坐标系统(GPS, GCJ-02, custom_gcj)
- **字段设计**: id, 坐标字段, 网格索引, 状态, 时间戳等
- **索引策略**: 基于坐标范围查询的空间索引
- **数据迁移**: 版本升级的迁移脚本

### 数据服务层 (matrixService.js)
- **CRUD操作**: 完整的增删改查接口
- **批量操作**: 批量插入网格数据，批量状态更新
- **区域查询**: 根据地图边界查询可见标记
- **格式转换**: 前端格式与数据库格式的双向转换
- **错误处理**: 数据库操作的异常处理和重试机制

### 数据集成策略
- **首次生成**: 生成网格数据后自动保存到数据库
- **后续加载**: 优先从数据库加载，避免重复计算
- **状态同步**: 用户操作后及时更新数据库状态
- **缓存机制**: 内存缓存与数据库数据的一致性保证

## 🔧 技术实现细节补充

### 坐标系统处理
```javascript
// 支持多种坐标系统的数据结构
interface MatrixMarker {
  id: string;
  gps: { lng: number; lat: number };      // WGS84坐标
  gcj: { lng: number; lat: number };      // GCJ-02坐标(高德标准)
  custom_gcj: { lng: number; lat: number }; // 自定义GCJ坐标
  gridIndex: { x: number; y: number };
  type: 'matrix';
  status: 'available' | 'captured' | 'processing' | 'unavailable';
}
```

### 性能优化策略
- **懒加载**: 只显示当前视图范围内的标记(1km缓冲区)
- **缓存机制**: 生成的网格数据进行内存和数据库双重缓存
- **事件防抖**: 地图移动事件300ms防抖，避免频繁更新
- **标记管理**: 及时清理隐藏的标记对象，防止内存泄漏

### 错误处理机制
- **网络错误**: 数据库连接失败的降级策略
- **数据错误**: 坐标转换失败的处理方案
- **用户反馈**: Toast通知显示操作结果和错误信息
- **日志记录**: 关键操作的日志记录，便于问题排查

## 📊 实际实施经验总结

### 开发阶段划分
1. **基础架构** (任务1-2): 数据结构和状态管理
2. **UI集成** (任务3): 菜单按钮集成
3. **核心算法** (任务4-5): 边界计算和网格生成
4. **功能集成** (任务6-7): 按钮逻辑和地图显示
5. **用户体验** (任务8-9): 样式设计和交互功能
6. **性能优化** (任务10-11): 懒加载和UI反馈
7. **集成测试** (任务12): 兼容性检查
8. **数据持久化** (任务13-14): 数据库服务和集成 ⚠️ **关键补充**

### 容易遗漏的关键环节
1. **数据库集成**: 最容易遗漏，导致数据无法持久化
2. **错误处理**: 异常情况下的用户体验
3. **性能优化**: 大量标记的性能问题
4. **状态同步**: 内存状态与数据库状态的一致性
5. **坐标转换**: 多种坐标系统的准确转换

### 验收标准补充
- **数据持久化**: 刷新页面后数据不丢失
- **状态同步**: 操作后数据库状态及时更新
- **错误恢复**: 网络异常后的自动重试机制
- **性能指标**: 483个标记点的流畅显示和交互

## 后续规划

1. **拍摄状态管理**：为实际拍摄后的状态更新功能预留接口
2. **多城市支持**：扩展到其他城市的环路系统
3. **自定义网格**：支持用户自定义网格密度和覆盖范围
4. **数据导出**：支持矩阵标记数据的导出功能
5. **离线支持**：支持离线模式下的数据缓存和同步
