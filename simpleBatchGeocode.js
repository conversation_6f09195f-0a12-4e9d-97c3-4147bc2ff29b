/**
 * 简化版批量地理编码脚本
 */

console.log('开始批量地理编码...');

// 配置
const AMAP_REST_KEY = '7a5917e97482a3cb8b2c1631b7cd9971';
const SUPABASE_URL = 'https://iabpcsmeijvjfgqhgocp.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlhYnBjc21laWp2amZncWhnb2NwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNDMxNDU0NywiZXhwIjoyMDQ5ODkwNTQ3fQ.pXx1kS3V1EnoOM2rgNYzu7pII7yhNF1x9hcUm2HWHTM';

// 地理编码函数
async function geocodeAddress(name) {
    try {
        // 添加武汉市前缀提高精度
        const address = `武汉市${name}`;
        const url = `https://restapi.amap.com/v3/geocode/geo?key=${AMAP_REST_KEY}&address=${encodeURIComponent(address)}&city=武汉`;
        const response = await fetch(url);
        const data = await response.json();

        if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
            const location = data.geocodes[0].location.split(',');
            const lng = parseFloat(location[0]);
            const lat = parseFloat(location[1]);

            // 验证坐标是否在武汉地区范围内 (大致范围: 113.6-115.1, 29.9-31.4)
            if (lng >= 113.6 && lng <= 115.1 && lat >= 29.9 && lat <= 31.4) {
                return {
                    success: true,
                    lng: lng,
                    lat: lat
                };
            } else {
                console.log(`坐标超出武汉范围: ${name} -> ${lng}, ${lat}`);
                return { success: false, reason: 'out_of_range' };
            }
        }
        return { success: false, reason: 'no_results' };
    } catch (error) {
        console.error('地理编码错误:', error);
        return { success: false, reason: 'error' };
    }
}

// 获取数据
async function getFangData() {
    try {
        const response = await fetch(`${SUPABASE_URL}/rest/v1/fang_data?select=id,name&gcj=is.null&limit=10`, {
            headers: {
                apikey: SUPABASE_KEY,
                Authorization: `Bearer ${SUPABASE_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log(`获取到 ${data.length} 条数据`);
        return data;
    } catch (error) {
        console.error('获取数据失败:', error);
        return [];
    }
}

// 更新数据
async function updateFangData(id, gcjData) {
    try {
        const response = await fetch(`${SUPABASE_URL}/rest/v1/fang_data?id=eq.${id}`, {
            method: 'PATCH',
            headers: {
                apikey: SUPABASE_KEY,
                Authorization: `Bearer ${SUPABASE_KEY}`,
                'Content-Type': 'application/json',
                Prefer: 'return=minimal'
            },
            body: JSON.stringify({ gcj: gcjData })
        });

        return response.ok;
    } catch (error) {
        console.error('更新数据失败:', error);
        return false;
    }
}

// 主处理函数
async function processBatch() {
    const fangData = await getFangData();

    if (fangData.length === 0) {
        console.log('没有需要处理的数据');
        return;
    }

    let successCount = 0;
    let failCount = 0;

    for (let i = 0; i < fangData.length; i++) {
        const item = fangData[i];
        console.log(`\n处理 ${i + 1}/${fangData.length}: ${item.name}`);

        // 地理编码
        const geocodeResult = await geocodeAddress(item.name);

        if (geocodeResult.success) {
            const gcjData = {
                lng: geocodeResult.lng,
                lat: geocodeResult.lat
            };

            // 更新数据库
            const updateSuccess = await updateFangData(item.id, gcjData);

            if (updateSuccess) {
                successCount++;
                console.log(`✓ 成功: ${item.name} -> ${geocodeResult.lng}, ${geocodeResult.lat}`);
            } else {
                failCount++;
                console.log(`✗ 更新失败: ${item.name}`);
            }
        } else {
            failCount++;
            console.log(`✗ 地理编码失败: ${item.name}`);
        }

        // 延迟避免请求过频
        await new Promise((resolve) => setTimeout(resolve, 500));
    }

    console.log(`\n=== 处理完成 ===`);
    console.log(`成功: ${successCount}`);
    console.log(`失败: ${failCount}`);
    console.log(`成功率: ${((successCount / (successCount + failCount)) * 100).toFixed(1)}%`);
}

// 运行
processBatch().catch(console.error);
