---
description: 
globs: 
alwaysApply: true
---
# TipTap 富文本编辑器规则

## 🎯 TipTap 核心概念

### **编辑器架构**
- **Editor** - 编辑器核心实例
- **Extensions** - 功能扩展插件
- **Commands** - 编辑器命令系统
- **ProseMirror** - 底层文档模型

## 📦 常用扩展配置

### **基础扩展组合**
```javascript
// ✅ DO: 标准编辑器配置
import { Editor } from '@tiptap/core'
import StarterKit from '@tiptap/starter-kit'
import Highlight from '@tiptap/extension-highlight'
import Image from '@tiptap/extension-image'
import Placeholder from '@tiptap/extension-placeholder'

const editor = new Editor({
  extensions: [
    StarterKit.configure({
      heading: {
        levels: [1, 2, 3, 4, 5, 6],
        HTMLAttributes: {
          class: 'editor-heading'
        }
      },
      bulletList: {
        HTMLAttributes: {
          class: 'editor-bullet-list'
        }
      },
      orderedList: {
        HTMLAttributes: {
          class: 'editor-ordered-list'
        }
      }
    }),
    Highlight.configure({
      multicolor: true,
      HTMLAttributes: {
        class: 'editor-highlight'
      }
    }),
    Image.configure({
      HTMLAttributes: {
        class: 'editor-image'
      },
      allowBase64: true,
      inline: false
    }),
    Placeholder.configure({
      placeholder: ({ node }) => {
        if (node.type.name === 'heading') {
          return '输入标题...'
        }
        return '开始输入内容...'
      }
    })
  ]
})
```

### **Vue 3 组合式API集成**
```vue
<script setup>
import { ref, watch, onBeforeUnmount } from 'vue'
import { Editor } from '@tiptap/core'
import { EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '开始输入...'
  },
  editable: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue'])

// ✅ DO: 使用ref管理编辑器实例
const editor = ref(null)

// ✅ DO: 初始化编辑器
const initEditor = () => {
  editor.value = new Editor({
    content: props.modelValue,
    editable: props.editable,
    extensions: [
      StarterKit,
      // 其他扩展...
    ],
    onUpdate: ({ editor }) => {
      emit('update:modelValue', editor.getHTML())
    }
  })
}

// ✅ DO: 监听props变化
watch(() => props.modelValue, (value) => {
  if (editor.value && editor.value.getHTML() !== value) {
    editor.value.commands.setContent(value, false)
  }
})

watch(() => props.editable, (value) => {
  if (editor.value) {
    editor.value.setEditable(value)
  }
})

// ✅ DO: 生命周期管理
onMounted(() => {
  initEditor()
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})
</script>

<template>
  <div class="tiptap-editor">
    <EditorContent 
      :editor="editor" 
      class="editor-content"
    />
  </div>
</template>
```

## 🎨 样式规范

### **编辑器样式**
```css
/* ✅ DO: 编辑器基础样式 */
.ProseMirror {
  @apply min-h-[200px] p-4 focus:outline-none;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  @apply font-bold mt-6 mb-4 leading-tight;
}

.ProseMirror h1 { @apply text-3xl; }
.ProseMirror h2 { @apply text-2xl; }
.ProseMirror h3 { @apply text-xl; }

.ProseMirror p {
  @apply mb-4 leading-relaxed;
}

.ProseMirror ul,
.ProseMirror ol {
  @apply ml-6 mb-4;
}

.ProseMirror li {
  @apply mb-2;
}

.ProseMirror blockquote {
  @apply border-l-4 border-gray-300 pl-4 italic my-4;
}

.ProseMirror pre {
  @apply bg-gray-100 rounded p-4 overflow-x-auto mb-4;
}

.ProseMirror code {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm;
}

.ProseMirror mark {
  @apply bg-yellow-200 px-1 rounded;
}

.ProseMirror img {
  @apply max-w-full h-auto rounded shadow-sm;
}
```

## 🛠️ 工具栏组件

### **工具栏按钮组件**
```vue
<script setup>
const props = defineProps({
  editor: Object,
  command: String,
  icon: String,
  title: String,
  isActive: Boolean
})

const executeCommand = () => {
  if (props.editor && props.command) {
    props.editor.chain().focus()[props.command]().run()
  }
}

const isCommandActive = computed(() => {
  if (!props.editor || !props.command) return false
  return props.editor.isActive(props.command)
})
</script>

<template>
  <Button
    @click="executeCommand"
    :class="{
      'bg-blue-500 text-white': isCommandActive,
      'bg-gray-200 text-gray-700': !isCommandActive
    }"
    size="small"
    :title="title"
  >
    <i :class="icon"></i>
  </Button>
</template>
```

### **完整工具栏示例**
```vue
<template>
  <div class="editor-toolbar flex gap-1 p-2 border-b">
    <!-- 文本格式化 -->
    <EditorButton
      :editor="editor"
      command="toggleBold"
      icon="pi pi-bold"
      title="粗体"
    />
    <EditorButton
      :editor="editor"
      command="toggleItalic"
      icon="pi pi-italic"
      title="斜体"
    />
    
    <!-- 标题 -->
    <Dropdown
      v-model="selectedHeading"
      :options="headingOptions"
      option-label="label"
      option-value="value"
      placeholder="标题"
      @change="setHeading"
    />
    
    <!-- 列表 -->
    <EditorButton
      :editor="editor"
      command="toggleBulletList"
      icon="pi pi-list"
      title="无序列表"
    />
    <EditorButton
      :editor="editor"
      command="toggleOrderedList"
      icon="pi pi-list-check"
      title="有序列表"
    />
  </div>
</template>
```

## 🔄 数据处理

### **内容验证**
```javascript
// ✅ DO: 内容长度验证
const validateContent = (content) => {
  const textContent = editor.value.state.doc.textContent
  const wordCount = textContent.trim().split(/\s+/).length
  
  return {
    isValid: wordCount <= 10000,
    wordCount,
    maxWords: 10000
  }
}

// ✅ DO: HTML清理
import DOMPurify from 'dompurify'

const sanitizeContent = (html) => {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'h1', 'h2', 'h3', 'ul', 'ol', 'li'],
    ALLOWED_ATTR: ['class']
  })
}
```

### **JSON序列化**
```javascript
// ✅ DO: 保存为JSON格式（推荐）
const saveAsJSON = () => {
  return editor.value.getJSON()
}

const loadFromJSON = (json) => {
  editor.value.commands.setContent(json)
}

// ✅ DO: HTML格式备用
const saveAsHTML = () => {
  return editor.value.getHTML()
}
```

## ⚠️ 注意事项

### **性能优化**
```javascript
// ✅ DO: 防抖更新
import { debounce } from 'lodash-es'

const debouncedUpdate = debounce((content) => {
  emit('update:modelValue', content)
}, 300)

const editor = new Editor({
  onUpdate: ({ editor }) => {
    debouncedUpdate(editor.getHTML())
  }
})
```

### **避免的反模式**
```javascript
// ❌ DON'T: 频繁的DOM操作
editor.commands.setContent(newContent) // 每次输入都调用

// ❌ DON'T: 忘记销毁编辑器
// 组件卸载时必须调用 editor.destroy()

// ❌ DON'T: 直接操作ProseMirror state
editor.state.doc = newDoc // 不要直接修改

// ✅ DO: 使用编辑器命令
editor.commands.setContent(newContent)
```

## 🔗 相关资源
- [TipTap 官方文档](mdc:https:/tiptap.dev)
- [ProseMirror 文档](mdc:https:/prosemirror.net)
- [vue3-stack.mdc](mdc:.cursor/rules/vue3-stack.mdc) - Vue 3 技术栈规则
