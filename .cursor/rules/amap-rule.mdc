---
description: 
globs: 
alwaysApply: true
---

---
description: 高德地图和地图服务集成规范和最佳实践
globs: src/**/*map*.vue, src/**/*amap*.js, src/**/*map*.js
alwaysApply: true
---

# 地图服务集成规则

## 🗺️ 地图服务架构

### **支持的地图服务**
- **高德地图** - 主要地图服务提供商
- **腾讯地图** - 备用地图服务
- **Pannellum** - 全景图查看器
- **坐标转换** - coordtransform库

## 📍 高德地图集成

### **地图初始化**
```javascript
// ✅ DO: 标准高德地图加载
import AMapLoader from '@amap/amap-jsapi-loader'

const initAMap = async () => {
  try {
    const AMap = await AMapLoader.load({
      key: process.env.VITE_AMAP_KEY, // 从环境变量获取
      version: '2.0',
      plugins: [
        'AMap.Scale',          // 比例尺
        'AMap.ToolBar',        // 工具栏
        'AMap.Geolocation',    // 定位
        'AMap.Geocoder',       // 地理编码
        'AMap.PlaceSearch'     // 地点搜索
      ]
    })
    
    const map = new AMap.Map('map-container', {
      viewMode: '3D',
      zoom: 10,
      center: [116.397428, 39.90923], // 北京
      mapStyle: 'amap://styles/normal'
    })
    
    return { AMap, map }
  } catch (error) {
    console.error('地图加载失败:', error)
    throw error
  }
}
```

### **标记点管理**
```javascript
// ✅ DO: 标记点操作
const addMarker = (map, position, options = {}) => {
  const marker = new AMap.Marker({
    position: position,
    title: options.title || '',
    icon: options.icon || new AMap.Icon({
      size: new AMap.Size(40, 50),
      image: '/marker-icon.png',
      imageOffset: new AMap.Pixel(-20, -50)
    }),
    ...options
  })
  
  map.add(marker)
  return marker
}

// ✅ DO: 批量添加标记点
const addMarkers = (map, markersData) => {
  const markers = markersData.map(data => {
    return addMarker(map, data.position, data.options)
  })
  
  // 自动调整视野以包含所有标记点
  if (markers.length > 0) {
    map.setFitView(markers)
  }
  
  return markers
}
```

### **信息窗体处理**
```vue
<!-- ✅ DO: 信息窗体组件 -->
<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  map: Object,
  position: Array,
  content: String,
  visible: Boolean
})

const infoWindow = ref(null)

const createInfoWindow = () => {
  if (!props.map) return
  
  infoWindow.value = new AMap.InfoWindow({
    position: props.position,
    content: props.content,
    offset: new AMap.Pixel(0, -30)
  })
}

const showInfoWindow = () => {
  if (infoWindow.value && props.visible) {
    infoWindow.value.open(props.map, props.position)
  }
}

const hideInfoWindow = () => {
  if (infoWindow.value) {
    infoWindow.value.close()
  }
}

watch(() => props.visible, (visible) => {
  if (visible) {
    showInfoWindow()
  } else {
    hideInfoWindow()
  }
})
</script>
```

## 🔄 坐标转换

### **坐标系转换**
```javascript
// ✅ DO: 坐标转换工具
import { gcj02towgs84, wgs84togcj02 } from 'coordtransform'

// GPS坐标转高德坐标
const gpsToAmap = (lng, lat) => {
  return wgs84togcj02(lng, lat)
}

// 高德坐标转GPS坐标
const amapToGps = (lng, lat) => {
  return gcj02towgs84(lng, lat)
}

// ✅ DO: 批量坐标转换
const convertCoordinates = (coords, fromType, toType) => {
  return coords.map(([lng, lat]) => {
    if (fromType === 'gps' && toType === 'amap') {
      return wgs84togcj02(lng, lat)
    } else if (fromType === 'amap' && toType === 'gps') {
      return gcj02towgs84(lng, lat)
    }
    return [lng, lat]
  })
}
```

## 📸 全景图集成

### **Pannellum 全景查看器**
```vue
<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'

const props = defineProps({
  panoramaUrl: String,
  autoLoad: {
    type: Boolean,
    default: true
  }
})

const panoramaContainer = ref(null)
const viewer = ref(null)

const initPanorama = () => {
  if (!panoramaContainer.value || !props.panoramaUrl) return
  
  viewer.value = pannellum.viewer(panoramaContainer.value, {
    type: 'equirectangular',
    panorama: props.panoramaUrl,
    autoLoad: props.autoLoad,
    autoRotate: -2,
    compass: true,
    showControls: true,
    showFullscreenCtrl: true,
    showZoomCtrl: true
  })
}

onMounted(() => {
  initPanorama()
})

onBeforeUnmount(() => {
  if (viewer.value) {
    viewer.value.destroy()
  }
})
</script>

<template>
  <div 
    ref="panoramaContainer" 
    class="panorama-viewer w-full h-96"
  />
</template>
```

## ⚠️ 注意事项

### **性能优化**
```javascript
// ✅ DO: 地图性能优化
const optimizeMap = (map) => {
  // 禁用不必要的交互
  map.setStatus({
    dragEnable: true,
    zoomEnable: true,
    doubleClickZoom: false,
    keyboardEnable: false,
    jogEnable: false,
    animateEnable: false
  })
  
  // 设置最大最小缩放级别
  map.setLimitBounds(bounds)
  map.setZooms([3, 20])
}

// ✅ DO: 大量标记点优化
const addMassMarkers = (map, markersData) => {
  // 使用海量点图层
  const massMarks = new AMap.MassMarks(markersData, {
    opacity: 0.8,
    zIndex: 111,
    cursor: 'pointer',
    style: massMarkStyle
  })
  
  massMarks.setMap(map)
  return massMarks
}
```

### **错误处理**
```javascript
// ✅ DO: 地图错误处理
const handleMapError = (error) => {
  console.error('地图操作失败:', error)
  
  // 根据错误类型进行处理
  if (error.message.includes('INVALID_KEY')) {
    // API密钥无效
    showError('地图API密钥无效，请检查配置')
  } else if (error.message.includes('QUOTA_EXCEEDED')) {
    // 配额超限
    showError('地图服务配额已用完')
  } else {
    // 其他错误
    showError('地图加载失败，请稍后重试')
  }
}
```

## 🔗 相关组件文件
- [Amap.vue](mdc:src/components/map/Amap.vue) - 高德地图主组件
- [InfoWindow.vue](mdc:src/components/map/InfoWindow.vue) - 信息窗体组件
- [vue3-stack.mdc](mdc:.cursor/rules/vue3-stack.mdc) - Vue 3 技术栈规则
