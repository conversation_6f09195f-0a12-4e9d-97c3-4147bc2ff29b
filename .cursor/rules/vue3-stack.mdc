---
description: 
globs: 
alwaysApply: true
---
# Vue 3 + TipTap + 地图应用技术栈规则

## 🚀 核心技术栈

### **前端框架**
- **Vue 3** (Composition API) - 主框架
- **Vite** - 构建工具和开发服务器
- **Vue Router 4** - 单页应用路由管理
- **Pinia** - 状态管理

### **UI 组件库**
- **PrimeVue 4** - 主要UI组件库
- **PrimeIcons** - 图标库
- **Tailwind CSS** - 样式框架
- **Chart.js** - 图表组件

### **富文本编辑器**
- **TipTap** - 现代富文本编辑器
  - `@tiptap/core` - 核心库
  - `@tiptap/vue-3` - Vue 3 集成
  - `@tiptap/starter-kit` - 基础扩展包
  - `@tiptap/extension-*` - 各种扩展

### **地图服务**
- **高德地图** (`@amap/amap-jsapi-loader`) - 主要地图服务
- **腾讯地图** (`tlbs-map-vue`) - 备用地图服务
- **Pannellum** - 全景图查看器
- **coordtransform** - 坐标转换工具

### **后端服务**
- **Express.js** - Node.js 服务器框架
- **Supabase** - 数据库和后端服务
- **Sharp** - 图像处理
- **Express FileUpload** - 文件上传处理

## 📋 开发规范

### **Vue 3 组件开发**
```vue
<script setup>
// ✅ DO: 使用 Composition API
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from '@/stores'

// ✅ DO: 使用 defineProps 和 defineEmits
const props = defineProps({
  modelValue: String,
  disabled: Boolean
})

const emit = defineEmits(['update:modelValue', 'change'])

// ✅ DO: 响应式数据和计算属性
const localValue = ref(props.modelValue)
const isDisabled = computed(() => props.disabled || false)
</script>

<template>
  <!-- ✅ DO: 使用 PrimeVue 组件 -->
  <InputText
    v-model="localValue"
    :disabled="isDisabled"
    @input="emit('update:modelValue', $event.target.value)"
  />
</template>

<style scoped>
/* ✅ DO: 使用 Tailwind CSS 类 */
.custom-input {
  @apply w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500;
}
</style>
```

### **TipTap 富文本编辑器**
```javascript
// ✅ DO: TipTap 编辑器配置
import { Editor } from '@tiptap/core'
import StarterKit from '@tiptap/starter-kit'
import Highlight from '@tiptap/extension-highlight'
import Image from '@tiptap/extension-image'
import Placeholder from '@tiptap/extension-placeholder'

const editor = new Editor({
  extensions: [
    StarterKit,
    Highlight.configure({ multicolor: true }),
    Image.configure({ HTMLAttributes: { class: 'editor-image' } }),
    Placeholder.configure({ placeholder: '开始输入内容...' })
  ],
  content: '<p>Hello World!</p>',
  onUpdate: ({ editor }) => {
    emit('update:modelValue', editor.getHTML())
  }
})
```

### **地图集成规范**
```javascript
// ✅ DO: 高德地图加载
import AMapLoader from '@amap/amap-jsapi-loader'

const initAMap = async () => {
  try {
    const AMap = await AMapLoader.load({
      key: 'your-amap-key',
      version: '2.0',
      plugins: ['AMap.Scale', 'AMap.ToolBar']
    })
    
    const map = new AMap.Map('map-container', {
      viewMode: '3D',
      zoom: 10,
      center: [116.397428, 39.90923]
    })
    
    return map
  } catch (error) {
    console.error('地图加载失败:', error)
  }
}
```

### **状态管理 (Pinia)**
```javascript
// ✅ DO: Pinia Store 定义
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useMapStore = defineStore('map', () => {
  // State
  const mapInstance = ref(null)
  const markers = ref([])
  const currentPosition = ref(null)
  
  // Getters
  const hasMarkers = computed(() => markers.value.length > 0)
  
  // Actions
  const addMarker = (marker) => {
    markers.value.push(marker)
  }
  
  const clearMarkers = () => {
    markers.value = []
  }
  
  return {
    mapInstance,
    markers,
    currentPosition,
    hasMarkers,
    addMarker,
    clearMarkers
  }
})
```

## 🔧 项目结构规范

```
src/
├── components/          # 可复用组件
│   ├── dashboard/      # 仪表盘组件
│   ├── landing/        # 落地页组件
│   └── map/           # 地图相关组件
├── views/             # 页面组件
├── stores/            # Pinia 状态管理
├── router/            # 路由配置
├── services/          # API 服务
├── utils/             # 工具函数
├── hooks/             # 组合式函数
└── assets/            # 静态资源
```

## ⚠️ 常见注意事项

### **避免的反模式**
```javascript
// ❌ DON'T: 在 Vue 3 中使用 Options API（除非必要）
export default {
  data() {
    return { count: 0 }
  },
  methods: {
    increment() { this.count++ }
  }
}

// ❌ DON'T: 直接修改 props
props.modelValue = 'new value'

// ❌ DON'T: 在模板中进行复杂逻辑处理
<template>
  <div>{{ data.items.filter(item => item.active).map(item => item.name).join(', ') }}</div>
</template>
```

### **性能优化建议**
- ✅ 使用 `v-memo` 优化列表渲染
- ✅ 使用 `defineAsyncComponent` 异步加载组件
- ✅ 合理使用 `shallowRef` 和 `shallowReactive`
- ✅ 使用 Vite 的代码分割功能

## 🔄 相关规则文件
- [amap-rule.mdc](mdc:.cursor/rules/amap-rule.mdc) - 高德地图专用规则
- [dev_workflow.mdc](mdc:.cursor/rules/dev_workflow.mdc) - 开发工作流规则
