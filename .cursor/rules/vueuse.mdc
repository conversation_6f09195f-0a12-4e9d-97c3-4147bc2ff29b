---
description: 
globs: 
alwaysApply: false
---
# VueUse 组合式工具库规则

## 📦 技术概览
- **用途**: Vue 3 组合式函数工具集，提供丰富的响应式工具
- **版本**: @vueuse/core ^10.x
- **文档**: [VueUse 官方文档](mdc:https:/vueuse.org)

## ⚙️ 安装和配置
```bash
# 安装 VueUse 核心包
npm install @vueuse/core

# 可选：特定用途的包
npm install @vueuse/router    # Vue Router 集成
npm install @vueuse/head      # 文档头部管理
```

## 📋 常用工具函数

### **状态管理类**
```javascript
// ✅ DO: 本地存储状态
import { useLocalStorage, useSessionStorage } from '@vueuse/core'

const userPreferences = useLocalStorage('user-prefs', {
  theme: 'light',
  language: 'zh-CN'
})

const sessionData = useSessionStorage('session-data', {})
```

### **DOM 操作类**
```javascript
// ✅ DO: 元素尺寸监听
import { useElementSize, useWindowSize } from '@vueuse/core'

const { width, height } = useWindowSize()
const elementRef = ref()
const { width: elementWidth, height: elementHeight } = useElementSize(elementRef)
```

### **网络请求类**
```javascript
// ✅ DO: 网络状态监听
import { useOnline, useFetch } from '@vueuse/core'

const isOnline = useOnline()

// 响应式 fetch
const { data, error, isFetching } = useFetch('/api/users', {
  onFetchError(ctx) {
    console.error('请求失败:', ctx.error)
  }
}).json()
```

### **设备交互类**
```javascript
// ✅ DO: 设备权限和传感器
import { useGeolocation, useDeviceOrientation, usePermission } from '@vueuse/core'

// 地理位置
const { coords, locatedAt, error } = useGeolocation()

// 设备方向
const { orientation } = useDeviceOrientation()

// 权限检查
const cameraPermission = usePermission('camera')
```

## 🎯 在地图应用中的使用

### **地理位置集成**
```vue
<script setup>
import { useGeolocation } from '@vueuse/core'
import { watch } from 'vue'

// ✅ DO: 地理位置与地图结合
const { coords, error } = useGeolocation({
  enableHighAccuracy: true,
  maximumAge: 30000,
  timeout: 27000
})

// 监听位置变化，更新地图中心
watch(coords, (newCoords) => {
  if (newCoords && map.value) {
    map.value.setCenter([newCoords.longitude, newCoords.latitude])
  }
}, { immediate: true })
</script>
```

### **响应式地图尺寸**
```vue
<script setup>
import { useElementSize } from '@vueuse/core'

const mapContainer = ref()
const { width, height } = useElementSize(mapContainer)

// ✅ DO: 响应式调整地图尺寸
watch([width, height], () => {
  if (map.value) {
    map.value.getViewport().resize()
  }
})
</script>

<template>
  <div ref="mapContainer" class="map-container">
    <!-- 地图内容 -->
  </div>
</template>
```

## 🎨 在 TipTap 编辑器中的使用

### **编辑器状态持久化**
```vue
<script setup>
import { useLocalStorage, useDebouncedRef } from '@vueuse/core'

// ✅ DO: 自动保存编辑器内容
const editorContent = useLocalStorage('editor-draft', '')
const debouncedContent = useDebouncedRef(editorContent, 1000)

const editor = new Editor({
  content: editorContent.value,
  onUpdate: ({ editor }) => {
    editorContent.value = editor.getHTML()
  }
})

// 监听防抖内容变化
watch(debouncedContent, (content) => {
  console.log('内容已自动保存')
})
</script>
```

### **编辑器快捷键**
```javascript
// ✅ DO: 键盘快捷键
import { useMagicKeys } from '@vueuse/core'

const { ctrl_s, ctrl_z, ctrl_y } = useMagicKeys()

watch(ctrl_s, (pressed) => {
  if (pressed) {
    saveDocument()
  }
})

watch(ctrl_z, (pressed) => {
  if (pressed && editor.value) {
    editor.value.commands.undo()
  }
})
```

## 🔄 响应式工具组合

### **窗口尺寸响应式**
```javascript
// ✅ DO: 响应式布局控制
import { useWindowSize, useBreakpoints } from '@vueuse/core'

const { width } = useWindowSize()
const breakpoints = useBreakpoints({
  mobile: 768,
  tablet: 1024,
  desktop: 1280,
})

const isMobile = breakpoints.smaller('tablet')
const isDesktop = breakpoints.greater('tablet')
```

### **主题切换**
```javascript
// ✅ DO: 主题管理
import { useDark, useToggle } from '@vueuse/core'

const isDark = useDark({
  selector: 'html',
  attribute: 'class',
  valueDark: 'dark',
  valueLight: 'light'
})

const toggleDark = useToggle(isDark)
```

## ⚠️ 注意事项

### **性能优化**
```javascript
// ✅ DO: 合理使用防抖和节流
import { useDebounceFn, useThrottleFn } from '@vueuse/core'

const debouncedSearch = useDebounceFn((query) => {
  searchAPI(query)
}, 300)

const throttledScroll = useThrottleFn(() => {
  updateScrollPosition()
}, 100)
```

### **避免的反模式**
```javascript
// ❌ DON'T: 过度使用响应式工具
// 简单的状态不需要 VueUse
const simpleCounter = ref(0) // 好
const simpleCounter = useCounter(0) // 过度

// ❌ DON'T: 忘记清理副作用
// 某些工具函数需要手动清理，特别是在组件卸载时

// ✅ DO: 在 onBeforeUnmount 中清理
import { tryOnBeforeUnmount } from '@vueuse/core'

const cleanup = someVueUseFunction()
tryOnBeforeUnmount(() => {
  cleanup()
})
```

## 🎯 项目特定用例

### **地图标记状态管理**
```javascript
// ✅ DO: 地图标记的响应式管理
import { useRefHistory } from '@vueuse/core'

const markers = ref([])
const { history, undo, redo, canUndo, canRedo } = useRefHistory(markers, {
  deep: true,
  capacity: 10
})
```

### **编辑器协作状态**
```javascript
// ✅ DO: 实时协作状态
import { useWebSocket } from '@vueuse/core'

const { status, data, send } = useWebSocket('ws://localhost:3000/collaborate', {
  onMessage: (ws, event) => {
    const update = JSON.parse(event.data)
    applyCollaborativeUpdate(update)
  }
})
```

## 🔗 相关规则文件
- [vue3-stack.mdc](mdc:.cursor/rules/vue3-stack.mdc) - Vue 3 核心技术栈
- [tiptap-editor.mdc](mdc:.cursor/rules/tiptap-editor.mdc) - TipTap 富文本编辑器
- [amap-rule.mdc](mdc:.cursor/rules/amap-rule.mdc) - 地图服务集成
