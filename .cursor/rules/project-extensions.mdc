---
description: 
globs: 
alwaysApply: false
---
# 项目扩展规则模板

## 🚀 如何添加新技术栈到规则中

### **第一步：识别新技术**
当您计划引入新的技术栈或功能时，考虑以下因素：
- 技术的用途和目标
- 与现有技术栈的集成方式
- 配置和最佳实践
- 常见问题和解决方案

### **第二步：创建专用规则文件**
```bash
# 为新技术创建专门的规则文件
touch .cursor/rules/[技术名称].mdc
```

### **第三步：规则文件模板**
```markdown
---
description: [技术名称] 集成规范和最佳实践
globs: src/**/*[相关文件模式]*, **/*[技术名称]*.js
alwaysApply: true  # 或 false，根据需要
---

# [技术名称] 规则

## 📦 技术概览
- **用途**: 简要说明技术的作用
- **版本**: 当前使用的版本
- **文档**: 官方文档链接

## ⚙️ 安装和配置
```javascript
// 安装命令
npm install [package-name]

// 基础配置示例
import [TechName] from '[package-name]'
```

## 📋 使用规范
### **标准用法**
```javascript
// ✅ DO: 推荐的使用方式
// 示例代码...

// ❌ DON'T: 避免的反模式
// 反例代码...
```

## 🔗 相关规则文件
- [vue3-stack.mdc](mdc:.cursor/rules/vue3-stack.mdc)
```

## 🎯 常见扩展场景

### **数据库集成 (示例)**
如果要添加新的数据库技术（如 Redis、MongoDB 等）：

1. **创建规则文件**: `.cursor/rules/database.mdc`
2. **包含内容**:
   - 连接配置
   - 查询模式
   - 错误处理
   - 性能优化

### **认证系统 (示例)**
如果要添加认证系统（如 Auth0、Firebase Auth 等）：

1. **创建规则文件**: `.cursor/rules/auth.mdc`
2. **包含内容**:
   - 认证流程
   - 权限控制
   - 安全最佳实践
   - 会话管理

### **测试框架 (示例)**
如果要添加测试工具（如 Vitest、Cypress 等）：

1. **创建规则文件**: `.cursor/rules/testing.mdc`
2. **包含内容**:
   - 测试结构
   - Mock 策略
   - 覆盖率要求
   - 持续集成

## 📝 规则维护指南

### **定期更新**
- ✅ 每当添加新依赖时，更新相关规则
- ✅ 项目架构变化时，同步更新规则
- ✅ 发现新的最佳实践时，及时记录

### **规则引用**
```markdown
# 在新规则文件中引用现有规则
- [vue3-stack.mdc](mdc:.cursor/rules/vue3-stack.mdc) - Vue 3 技术栈
- [tiptap-editor.mdc](mdc:.cursor/rules/tiptap-editor.mdc) - TipTap 编辑器
- [amap-rule.mdc](mdc:.cursor/rules/amap-rule.mdc) - 地图服务
```

### **规则激活控制**
```yaml
---
alwaysApply: true   # 总是应用此规则
alwaysApply: false  # 仅在相关文件时应用
globs: "src/components/auth/**/*"  # 限制应用范围
---
```

## 🔄 项目演进示例

### **当前技术栈** (已配置规则)
- ✅ Vue 3 + Composition API
- ✅ TipTap 富文本编辑器  
- ✅ 高德地图 + 全景图
- ✅ PrimeVue + Tailwind CSS
- ✅ Pinia 状态管理
- ✅ Vite 构建工具

### **计划中的扩展** (待添加规则)
- 🔄 WebSocket 实时通信
- 🔄 PWA 渐进式应用
- 🔄 国际化 (i18n)
- 🔄 单元测试框架
- 🔄 Docker 容器化

### **扩展步骤示例**
```bash
# 1. 安装新技术
npm install @vueuse/core

# 2. 创建规则文件
touch .cursor/rules/vueuse.mdc

# 3. 编写规则内容
# 4. 更新主规则文件引用
# 5. 测试规则效果
```

## 📚 相关规则文件
- [vue3-stack.mdc](mdc:.cursor/rules/vue3-stack.mdc) - Vue 3 核心技术栈
- [tiptap-editor.mdc](mdc:.cursor/rules/tiptap-editor.mdc) - TipTap 富文本编辑器
- [amap-rule.mdc](mdc:.cursor/rules/amap-rule.mdc) - 地图服务集成
- [dev_workflow.mdc](mdc:.cursor/rules/dev_workflow.mdc) - 开发工作流程
- [cursor_rules.mdc](mdc:.cursor/rules/cursor_rules.mdc) - Cursor 规则格式
