import { fileURLToPath, URL } from 'node:url';

import { PrimeVueResolver } from '@primevue/auto-import-resolver';
import vue from '@vitejs/plugin-vue';
import Components from 'unplugin-vue-components/vite';
import { defineConfig } from 'vite';

// https://vitejs.dev/config/
export default defineConfig({
    optimizeDeps: { include: ['@amap/amap-jsapi-loader'] },
    plugins: [vue(), Components({ resolvers: [PrimeVueResolver()] })],
    resolve: { alias: { '@': fileURLToPath(new URL('./src', import.meta.url)) } },
    build: { commonjsOptions: { transformMixedEsModules: true, include: [/node_modules/] } },
    server: {
        host: '0.0.0.0',
        port: 5173,
        headers: {
            'Cross-Origin-Embedder-Policy': 'unsafe-none',
            'Cross-Origin-Opener-Policy': 'unsafe-none'
        }
    },
    css: {
        preprocessorOptions: {
            scss: {}
        }
    }
});
