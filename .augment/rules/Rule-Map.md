---
type: 'always_apply'
---

# 开发须知

本应用基于高德地图，所有的功能几乎都围绕其展开，目的是让所有与地理位置相关的数据都能更好的在地图上展现、编辑、应用。

## 地图功能区分：

- 主地图、标记地图 Amap.vue：用来做标记 marker，不同种类的标记，方便用户根据标记的类型而更方便的管理和数据呈现。包含各种地图相关的小工具。
- 房产地图 FangMap.vue：用来处理、展示与房产信息相关的数据。譬如查询小区的全景图，查看基本信息等。
- AdminMap 地图管理页：随着项目开发进度推进，会有越来越多的地图专注于不同的领域。这里的组件页面用来对地图进行管理，比如未来考虑对不同的用户角色开启不同的地图页。
- 测试功能地图 TestMap.vue ：用来开发新的功能，优化现有代码逻辑，添加测试性的功能页面或组件。总之，这是专用于地图相关数据的应用组件，所有测试性质的工作，都可以在此集中完成，不能单独创建临时页面，以防应用程序的管理混乱、数据离散无序。

## 环境变量

参考项目根目录的 .env 文件或者 .env.local 文件中的内容。

## 临时测试

临时开发如果需要，可以在 /src/test/ 目录中存放功能或脚本，相关数据存放于 /src/test/ref_data/ 目录中。

## mcp 工具

本项目已经在 augment 编辑器环境中配置了 高德地图,supabase,context7,playwright,Sequential thinking mcp tools, 开发时可以根据需要调用。
