{"name": "sakai-vue", "version": "4.2.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --fix . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "server": "node ./server/index.js", "server:dev": "nodemon ./server/index.js", "start": "concurrently \"pnpm dev\" \"pnpm server:dev\""}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@primeuix/utils": "^0.5.1", "@primevue/themes": "^4.2.4", "@supabase/supabase-js": "^2.48.1", "@tiptap/core": "^2.11.7", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@tiptap/vue-3": "^2.11.7", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "axios": "^1.8.1", "body-parser": "^1.20.3", "chart.js": "3.3.2", "coordtransform": "^2.1.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "exifr": "^7.1.3", "express": "^4.21.2", "express-fileupload": "^1.5.1", "pannellum": "^2.5.6", "pinia": "^3.0.1", "primeicons": "^7.0.0", "primevue": "^4.3.1", "sharp": "^0.33.5", "tlbs-map-vue": "^1.2.1", "vue": "^3.4.34", "vue-demi": "^0.14.10", "vue-router": "^4.4.0"}, "devDependencies": {"@primevue/auto-import-resolver": "^4.2.4", "@rushstack/eslint-patch": "^1.8.0", "@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "autoprefixer": "^10.4.19", "concurrently": "^9.1.2", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "nodemon": "^3.1.9", "postcss": "^8.4.40", "prettier": "^3.2.5", "sass": "^1.85.1", "tailwindcss": "^3.4.17", "tailwindcss-primeui": "^0.3.2", "unplugin-vue-components": "^0.27.3", "vite": "^5.3.1"}}