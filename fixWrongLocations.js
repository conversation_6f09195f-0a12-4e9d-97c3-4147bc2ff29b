/**
 * 修正错误的地理编码数据
 */

console.log('开始修正错误的地理编码数据...');

// 配置
const AMAP_REST_KEY = '7a5917e97482a3cb8b2c1631b7cd9971';
const SUPABASE_URL = 'https://iabpcsmeijvjfgqhgocp.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlhYnBjc21laWp2amZncWhnb2NwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNDMxNDU0NywiZXhwIjoyMDQ5ODkwNTQ3fQ.pXx1kS3V1EnoOM2rgNYzu7pII7yhNF1x9hcUm2HWHTM';

// 需要修正的小区数据
const wrongLocations = [
    { id: 7, name: '橡树湾', district: '武昌', location: '徐东' },
    { id: 8, name: '东原启城(二期)', district: '洪山', location: '白沙洲' },
    { id: 10, name: '菩提苑', district: '江夏', location: '文化大道' },
    { id: 11, name: '碧桂园生态城左岸', district: '洪山', location: '光谷东' },
    { id: 13, name: '百步亭现代城二区', district: '江岸', location: '百步亭' }
];

// 区域映射
const districtMap = {
    江夏: '江夏区', 洪山: '洪山区', 武昌: '武昌区', 沌口: '汉南区',
    经济开发区: '汉南区', 江汉: '江汉区', 江岸: '江岸区', 硚口: '硚口区',
    汉阳: '汉阳区', 青山: '青山区', 武汉: '武汉市', 汉南: '汉南区',
    蔡甸: '蔡甸区', 新洲: '新洲区', 黄陂: '黄陂区', 东西湖: '东西湖区'
};

// 地理编码函数
async function geocodeWithDistrict(name, district, location) {
    try {
        const standardDistrict = districtMap[district] || district;
        
        // 尝试多种地址组合
        const addressVariations = [
            `武汉市${standardDistrict}${location}${name}`,
            `武汉市${standardDistrict}${name}`,
            `武汉市${location}${name}`,
            `武汉${name}`
        ];
        
        for (const address of addressVariations) {
            console.log(`尝试地址: ${address}`);
            
            const url = `https://restapi.amap.com/v3/geocode/geo?key=${AMAP_REST_KEY}&address=${encodeURIComponent(address)}&city=武汉`;
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
                const location = data.geocodes[0].location.split(',');
                const lng = parseFloat(location[0]);
                const lat = parseFloat(location[1]);
                
                // 验证坐标是否在武汉地区范围内
                if (lng >= 113.6 && lng <= 115.1 && lat >= 29.9 && lat <= 31.4) {
                    console.log(`✓ 成功: ${address} -> ${lng}, ${lat}`);
                    return {
                        success: true,
                        lng: lng,
                        lat: lat,
                        address: address,
                        formatted_address: data.geocodes[0].formatted_address
                    };
                } else {
                    console.log(`坐标超出武汉范围: ${address} -> ${lng}, ${lat}`);
                }
            }
            
            // 延迟避免请求过频
            await new Promise(resolve => setTimeout(resolve, 300));
        }
        
        return { success: false, reason: 'no_valid_results' };
    } catch (error) {
        console.error('地理编码错误:', error);
        return { success: false, reason: 'error' };
    }
}

// 更新数据库
async function updateLocation(id, gcjData) {
    try {
        const response = await fetch(`${SUPABASE_URL}/rest/v1/fang_data?id=eq.${id}`, {
            method: 'PATCH',
            headers: {
                apikey: SUPABASE_KEY,
                Authorization: `Bearer ${SUPABASE_KEY}`,
                'Content-Type': 'application/json',
                Prefer: 'return=minimal'
            },
            body: JSON.stringify({ gcj: gcjData })
        });
        
        return response.ok;
    } catch (error) {
        console.error('更新数据失败:', error);
        return false;
    }
}

// 主处理函数
async function fixLocations() {
    let successCount = 0;
    let failCount = 0;
    
    for (const item of wrongLocations) {
        console.log(`\n处理: ${item.name} (${item.district} ${item.location})`);
        
        // 地理编码
        const geocodeResult = await geocodeWithDistrict(item.name, item.district, item.location);
        
        if (geocodeResult.success) {
            const gcjData = {
                lng: geocodeResult.lng,
                lat: geocodeResult.lat
            };
            
            // 更新数据库
            const updateSuccess = await updateLocation(item.id, gcjData);
            
            if (updateSuccess) {
                successCount++;
                console.log(`✓ 更新成功: ${item.name} -> ${geocodeResult.lng}, ${geocodeResult.lat}`);
                console.log(`  使用地址: ${geocodeResult.address}`);
                console.log(`  标准地址: ${geocodeResult.formatted_address}`);
            } else {
                failCount++;
                console.log(`✗ 数据库更新失败: ${item.name}`);
            }
        } else {
            failCount++;
            console.log(`✗ 地理编码失败: ${item.name} - ${geocodeResult.reason}`);
        }
        
        // 延迟避免请求过频
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log(`\n=== 修正完成 ===`);
    console.log(`成功: ${successCount}`);
    console.log(`失败: ${failCount}`);
    console.log(`成功率: ${(successCount / (successCount + failCount) * 100).toFixed(1)}%`);
}

// 运行
fixLocations().catch(console.error);
