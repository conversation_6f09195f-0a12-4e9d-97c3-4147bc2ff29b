这是一个前端为： vue3 +tailwindcss + primevue + pinia + 高德地图 + Tiptap
后端为： express + supabase 的架构。
@/Users/<USER>/GitHub/livemapTipTap page/src/components/map/Amap.vue 是主程序web 的主页。
我的要求是：

1. 现在我希望在侧边栏中的第二项 路由中，由 File 改为 @/Users/<USER>/GitHub/livemapTipTap page/src/components/map/FangMap.vue （调整路由）
2. @/Users/<USER>/GitHub/livemapTipTap page/src/components/map/Amap.vue 现在和 @/Users/<USER>/GitHub/livemapTipTap page/src/components/map/FangMap.vue 相似，是因为本身 @/Users/<USER>/GitHub/livemapTipTap page/src/components/map/FangMap.vue 就是 Amap.vue 的一个副本。（移除 @FangMap.vue中与下面描述不相关的信息，让其变成一个干净的组件，专注于房产信息的显示）
   我想在希望将 @/Users/<USER>/GitHub/livemapTipTap page/src/components/map/FangMap.vue 专门用来显示 关于房地产小区的 mark 标识信息的专项页面，专门用来显示小区信息。形式为从房屋信息中读取每一条房屋信息，并将每一条信息，通过高德地图 marker 的方式添加到地图上。鼠标移动到每个 marker 上后，显示有关于该条信息的内容。点击该 marker 后，可以弹出一个页面，并显示器全景图页面。（数据暂时来自安居客 anjuke.com）
   2.1 我希望保留 菜单栏 menu 和 Dock 图标 组件。菜单栏和 dock 组件仅保留必要的功能。比如说，菜单栏添加按钮用来显示安居客的房产信息（未来还有房天下的数据）；dock 中 保留添加 page marker 的功能。
3. anjuke.com 安居客网站的信息我已经从网络获取到，并整理成了 csv格式的数据，并暂时放在 public 文件夹中：/public/ajk-fang.csv
   我希望将其中的数据添加到 supabase 中，但是 supabase 中相关的表还没有建立，网站和 supabase 的数据访问相关的程序也没有写。我希望你在 @/Users/<USER>/GitHub/livemapTipTap page/src/services 文件夹中，建立了一个 fangService.js 的文件，我希望你帮我将其中以及相关 pinia 的代码补充完整。我希望你能通过已经配置好的工具，将 ajk-fang.csv 中的数据读取并导入到 supabase 中，并在 FangMap.vue 中显示出来。
4. 与高德地图api 访问的相关密钥文件，与 supabase 建立连接的相关信息都放在 @/Users/<USER>/GitHub/livemapTipTap page/.env.local 和 .env 中

由于工作内容比较多，我希望你能建立一个 task 。拆分每一个需求点，逐步完成，并测试。全部完成后逐一 check 进度，以便后续持续进行，而不会丢失进度，防止反复重构。
