/**
 * 简化版地理编码测试脚本
 */

console.log('开始地理编码测试...');

// 测试 fetch API
async function testFetch() {
    try {
        const AMAP_REST_KEY = '7a5917e97482a3cb8b2c1631b7cd9971';
        const testName = '中建汤逊湖壹号';
        
        console.log(`测试地理编码: ${testName}`);
        
        const url = `https://restapi.amap.com/v3/geocode/geo?key=${AMAP_REST_KEY}&address=${encodeURIComponent(testName)}`;
        console.log(`请求URL: ${url}`);
        
        const response = await fetch(url);
        const data = await response.json();
        
        console.log('API响应:', JSON.stringify(data, null, 2));
        
        if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
            const location = data.geocodes[0].location.split(',');
            const result = {
                success: true,
                lng: parseFloat(location[0]),
                lat: parseFloat(location[1]),
                formatted_address: data.geocodes[0].formatted_address
            };
            console.log('地理编码成功:', result);
            return result;
        } else {
            console.log('地理编码失败: No results');
            return { success: false, error: 'No results' };
        }
    } catch (error) {
        console.error('地理编码错误:', error);
        return { success: false, error: error.message };
    }
}

// 运行测试
testFetch().then(result => {
    console.log('测试完成:', result);
}).catch(error => {
    console.error('测试失败:', error);
});
