/**
 * 批量地理编码 fang_data 表中的数据
 * 使用高德地图 REST API 进行地理编码
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Supabase 配置
const supabaseUrl = 'https://iabpcsmeijvjfgqhgocp.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlhYnBjc21laWp2amZncWhnb2NwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNDMxNDU0NywiZXhwIjoyMDQ5ODkwNTQ3fQ.pXx1kS3V1EnoOM2rgNYzu7pII7yhNF1x9hcUm2HWHTM';

// 高德地图API Key
const AMAP_REST_KEY = '7a5917e97482a3cb8b2c1631b7cd9971';

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * 从数据库获取没有 gcj 坐标的房产数据
 */
async function getFangDataWithoutGcj(limit = 50) {
    try {
        const { data, error } = await supabase.from('fang_data').select('id, name, district, location').is('gcj', null).limit(limit);

        if (error) {
            throw error;
        }

        console.log(`获取到 ${data.length} 条没有坐标的房产数据`);
        return data;
    } catch (error) {
        console.error('获取数据失败:', error);
        throw error;
    }
}

/**
 * 使用高德地图REST API进行地理编码
 */
async function geocodeWithRestAPI(name) {
    try {
        const url = `https://restapi.amap.com/v3/geocode/geo?key=${AMAP_REST_KEY}&address=${encodeURIComponent(name)}`;
        const response = await fetch(url);
        const data = await response.json();

        if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
            const location = data.geocodes[0].location.split(',');
            return {
                success: true,
                lng: parseFloat(location[0]),
                lat: parseFloat(location[1]),
                formatted_address: data.geocodes[0].formatted_address
            };
        }
        return { success: false, error: 'No results' };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

/**
 * 更新数据库中的 gcj 字段
 */
async function updateFangDataGcj(id, gcjData) {
    try {
        const { error } = await supabase.from('fang_data').update({ gcj: gcjData }).eq('id', id);

        if (error) {
            throw error;
        }
        return true;
    } catch (error) {
        console.error(`更新数据失败 (ID: ${id}):`, error);
        return false;
    }
}

/**
 * 批量地理编码处理
 */
async function batchGeocodeFangData() {
    console.log('开始批量地理编码处理...');

    let processedCount = 0;
    let successCount = 0;
    let failCount = 0;
    const results = [];

    try {
        // 分批处理，每次处理50条数据
        let hasMoreData = true;

        while (hasMoreData) {
            const fangData = await getFangDataWithoutGcj(50);

            if (fangData.length === 0) {
                hasMoreData = false;
                break;
            }

            console.log(`\n=== 处理第 ${Math.floor(processedCount / 50) + 1} 批数据 ===`);

            for (let i = 0; i < fangData.length; i++) {
                const item = fangData[i];
                processedCount++;

                console.log(`正在处理 ${processedCount}: ${item.name}`);

                // 地理编码
                const geocodeResult = await geocodeWithRestAPI(item.name);

                const result = {
                    id: item.id,
                    name: item.name,
                    geocode_success: geocodeResult.success,
                    update_success: false
                };

                if (geocodeResult.success) {
                    // 构建 gcj 对象
                    const gcjData = {
                        lng: geocodeResult.lng,
                        lat: geocodeResult.lat
                    };

                    // 更新数据库
                    const updateSuccess = await updateFangDataGcj(item.id, gcjData);
                    result.update_success = updateSuccess;
                    result.gcj = gcjData;
                    result.formatted_address = geocodeResult.formatted_address;

                    if (updateSuccess) {
                        successCount++;
                        console.log(`✓ 成功: ${item.name} -> ${geocodeResult.lng}, ${geocodeResult.lat}`);
                    } else {
                        failCount++;
                        console.log(`✗ 更新失败: ${item.name}`);
                    }
                } else {
                    failCount++;
                    result.error = geocodeResult.error;
                    console.log(`✗ 地理编码失败: ${item.name} - ${geocodeResult.error}`);
                }

                results.push(result);

                // 延迟避免请求过频
                await new Promise((resolve) => setTimeout(resolve, 200));
            }

            console.log(`第 ${Math.floor(processedCount / 50)} 批处理完成`);

            // 批次间延迟
            await new Promise((resolve) => setTimeout(resolve, 1000));
        }
    } catch (error) {
        console.error('批量处理失败:', error);
    }

    // 保存结果到文件
    const resultData = {
        summary: {
            total_processed: processedCount,
            success_count: successCount,
            fail_count: failCount,
            success_rate: processedCount > 0 ? ((successCount / processedCount) * 100).toFixed(2) + '%' : '0%'
        },
        results: results
    };

    fs.writeFileSync('fang_geocode_results.json', JSON.stringify(resultData, null, 2));

    console.log('\n=== 批量地理编码完成 ===');
    console.log(`总处理数: ${processedCount}`);
    console.log(`成功数: ${successCount}`);
    console.log(`失败数: ${failCount}`);
    console.log(`成功率: ${resultData.summary.success_rate}`);
    console.log('详细结果已保存到 fang_geocode_results.json');

    return resultData;
}

/**
 * 检查处理进度
 */
async function checkProgress() {
    try {
        const { data, error } = await supabase.from('fang_data').select('id', { count: 'exact' });

        if (error) throw error;

        const { data: withGcj, error: gcjError } = await supabase.from('fang_data').select('id', { count: 'exact' }).not('gcj', 'is', null);

        if (gcjError) throw gcjError;

        const total = data.length;
        const completed = withGcj.length;
        const remaining = total - completed;

        console.log('\n=== 处理进度 ===');
        console.log(`总数据量: ${total}`);
        console.log(`已完成: ${completed}`);
        console.log(`剩余: ${remaining}`);
        console.log(`完成率: ${((completed / total) * 100).toFixed(2)}%`);

        return { total, completed, remaining };
    } catch (error) {
        console.error('检查进度失败:', error);
    }
}

// 主函数
async function main() {
    console.log('房产数据批量地理编码工具');
    console.log('==============================');

    // 检查初始进度
    await checkProgress();

    // 开始批量处理
    await batchGeocodeFangData();

    // 检查最终进度
    await checkProgress();
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}

export { batchGeocodeFangData, checkProgress };
