CREATE TABLE public.amap_matrix (
  id text NOT NULL, -- 矩阵标记唯一标识符 (格式: matrix_x_y)
  create_time timestamp with time zone NULL DEFAULT now(), -- 创建时间
  update_time timestamp with time zone NULL DEFAULT now(), -- 更新时间
  custom_gcj jsonb NOT NULL, -- 自定义位置坐标 {lng, lat}
  gps jsonb NOT NULL, -- GPS位置坐标 {lng, lat}
  gcj jsonb NOT NULL, -- GCJ-02位置坐标 {lng, lat}
  grid_index jsonb NOT NULL, -- 网格索引 {x, y}
  type text NOT NULL DEFAULT 'matrix', -- 标记类型 (固定为 'matrix')
  status text NOT NULL DEFAULT 'planned', -- 状态 ('planned' | 'captured')
  grid_size integer NOT NULL DEFAULT 1000, -- 网格大小（米）
  boundary_region text NULL DEFAULT 'wuhan_3rd_ring', -- 所属区域
  metadata jsonb NULL, -- 额外元数据
  captured_time timestamp with time zone NULL, -- 实际拍摄时间
  pano_url text NULL, -- 关联的全景图片URL地址
  notes text NULL, -- 备注信息
  CONSTRAINT amap_matrix_pkey PRIMARY KEY (id), -- 主键约束
  CONSTRAINT amap_matrix_status_check CHECK (status IN ('planned', 'captured')), -- 状态约束
  CONSTRAINT amap_matrix_type_check CHECK (type = 'matrix') -- 类型约束
) TABLESPACE pg_default;

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_amap_matrix_create_time ON public.amap_matrix USING btree (create_time) TABLESPACE pg_default; -- 创建时间索引
CREATE INDEX IF NOT EXISTS idx_amap_matrix_status ON public.amap_matrix USING btree (status) TABLESPACE pg_default; -- 状态索引
CREATE INDEX IF NOT EXISTS idx_amap_matrix_grid_index ON public.amap_matrix USING gin (grid_index) TABLESPACE pg_default; -- 网格索引（GIN索引用于JSONB）
CREATE INDEX IF NOT EXISTS idx_amap_matrix_gcj ON public.amap_matrix USING gin (gcj) TABLESPACE pg_default; -- GCJ-02位置坐标（GIN索引用于JSONB空间查询）
CREATE INDEX IF NOT EXISTS idx_amap_matrix_boundary_region ON public.amap_matrix USING btree (boundary_region) TABLESPACE pg_default; -- 区域索引
CREATE INDEX IF NOT EXISTS idx_amap_matrix_grid_size ON public.amap_matrix USING btree (grid_size) TABLESPACE pg_default; -- 网格大小索引

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_amap_matrix_update_time()
RETURNS TRIGGER AS $$
BEGIN
  NEW.update_time = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER trigger_update_amap_matrix_update_time
  BEFORE UPDATE ON public.amap_matrix
  FOR EACH ROW
  EXECUTE FUNCTION update_amap_matrix_update_time();