-- 创建 amap_page 表
CREATE TABLE amap_page (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    create_time TIMESTAMPTZ DEFAULT NOW(),
    gps JSONB,
    gcj JSONB,
    custom_gcj JSONB,
    name TEXT,
    altitude NUMERIC,
    city TEXT,
    full_address TEXT,
    tag TEXT,
    collection_id UUID,
    icon TEXT,
    link TEXT,
    page_url TEXT,
    first_image_url TEXT,
    thumbnail_url TEXT,
    page_content TEXT,
    page_markers_ids TEXT
);

-- 添加注释
COMMENT ON TABLE amap_page IS '高德地图页面数据表';
COMMENT ON COLUMN amap_page.id IS '唯一标识符';
COMMENT ON COLUMN amap_page.create_time IS '创建时间';
COMMENT ON COLUMN amap_page.gps IS '原始GPS坐标 (WGS84)，格式为 {"lng": 经度, "lat": 纬度}';
COMMENT ON COLUMN amap_page.gcj IS '转换后的GPS坐标 (GCJ02)，格式为 {"lng": 经度, "lat": 纬度}';
COMMENT ON COLUMN amap_page.custom_gcj IS '用户自定义GPS坐标, 格式为 {"lng": 经度, "lat": 纬度}';
COMMENT ON COLUMN amap_page.name IS '页面名称';
COMMENT ON COLUMN amap_page.altitude IS '海拔高度';
COMMENT ON COLUMN amap_page.city IS '城市名称';
COMMENT ON COLUMN amap_page.full_address IS '完整地址';
COMMENT ON COLUMN amap_page.tag IS '页面标签';
COMMENT ON COLUMN amap_page.collection_id IS '收藏集ID';
COMMENT ON COLUMN amap_page.icon IS '图标URL或标识符';
COMMENT ON COLUMN amap_page.link IS '关联链接';
COMMENT ON COLUMN amap_page.page_url IS '页面URL';
COMMENT ON COLUMN amap_page.first_image_url IS '第一张图片URL';
COMMENT ON COLUMN amap_page.thumbnail_url IS '缩略图URL';
COMMENT ON COLUMN amap_page.page_content IS '页面内容';
COMMENT ON COLUMN amap_page.page_markers_ids IS '页面中所有 marker 的 id';

-- 创建索引
CREATE INDEX idx_amap_page_create_time ON amap_page(create_time);
CREATE INDEX idx_amap_page_city ON amap_page(city);
CREATE INDEX idx_amap_page_collection_id ON amap_page(collection_id);
CREATE INDEX idx_amap_page_tag ON amap_page(tag);
CREATE INDEX idx_amap_page_page_url ON amap_page(page_url);
