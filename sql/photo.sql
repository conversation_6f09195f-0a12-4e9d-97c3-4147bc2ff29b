CREATE TABLE public.amap_photo (
  id uuid NOT NULL DEFAULT extensions.uuid_generate_v4(), -- 唯一标识符
  create_time timestamp with time zone NULL DEFAULT now(), -- 创建时间
  gps jsonb NULL, -- GPS 数据
  gcj jsonb NULL, -- GCJ 数据
  name text NULL, -- 照片名称
  gps_timestamp timestamp without time zone NULL, -- GPS 时间戳
  altitude numeric NOT NULL, -- 海拔高度
  city text NULL, -- 城市名称
  full_address text NULL, -- 完整地址
  photo_url text NULL, -- 照片 URL
  thumbnail_url text NULL, -- 缩略图 URL
  custom_gcj jsonb NULL, -- 自定义 GCJ 数据
  tag text NULL, -- 标签
  collection_id text NULL, -- 集合 ID
  link text NULL, -- 相关链接
  is_custom boolean NULL DEFAULT false, -- 是否为自定义照片
  custom_address text NULL, -- 自定义地址
  CONSTRAINT amap_marker_images_pkey PRIMARY KEY (id) -- 主键约束
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_amap_photo_create_time ON public.amap_photo USING btree (create_time) TABLESPACE pg_default; -- 创建时间索引
CREATE INDEX IF NOT EXISTS idx_amap_photo_city ON public.amap_photo USING btree (city) TABLESPACE pg_default; -- 城市索引
CREATE INDEX IF NOT EXISTS idx_amap_photo_gps_timestamp ON public.amap_photo USING btree (gps_timestamp) TABLESPACE pg_default; -- GPS 时间戳索引
CREATE INDEX IF NOT EXISTS idx_amap_photo_tag ON public.amap_photo USING btree (tag) TABLESPACE pg_default; -- 标签索引
CREATE INDEX IF NOT EXISTS idx_amap_photo_name ON public.amap_photo USING btree (name) TABLESPACE pg_default; -- 照片名称索引