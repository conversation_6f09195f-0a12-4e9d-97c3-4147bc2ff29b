CREATE TABLE public.amap_page (
  id uuid NOT NULL DEFAULT extensions.uuid_generate_v4(), -- 唯一标识符
  create_time timestamp with time zone NULL DEFAULT now(), -- 创建时间
  gps jsonb NULL, -- GPS 数据
  gcj jsonb NULL, -- GCJ 数据
  custom_gcj jsonb NULL, -- 自定义 GCJ 数据
  name text NULL, -- 名称
  altitude numeric NULL, -- 海拔高度
  city text NULL, -- 城市名称
  full_address text NULL, -- 完整地址
  tag text NULL, -- 标签
  collection_id uuid NULL, -- 集合 ID
  icon text NULL, -- 图标
  link text NULL, -- 相关链接
  page_url text NULL, -- 页面 URL
  first_image_url text NULL, -- 第一张图片 URL
  thumbnail_url text NULL, -- 缩略图 URL
  page_content text NULL, -- 页面内容
  page_markers_ids text NULL, -- 页面标记 ID
  CONSTRAINT amap_page_pkey PRIMARY KEY (id) -- 主键约束
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_amap_page_create_time ON public.amap_page USING btree (create_time) TABLESPACE pg_default; -- 创建时间索引
CREATE INDEX IF NOT EXISTS idx_amap_page_city ON public.amap_page USING btree (city) TABLESPACE pg_default; -- 城市索引
CREATE INDEX IF NOT EXISTS idx_amap_page_collection_id ON public.amap_page USING btree (collection_id) TABLESPACE pg_default; -- 集合 ID 索引
CREATE INDEX IF NOT EXISTS idx_amap_page_tag ON public.amap_page USING btree (tag) TABLESPACE pg_default; -- 标签索引
CREATE INDEX IF NOT EXISTS idx_amap_page_page_url ON public.amap_page USING btree (page_url) TABLESPACE pg_default; -- 页面 URL 索引
CREATE INDEX IF NOT EXISTS idx_amap_page_name ON public.amap_page USING btree (name) TABLESPACE pg_default; -- 名称索引