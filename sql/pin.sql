CREATE TABLE public.amap_pin (
  id uuid NOT NULL DEFAULT extensions.uuid_generate_v4(),
  create_time timestamp with time zone NULL DEFAULT now(),
  gps jsonb NULL,
  gcj jsonb NULL,
  custom_gcj jsonb NULL,
  name text NULL,
  altitude numeric NULL,
  city text NULL,
  full_address text NULL,
  tag text NULL,
  collection_id uuid NULL,
  icon text NULL,
  link text NULL,
  CONSTRAINT amap_marker_pins_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_amap_marker_pins_city ON public.amap_pin USING btree (city) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_amap_marker_pins_collection_id ON public.amap_pin USING btree (collection_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_amap_marker_pins_created_at ON public.amap_pin USING btree (create_time) TABLESPACE pg_default;