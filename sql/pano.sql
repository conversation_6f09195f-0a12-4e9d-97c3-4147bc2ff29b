CREATE TABLE public.amap_pano (
  id uuid NOT NULL DEFAULT extensions.uuid_generate_v4(), -- 唯一标识符
  create_time timestamp with time zone NULL DEFAULT now(), -- 创建时间
  gps jsonb NULL, -- GPS 数据
  gcj jsonb NULL, -- GCJ 数据
  custom_gcj jsonb NULL, -- 自定义 GCJ 数据
  name text NULL, -- 名称
  altitude numeric NULL, -- 海拔高度
  city text NULL, -- 城市名称
  full_address text NULL, -- 完整地址
  tag text NULL, -- 标签
  collection_id text NULL, -- 集合 ID
  config_json jsonb NULL, -- 配置 JSON 数据
  img_url text NULL, -- 图片 URL
  config_file_url text NULL, -- 配置文件 URL
  type public.panotype NULL DEFAULT 'equirectangular'::panotype, -- 类型
  thumbnail_url text NULL, -- 缩略图 URL
  gps_timestamp timestamp without time zone NULL, -- GPS 时间戳
  link text NULL, -- 相关链接
  CONSTRAINT amap_marker_panos_pkey PRIMARY KEY (id) -- 主键约束
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_amap_pano_create_time ON public.amap_pano USING btree (create_time) TABLESPACE pg_default; -- 创建时间索引
CREATE INDEX IF NOT EXISTS idx_amap_pano_city ON public.amap_pano USING btree (city) TABLESPACE pg_default; -- 城市索引
CREATE INDEX IF NOT EXISTS idx_amap_pano_tag ON public.amap_pano USING btree (tag) TABLESPACE pg_default; -- 标签索引
CREATE INDEX IF NOT EXISTS idx_amap_pano_collection_id ON public.amap_pano USING btree (collection_id) TABLESPACE pg_default; -- 集合 ID 索引
CREATE INDEX IF NOT EXISTS idx_amap_pano_name ON public.amap_pano USING btree (name) TABLESPACE pg_default; -- 名称索引